---
Updated: 2023-03-10T19:54
tags:
  - AI->-Chatbot
  - AI->-Prompt
Created: 2023-03-10T16:24
---
![[f2d4549e683d47dca1a18b4d556a08ec.png]]
本站提供精煉過的指令語句，讓你充分發揮 ChatGPT 的強大功能
全部
寫報告
資料整理
履歷與自傳
準備面試
程式開發
知識學習
英語學習
工作生產力
寫作幫手
日常生活
有趣好玩
角色扮演
寫報告
複製
### 報告開頭
我現在正在 報告的情境與目的。我的簡報主題是 主題，請提供 數字 種開頭方式，要簡單到 目標族群 能聽懂，同時要足夠能吸引人，讓他們願意專心聽下去
👉 我現在正在修台大的簡報課，其中一項作業是要做一份讓小學生能聽懂的簡報。我的簡報主題是機會成本，請提供三種開頭方式，要簡單到小學生能聽懂，同時要足夠能吸引人，讓他們願意專心聽下去
複製
### 研究報告
寫出一篇有關 知識 的 數字 字研究報告，報告中需引述最新的研究，並引用專家觀點
👉 寫出一篇有關自動駕駛的 300 字研究報告，報告中需引述最新的研究，並引用專家觀點
複製
### 提出反對觀點
你是 某個主題 的專家，請針對以下論述 附上論述，提出 數字 個反駁的論點，每個論點都要有佐證
👉 你是大數據分析的專家，請針對以下論述「在數據分析中，越多數據越好」，提出 3 個反駁的論點，每個論點都要有佐證
複製
### 報告總結
你是 某個主題 的專家，請總結以下內容，並針對以下內容提出未來能進一步研究的方向 附上內容
👉 你是金融科技專家，請總結以下內容，並針對以下內容提出未來能進一步研究的方向 [附上內容]
資料整理
複製
### 蒐集資料
給我 數字 篇，有關 領域 的文章。
👉 給我 5 篇，有關 SEO 的文章。
複製
### 內容總結
用列點的方式總結出這篇文章的 數字 個重點：附上文章內容/附上文章網址。
👉 用列點的方式總結出這篇文章的 5 個重點：[附上文章內容/附上文章網址]。
複製
### 摘錄某領域重點
用列點的方式總結出 數字 個 領域 知識重點
👉 用列點的方式總結出 10 個量子力學知識重點。
履歷與自傳
複製
### 尋求履歷的反饋
這份 職位 的履歷，有哪邊可以寫更好? 請以專業面試官的角度，提出具體改進建議。接著以你提出的建議來改寫這段經歷，改寫時請維持列點的形式。 附上履歷
👉 這份 UIUX 設計師的履歷，有哪邊可以寫更好? 請以專業面試官的角度，提出具體改進建議。接著以你提出的建議來改寫這段經歷，改寫時請維持列點的形式。
複製
### 為履歷加上量化數據
改寫以下履歷，為每一點加上量化的數據，改寫時請維持列點的形式。附上履歷
複製
### 把經歷修精簡
把這段經歷寫得更精簡一點，讓別人可以馬上看到重點，同時維持生動的描述。附上經歷
複製
### 為不同公司客製化撰寫履歷
我今天要申請 公司 的 職位，改寫以下經歷，讓我能更符合 公司 的企業文化。附上經歷
👉 我今天要申請 Google 的前端工程師，改寫以下經歷，讓我能更符合 Google 的企業文化。[附上經歷]
準備面試
複製
### 彙整面試題目
你現在是 公司 的 職位 面試官，請分享在 職位 面試時最常會問的 數字 個問題。
👉 你現在是 Google 的產品經理面試官，請分享在 Google 產品經理面試時最常會問的 5 個問題。
複製
### 給予回饋
我針對 問題 的回答，有哪些可以改進的地方? 附上回答
👉 我針對「你會如何排定不同產品功能優先順序?」的回答，有哪些可以改進的地方? [附上回答]
複製
### 提供追問的問題
針對 問題 這個面試問題，請提供一些常見的追問面試題。
👉 針對「你會如何排定不同產品功能優先順序?」這個面試問題，請提供一些常見的追問面試題。
複製
### 用 STAR 原則回答面試問題
我在準備 問題 這個面試問題，請用 STAR 原則幫我回答這個問題。針對這個問題，我有的經歷如下 附上經歷。
👉 我在準備「請分享一個你在急迫的期限中完成專案的經驗」這個面試問題，請用 STAR 原則幫我回答這個問題。針對這個問題，我有的經歷如下 [附上經歷]。
程式開發
複製
### 寫程式
你現在是一個 程式語言 專家，請幫我用 程式語言 寫一個函式，它需要做到 某個功能
👉 你現在是一個 JavaScript 專家，請幫我用 JavaScript 寫一個函式，它需要做到 輸入一個一維陣列，把這個一維陣列轉換成二維陣列。同時我要能夠自由地決定二維陣列中的子陣列長度是多少
複製
### 解讀程式碼
你現在是一個 程式語言 專家，請告訴我以下的程式碼在做什麼。 附上程式碼
複製
### 重構程式碼
你現在是一個 Clean Code 專家，我有以下的程式碼，請用更乾淨簡潔的方式改寫，讓我的同事們可以更容易維護程式碼。另外，也解釋為什麼你要這樣重構，讓我能把重構的方式的說明加到 Pull Request 當中。 附上程式碼
複製
### 解 bug
你現在是一個 程式語言 專家，我有一段程式碼，我預期這段程式碼可以 做到某個功能，只是它通過不了 測試案例 這個測試案例。請幫我找出我哪裡寫錯了，以及用正確的方式改寫。附上程式碼
👉 你現在是一個 python 專家，我有一段程式碼，我預期這段程式碼可以判斷一個字串是不是鏡像迴文，只是它通過不了 aacdeedcc 這個測試案例。請幫我找出我哪裡寫錯了，以及用正確的方式改寫。[附上程式碼]
複製
### 寫測試
你現在是一個 程式語言 專家，我有一段程式碼 附上程式碼，請幫我寫一個測試，請至少提供五個測試案例，同時要包含到極端的狀況，讓我能夠確定這段程式碼的輸出是正確的。
複製
### 寫 Regex
你現在是一個 Regex 專家，請幫我寫一個 Regex ，它能夠把 需求
👉 你現在是一個 Regex 專家，請幫我寫一個 Regex ，它能夠把輸入一個字串，把這個字串中的所有數字都取出來
知識學習
複製
### 概念解說
詳細的說明 填入想了解的知識
👉 詳細的說明如何製造一台電腦
複製
### 簡易教學
你扮演 科目老師 的角色， 我需要理解 理論。 請用 方式 方式描述。
👉 你扮演數學老師的角色， 我需要理解一元二次方程式。 請用淺顯易懂方式描述。
複製
### 深度教學
你是一個 SEO 專家，你要教我深度的 SEO 知識
👉 你是一個 SEO 專家，你要教我深度的 SEO 知識。
複製
### 教學與測驗
教我 二次方程式，最後給我一個測驗
👉 教我 一元二次方程式，最後給我一個測驗
英語學習
複製
### 背單字法寶
用 中文/英文 解釋以下英文單字：填入一個或多個單字。請用表格的方式呈現，並且表格內須包含單字、詞性、解釋與例句。
👉 用中文解釋以下英文單字：apple, orange, doctor, car, run。請用表格的方式呈現，並且表格內須包含單字、詞性、解釋與例句。
複製
### 英語單字學習
解釋英文單字 英文單字，並且給我 數字 個常用句子。
👉 解釋英文單字 divest，並且給我 5 個常用句子。
複製
### 英語對話
Can we have a conversation about 話題?
👉 Can we have a conversation about machine learning?
複製
### 校閱英文文法
Can you check the spelling and grammar in the following text? 附上英文文字
複製
### 英文作文修改與解釋
校閱以下英文文章，並用表格的方式呈現，要有三個欄位，分別是原文、修正後的版本，以及用中文詳解為什麼要這樣修改：附上英文文章
複製
### 糾正文法和拼字錯誤
Please correct my grammar and spelling mistakes in the text above: 附上英文文字
👉 Please correct my grammar and spelling mistakes in the text above: I love eat fooded
工作生產力
複製
### 回覆 Email
你是一名 職業，我會給你一封電子郵件，你要回覆這封電子郵件。電子郵件：附上內容
👉 你是一名產品經理，我會給你一封電子郵件，你要回覆這封電子郵件。電子郵件：[附上內容]
寫作幫手
複製
### 撰寫標題
寫出 數字 個有關 主題 的 社群平台 風格標題，要遵守以下規則：規則 1、規則 2、其他規則。
👉 寫出 5 個有關日本迪士尼旅遊心得的 Instagram 風格標題，要遵守以下規則：標題不超過 20 字、標題要加上適當表情符號。
複製
### 撰寫文章大綱
提供 某主題 主題的文章大綱
👉 提供美國留學主題的文章大綱
複製
### 文章撰寫
針對 主題 這個主題生成一篇文章
👉 針對使用 ChatGPT 小訣竅這個主題生成一篇文章
複製
### 產品文案
將以下產品關鍵字生成 數字 句的產品文案。產品關鍵字：附上關鍵字...
👉 將以下產品關鍵字生成 10 句的產品文案。產品關鍵字：球鞋、春季最新款、多種顏色、適合慢跑
日常生活
複製
### 食譜生成
提供給我一個食譜，食材包含 食材 1、食材 2、食材...。
👉 提供給我一個食譜，食材包含雞腿肉、雞蛋、起司。
複製
### 提供食譜
請列出這份食譜的採買清單和步驟：數字 人份的 食譜。
👉 請列出這份食譜的採買清單和步驟：1 人份的蕃茄炒蛋。
複製
### 活動計劃清單
你扮演一位專業的活動企劃，請生成 活動 活動計劃清單，包括重要任務和截止日期。
👉 你扮演一位專業的活動企劃，請生成運動會活動計劃清單，包括重要任務和截止日期。
複製
### 提供點子
提供 數字 個 想法 的點子
👉 提供 5 個情人節的點子
複製
### 旅遊計畫
生成一份 數字 天的 地點 旅遊計畫，交通工具是 交通工具...。要遵守以下規則：填入規則
👉 生成一份 5 天的東京旅遊計畫，交通工具是地鐵和火車。要遵守以下規則：1. 地點要包含東京鐵塔、富士山、迪士尼樂園 2. 需要包含交通如何乘坐 3. 一天不超過 3 個地點。
有趣好玩
複製
### 寫歌詞
大家都說我寫的歌詞像 人名，但我最近有點沒靈感，請幫我用 人名 的風格寫一首歌。歌中包含的元素要 關鍵字...。
👉 大家都說我寫的歌詞像方文山，但我最近有點沒靈感，請幫我用方文山的風格寫一首歌。歌中包含的元素要有別離、思念、峰迴路轉。
複製
### 寫故事
寫出一篇有關 故事想法，擁有 風格 風格的短篇故事
👉 寫出一篇有關工程師拯救這個世界的短篇故事
複製
### 寫 rap
你是現在最紅的饒舌歌手，請創作一首 Rap，主題是 附上主題。
👉 你是現在最紅的饒舌歌手，請創作一首 Rap，主題是孤勇者。
角色扮演
複製
### 綜合情境
你現在是一名 角色，你要針對我提出的問題提供建議。我的問題是：附上問題。
👉 你現在是一名生涯教練，你要針對我提出的問題提供建議。我的問題是：我是否要出國唸書？
複製
### 面試官
你現在是一個 職位 面試官，而我是要應徵 職位 的面試者。你需要遵守以下規則：1. 你只能問我有關 職位 的面試問題。2. 不需要寫解釋。3. 你需要向面試官一樣等我回答問題，再提問下一個問題。我的第一句話是，你好。
👉 你現在是一個產品經理面試官，而我是要應徵產品經理的面試者。你需要遵守以下規則：1. 你只能問我有關產品經理的面試問題。2. 不需要寫解釋。3. 你需要向面試官一樣等我回答問題，再提問下一個問題。我的第一句話是，你好。
你是一位導遊，我會把我旅遊的位置給你，你要推薦一個靠近我位置的地方。在某些情況下，我還會告訴您我想旅遊地點的類型。你還會向我推薦靠近我的第一個位置的類似類型的地方。我的第一個需求是填入需求