---
Updated: 2024-03-26T20:14
tags:
  - AI->-MoE
  - AI->-Theory
Created: 2024-03-26T20:11
---
[![](https://arxiv.org/static/browse/0.3.4/images/arxiv-logo-fb.png)](https://arxiv.org/static/browse/0.3.4/images/arxiv-logo-fb.png)
==Download a PDF of the paper titled Evolutionary Optimization of Model Merging Recipes, by <PERSON><PERSON><PERSON> and 4 other authors==
[==Download PDF==](https://arxiv.org/pdf/2403.13187) [==HTML (experimental)==](https://arxiv.org/html/2403.13187v1)
==Abstract:We present a novel application of evolutionary algorithms to automate the creation of powerful foundation models. While model merging has emerged as a promising approach for LLM development due to its cost-effectiveness, it currently relies on human intuition and domain knowledge, limiting its potential. Here, we propose an evolutionary approach that overcomes this limitation by automatically discovering effective combinations of diverse open-source models, harnessing their collective intelligence without requiring extensive additional training data or compute. Our approach operates in both parameter space and data flow space, allowing for optimization beyond just the weights of the individual models. This approach even facilitates cross-domain merging, generating models like a Japanese LLM with Math reasoning capabilities. Surprisingly, our Japanese Math LLM achieved state-of-the-art performance on a variety of established Japanese LLM benchmarks, even surpassing models with significantly more parameters, despite not being explicitly trained for such tasks. Furthermore, a culturally-aware Japanese VLM generated through our approach demonstrates its effectiveness in describing Japanese culture-specific content, outperforming previous Japanese VLMs. This work not only contributes new state-of-the-art models back to the open-source community, but also introduces a new paradigm for automated model composition, paving the way for exploring alternative, efficient approaches to foundation model development.==
## ==Submission history==
==From: David Ha [==[==view email==](https://arxiv.org/show-email/f141b966/2403.13187)==]====  
  
====**[v1]**== ==Tue, 19 Mar 2024 22:56:53 UTC (1,162 KB)==