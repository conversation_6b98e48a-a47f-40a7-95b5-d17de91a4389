---
DocFlag:
  - Reference
Updated: 2024-04-06T09:16
tags:
  - AI->-Programming
  - AI->-Theory
Created: 2024-04-06T09:14
---
[![](https://miro.medium.com/v2/da:true/resize:fit:1200/0*JCp7tL8hWXv8EQvi)](https://miro.medium.com/v2/da:true/resize:fit:1200/0*JCp7tL8hWXv8EQvi)
## ==Dive into the techniques to fine-tune Neural Networks, understand their mathematics, build them from scratch, and explore their applications====  
  
====深入研究微调神经网络的技术，了解其数学原理，从零开始构建神经网络，并探索其应用领域==
==[==
[![](https://miro.medium.com/v2/da:true/resize:fill:88:88/0*vXUpsKuv7A7DlqP0)](https://miro.medium.com/v2/da:true/resize:fill:88:88/0*vXUpsKuv7A7DlqP0)
==](https://medium.com/@cristianleo120?source=post_page-----8138d548da69--------------------------------)[==
[![](https://miro.medium.com/v2/resize:fill:48:48/1*CJe3891yB1A1mzMdqemkdg.jpeg)](https://miro.medium.com/v2/resize:fill:48:48/1*CJe3891yB1A1mzMdqemkdg.jpeg)
==](https://towardsdatascience.com/?source=post_page-----8138d548da69--------------------------------)==
[![](https://miro.medium.com/v2/resize:fit:700/0*JCp7tL8hWXv8EQvi)](https://miro.medium.com/v2/resize:fit:700/0*JCp7tL8hWXv8EQvi)
==Image by DALL-E 图片来自 DALL-E==
==While you might get by in machine learning by trying out a few models, picking the best performer, and tweaking some settings, deep learning doesn’t play by the same rules. If you’ve ever experimented with Neural Networks, you might’ve noticed their performance can be pretty hit or miss. You might even have seen something as straightforward as Logistic Regression beat your fancy 200-layer Deep Neural Network.====  
  
====在机器学习中，你可能会尝试几个模型，选出性能最好的，然后调整一些设置，但深度学习的规则并不一样。如果你曾经尝试过神经网络，你可能会注意到它们的性能可能会很不稳定。你甚至可能见过像逻辑回归这样简单的东西打败你花哨的 200 层深度神经网络。==
==Why does this happen? Deep learning is among the most advanced AI techniques we have, but it demands a solid understanding and careful handling. Knowing how to fine-tune a Neural Network, getting what’s going on inside it, and mastering its use are crucial. That’s what we’re diving into today!====  
  
====为什么会出现这种情况？深度学习是我们拥有的最先进的人工智能技术之一，但它需要扎实的理解和谨慎的处理。知道如何微调神经网络、了解它的内部结构并掌握其使用方法至关重要。这就是我们今天要深入学习的内容！==
==Before we jump into the article, I suggest you pull up this Jupyter Notebook. It’s got all the code we’ll be covering today, so having it handy will make it easier to follow along:====  
  
====在开始阅读文章之前，我建议你先打开这本 Jupyter Notebook。它包含了我们今天要讨论的所有代码，因此如果能随手拿来，会更容易理解：==
## ==1: Introduction 1: 引言==
## ==1.1: Elevating Our Basic Neural Network====  
  
====1.1:提升我们的基本神经网络==
==In our last dive into artificial intelligence, we built a neural network from the ground up. This basic model opened up the world of neural networks to us — the core of today’s AI tech. We covered the essentials: how input, hidden, and output layers, along with activation functions, come together to process info and make predictions. Then, we put theory into practice with a simple neural network trained on a digits dataset for a computer vision task.====  
  
====在上一次深入研究人工智能时，我们从头开始构建了一个神经网络。这个基本模型为我们打开了神经网络的世界--当今人工智能技术的核心。我们介绍了基本要素：输入层、隐藏层和输出层如何与激活函数一起处理信息并进行预测。然后，我们将理论付诸实践，在计算机视觉任务的数字数据集上训练了一个简单的神经网络。==
==Now, we’re going to build on that foundation. We’ll introduce more complexity by adding layers and exploring various techniques for initialization, regularization, and optimization. And, of course, we’ll put our code to the test to see how these tweaks impact our Neural Network’s performance.====  
  
====现在，我们将在此基础上继续前进。我们将通过增加层数和探索各种初始化、正则化和优化技术来提高复杂性。当然，我们还要对代码进行测试，看看这些调整对神经网络性能的影响。==
==If you haven’t checked out my previous article where we built a neural network from scratch, I recommend giving it a read. We’ll be building on that work, and I’ll assume you’re already familiar with the concepts we covered.====  
  
====如果你还没看过我之前的文章，我建议你读一读，在那篇文章中，我们从零开始构建了一个神经网络。我们将在这篇文章的基础上继续学习，我假设你已经熟悉了我们所涉及的概念。==
## ==1.2: The Path to Complexity====  
  
====1.2: 通往复杂性之路==
==Transforming a neural network from a basic setup to a more sophisticated one isn’t just about piling on more layers or nodes. It’s a delicate dance of fine-tuning that requires a solid grasp of the network’s structure and the nuances of the data it handles. As we dive deeper, our goal becomes to enrich our neural network’s depth, layering in more complexity to better discern intricate patterns and connections in the data.====  
  
====将神经网络从基本设置转变为更复杂的设置，并不是简单地增加层数或节点。这是一场微调的精妙舞蹈，需要扎实掌握网络的结构及其处理数据的细微差别。当我们深入研究时，我们的目标就变成了丰富神经网络的深度，层层叠加更多的复杂性，以更好地辨别数据中错综复杂的模式和连接。==
==However, beefing up complexity isn’t without its hurdles. With each new layer we introduce, the necessity for refined optimization techniques grows. These are crucial not just for effective learning but also for the model’s ability to adapt to new, unseen data. This guide will walk you through beefing up our foundational neural network. We’ll dive into sophisticated strategies to fine-tune our network, including tweaks to learning rates, adopting early stopping, and playing around with various optimization algorithms like SGD (Stochastic Gradient Descent) and Adam.====  
  
====然而，提高复杂性并非没有障碍。我们每引入一个新层，对精细优化技术的需求就会增加。这不仅对有效学习至关重要，对模型适应新的未知数据的能力也至关重要。本指南将引导您强化我们的基础神经网络。我们将深入探讨对网络进行微调的复杂策略，包括调整学习率、采用提前停止以及使用 SGD（随机梯度下降）和 Adam 等各种优化算法。==
==We’re also going to cover the significance of how we kick things off with initialization methods, the advantages of using dropout to dodge overfitting, and why keeping our network’s gradients in check with clipping and normalization matters so much for stability. Plus, we’ll tackle the challenge of figuring out the best number of layers to add — enough to enhance learning but not so many that we tip into unnecessary complexity.====  
  
====我们还将介绍初始化方法的重要意义、使用 dropout 来避免过拟合的优势，以及为什么使用削波和归一化来控制网络梯度对稳定性非常重要。此外，我们还将解决如何确定添加层的最佳数量这一难题--既要足够多，以提高学习效率，又不能太多，以免造成不必要的复杂性。==
==Below is the Neural Network and Trainer class we put together in our last article. We’re going to tweak it and practically explore how each modification affects our model’s performance:====  
  
====以下是我们在上一篇文章中创建的神经网络和训练器类。我们将对其进行调整，并实际探索每次修改对模型性能的影响：==
==class NeuralNetwork:====  
  
====def __init__(self, input_size, hidden_size, output_size, loss_func='mse'):====  
  
====self.input_size = input_size====  
  
====self.hidden_size = hidden_size====  
  
====self.output_size = output_size====  
  
====self.loss_func = loss_func==
```plain
    self.weights1 = np.random.randn(self.input\_size, self.hidden\_size)  
    self.bias1 = np.zeros((1, self.hidden\_size))  
    self.weights2 = np.random.randn(self.hidden\_size, self.output\_size)  
    self.bias2 = np.zeros((1, self.output\_size))
      
    self.train\_loss = \[\]  
    self.test\_loss = \[\]
def \_\_str\_\_(self):  
    return f"Neural Network Layout:\\nInput Layer: {self.input\_size} neurons\\nHidden Layer: {self.hidden\_size} neurons\\nOutput Layer: {self.output\_size} neurons\\nLoss Function: {self.loss\_func}"
        def forward(self, X):  
      
    self.z1 = np.dot(X, self.weights1) + self.bias1  
    self.a1 = self.sigmoid(self.z1)  
    self.z2 = np.dot(self.a1, self.weights2) + self.bias2  
    if self.loss\_func == 'categorical\_crossentropy':  
        self.a2 = self.softmax(self.z2)  
    else:  
        self.a2 = self.sigmoid(self.z2)  
    return self.a2
    def backward(self, X, y, learning\_rate):  
      
    m = X.shape\[0\]
              
    if self.loss\_func == 'mse':  
        self.dz2 = self.a2 - y  
    elif self.loss\_func == 'log\_loss':  
        self.dz2 = -(y/self.a2 - (1\-y)/(1\-self.a2))  
    elif self.loss\_func == 'categorical\_crossentropy':  
        self.dz2 = self.a2 - y  
    else:  
        raise ValueError('Invalid loss function')
            self.dw2 = (1 / m) \* np.dot(self.a1.T, self.dz2)  
    self.db2 = (1 / m) \* np.sum(self.dz2, axis=0, keepdims=True)  
    self.dz1 = np.dot(self.dz2, self.weights2.T) \* self.sigmoid\_derivative(self.a1)  
    self.dw1 = (1 / m) \* np.dot(X.T, self.dz1)  
    self.db1 = (1 / m) \* np.sum(self.dz1, axis=0, keepdims=True)
              
    self.weights2 -= learning\_rate \* self.dw2  
    self.bias2 -= learning\_rate \* self.db2  
    self.weights1 -= learning\_rate \* self.dw1  
    self.bias1 -= learning\_rate \* self.db1
        def sigmoid(self, x):  
    return 1 / (1 + np.exp(-x))
    def sigmoid\_derivative(self, x):  
    return x \* (1 - x)
    def softmax(self, x):  
    exps = np.exp(x - np.max(x, axis=1, keepdims=True))  
    return exps/np.sum(exps, axis=1, keepdims=True)
```
==class Trainer:====  
  
====def __init__(self, model, loss_func='mse'):====  
  
====self.model = model====  
  
====self.loss_func = loss_func====  
  
====self.train_loss = []====  
  
====self.val_loss = []==
```plain
def calculate\_loss(self, y\_true, y\_pred):  
    if self.loss\_func == 'mse':  
        return np.mean((y\_pred - y\_true)\*\*2)  
    elif self.loss\_func == 'log\_loss':  
        return -np.mean(y\_true\*np.log(y\_pred) + (1\-y\_true)\*np.log(1\-y\_pred))  
    elif self.loss\_func == 'categorical\_crossentropy':  
        return -np.mean(y\_true\*np.log(y\_pred))  
    else:  
        raise ValueError('Invalid loss function')
def train(self, X\_train, y\_train, X\_test, y\_test, epochs, learning\_rate):  
    for \_ in range(epochs):  
        self.model.forward(X\_train)  
        self.model.backward(X\_train, y\_train, learning\_rate)  
        train\_loss = self.calculate\_loss(y\_train, self.model.a2)  
        self.train\_loss.append(train\_loss)
                    self.model.forward(X\_test)  
        test\_loss = self.calculate\_loss(y\_test, self.model.a2)  
        self.val\_loss.append(val\_loss)
```
## ==2: Expanding Model Complexity====  
  
====2: 扩展模型的复杂性==
==Diving deeper into refining neural networks, we hit upon a game-changing strategy: dialing up the complexity by layering on more levels. This move isn’t just about bulking up the model; it’s about sharpening its ability to grasp and interpret nuances in the data with greater sophistication.====  
  
====在深入研究完善神经网络的过程中，我们发现了一种改变游戏规则的策略：通过增加层次来提高复杂性。此举不仅是为了增加模型的体积，更是为了提高模型的能力，以更高的复杂性来把握和解释数据中的细微差别。==
## ==2.1: Adding More Layers 2.1:添加更多图层==
==**The Rationale Behind Increased Network Depth====  
  
====增加网络深度的理由====  
  
====**At the heart of deep learning is its knack for piecing together hierarchical data representations. By weaving in more layers, we’re essentially equipping our neural network with the tools to pick apart and understand patterns of growing intricacy. Think of it as teaching the network to start with recognizing simple forms and textures and gradually advancing to unravel more complex relationships and features in the data. This layered learning approach somewhat mirrors how humans make sense of information, evolving from basic understanding to complex interpretation.====  
  
====深度学习的核心在于其拼凑分层数据表示的诀窍。通过编织更多的层级，我们基本上为神经网络配备了各种工具，以剔除和理解日益复杂的模式。这就好比教会神经网络从识别简单的形式和纹理开始，逐步推进到揭示数据中更复杂的关系和特征。这种分层学习方法在一定程度上反映了人类是如何理解信息的，即从基本的理解发展到复杂的解释。==
==Piling on more layers boosts the network’s “learning capacity,” broadening its horizon to map out and digest a more extensive range of data relationships. This enables the handling of more elaborate tasks. But it’s not a free-for-all; adding layers willy-nilly without them meaningfully contributing to the model’s intelligence could muddy the learning process rather than clarify it.====  
  
====层数越多，网络的 "学习能力 "就越强，网络的视野也就越开阔，能够绘制和消化更广泛的数据关系。这样就能处理更复杂的任务。但这并不是无限制的，随意增加层数而不对模型的智能做出有意义的贡献，可能会使学习过程变得模糊而不是清晰。==
==**Guide to Integrating More Layers**====  
  
====**整合更多图层指南**==
==class NeuralNetwork:====  
  
====def __init__(self, layers, loss_func='mse'):====  
  
====self.layers = []====  
  
====self.loss_func = loss_func==
```plain
    for i in range(len(layers) - 1):  
        self.layers.append({  
            'weights': np.random.randn(layers\[i\], layers\[i + 1\]),  
            'biases': np.zeros((1, layers\[i + 1\]))  
        })
      
    self.train\_loss = \[\]  
    self.test\_loss = \[\]
def forward(self, X):  
    self.a = \[X\]  
    for layer in self.layers:  
        self.a.append(self.sigmoid(np.dot(self.a\[-1\], layer\['weights'\]) + layer\['biases'\]))  
    return self.a\[-1\]
def backward(self, X, y, learning\_rate):  
    m = X.shape\[0\]  
    self.dz = \[self.a\[-1\] - y\]
    for i in reversed(range(len(self.layers) - 1)):  
        self.dz.append(np.dot(self.dz\[-1\], self.layers\[i + 1\]\['weights'\].T) \* self.sigmoid\_derivative(self.a\[i + 1\]))
    self.dz = self.dz\[::-1\]
    for i in range(len(self.layers)):  
        self.layers\[i\]\['weights'\] -= learning\_rate \* np.dot(self.a\[i\].T, self.dz\[i\]) / m  
        self.layers\[i\]\['biases'\] -= learning\_rate \* np.sum(self.dz\[i\], axis=0, keepdims=True) / m
def sigmoid(self, x):  
    return 1 / (1 + np.exp(-x))
    def sigmoid\_derivative(self, x):  
    return x \* (1 - x)
```
==In this section, we’ve made some significant adjustments to how our neural network operates, aiming for a model that flexibly supports any number of layers. Here’s a breakdown of what’s changed:====  
  
====在本节中，我们对神经网络的运行方式做了一些重大调整，旨在建立一个可灵活支持任意层数的模型。以下是变化的细目：==
==First off, we’ve dropped the== ==`self.input`====,== ==`self.hidden`====, and== ==`self.output`== ==variables that previously defined the number of nodes in each layer. Our goal now is a versatile model that can manage an arbitrary number of layers. For instance, to replicate our prior model used on the digits dataset—which had 64 input nodes, 64 hidden nodes, and 10 output nodes—we could simply set it up like this:====  
  
====首先，我们放弃了之前定义每层节点数的== ==`self.input`== ==、== ==`self.hidden`== ==和== ==`self.output`== ==变量。我们现在的目标是建立一个可以管理任意层数的通用模型。例如，要复制我们之前在数字数据集上使用的模型--它有 64 个输入节点、64 个隐藏节点和 10 个输出节点--我们可以简单地这样设置它：==
==nn = NeuralNetwork(layers=[64, 64, 10])==
==You’ll notice that the code now loops over each layer three times, each for a different purpose:====  
  
====你会发现，现在代码在每个层上循环了三次，每次都有不同的目的：==
==During initialization, all weights and biases across every layer are set up. This step is crucial for preparing the network with the initial parameters it needs for the learning process.====  
  
====在初始化过程中，每一层的所有权重和偏置都要进行设置。这一步对于为网络准备学习过程所需的初始参数至关重要。==
==During the Forward pass, the activations== ==`self.a`== ==are collected in a list, starting with the activation of the input layer (essentially, the input data== ==`X`====). For every layer, it calculates the weighted sum of inputs and biases using== ==`np.dot(self.a[-1], layer['weights']) + layer['biases']`====, applies the sigmoid activation function, and tacks the result onto== ==`self.a`====. The outcome of the network is the last element in== ==`self.a`====, which represents the final output.====  
  
====在前向传递过程中，激活== ==`self.a`== ==被收集到一个列表中，从输入层的激活开始（基本上是输入数据== ==`X`== ==）。对于每一层，它都会使用== ==`np.dot(self.a[-1], layer['weights']) + layer['biases']`== ==计算输入和偏置的加权和，应用 sigmoid 激活函数，并将结果粘贴到== ==`self.a`== ==上。网络的结果是== ==`self.a`== ==中的最后一个元素，代表最终输出。==
==During the Backward pass, this stage kicks off by figuring out the derivative of the loss concerning the last layer’s activations (====`self.dz`====) and preps the list with the output layer's error. It then walks back through the network (using== ==`reversed(range(len(self.layers) - 1))`====), calculating error terms for the hidden layers. This involves dotting the current error term with the next layer's weights (backward) and scaling by the sigmoid function's derivative to handle the non-linearity.====  
  
====在后向传递过程中，该阶段首先计算出与最后一层激活相关的损失导数 (== ==`self.dz`== ==) ，并将输出层的误差预置在列表中。然后，它会回溯网络（使用== ==`reversed(range(len(self.layers) - 1))`== ==），计算隐藏层的误差项。这需要将当前误差项与下一层的权重（向后）进行点化，并用 sigmoid 函数的导数进行缩放，以处理非线性问题。==
==class Trainer:====  
  
====...====  
  
====def train(self, X_train, y_train, X_test, y_test, epochs, learning_rate):====  
  
====for _ in range(epochs):====  
  
====self.model.forward(X_train)====  
  
====self.model.backward(X_train, y_train, learning_rate)====  
  
====train_loss = self.calculate_loss(y_train, self.model.a[-1])====  
  
====self.train_loss.append(train_loss)==
```plain
                    self.model.forward(X\_test)  
        test\_loss = self.calculate\_loss(y\_test, self.model.a\[-1\])  
        self.test\_loss.append(test\_loss)
```
==Lastly, we’ve updated the== ==`Trainer`== ==class to align with the changes in the== ==`NeuralNetwork`== ==class. The significant adjustments are in the== ==`train`== ==method, particularly in recalculating training and testing loss since the network's output is now fetched from== ==`self.model.a[-1]`== ==rather than== ==`self.model.a2`====.====  
  
====最后，我们更新了== ==`Trainer`== ==类，以与== ==`NeuralNetwork`== ==类中的变化保持一致。重要的调整是在== ==`train`== ==方法中，特别是在重新计算训练和测试损失方面，因为网络的输出现在是从== ==`self.model.a[-1]`== ==而不是== ==`self.model.a2`== ==获取的。==
==These modifications not only make our neural network more adaptable to different architectures but also underscore the importance of understanding the flow of data and gradients through the network. By streamlining the structure, we enhance our ability to experiment with and optimize the network’s performance across various tasks.====  
  
====这些修改不仅使我们的神经网络更能适应不同的体系结构，而且还强调了理解数据流和梯度在网络中流动的重要性。通过精简结构，我们提高了在各种任务中试验和优化网络性能的能力。==
## ==3: Optimization Techniques for Enhanced Learning====  
  
====3: 优化技术促进学习==
==Optimizing neural networks is essential for boosting their ability to learn, ensuring efficient training, and steering them toward the best version they can be. Let’s dive into some crucial optimization techniques that significantly impact how well our models perform.====  
  
====优化神经网络对于提高神经网络的学习能力、确保高效训练以及引导神经网络达到最佳状态至关重要。让我们深入了解一些重要的优化技术，它们会对我们的模型性能产生重大影响。==
## ==3.1: Learning Rate 3.1:学习率==
==The learning rate is the control knob for adjusting the network’s weights based on the loss gradient. It sets the pace at which our model learns, determining how big or small the steps we take during optimization are. Getting the learning rate just right can help the model quickly find a solution with low error. On the flip side, if we don’t set it correctly, we might end up with a model that either takes forever to converge or doesn’t find a good solution at all.====  
  
====学习率是根据损失梯度调整网络权重的控制旋钮。它设定了模型的学习速度，决定了我们在优化过程中所采取的步骤的大小。恰到好处的学习速度可以帮助模型快速找到低误差的解决方案。反之，如果设置不当，我们的模型可能永远无法收敛，或者根本找不到好的解决方案。==
==If we set the learning rate too high, our model might just skip right over the best solution, leading to erratic behavior. This can show up as the accuracy or loss swinging wildly during training.====  
  
====如果我们设置的学习率过高，我们的模型可能会直接跳过最佳解决方案，从而导致不稳定的行为。这可能表现为准确率或损失在训练过程中大幅波动。==
==A learning rate that’s too low creeps along too slowly, dragging out the training process. Here, you’ll see the training loss barely budging over time.====  
  
====过低的学习率会使训练过程进展缓慢。在这里，你会看到训练损耗随着时间的推移几乎没有变化。==
==The trick is to monitor our training and validation loss as we go, which can give us clues about how our learning rate is doing. Two practical approaches are to log these losses at intervals during training and then plot them afterward to get a clearer picture of how smooth or erratic our loss landscape is. In our code, we’re using Python’s logging library to help us keep tabs on these metrics. Here’s how it looks:====  
  
====诀窍是在训练过程中监控训练和验证损失，这可以为我们提供学习率的线索。有两种实用的方法，一是在训练过程中每隔一段时间记录一次损失，然后在训练结束后绘制损失图，以便更清楚地了解损失情况的平稳性或不稳定性。在我们的代码中，我们使用 Python 的日志库来帮助我们跟踪这些指标。如下所示==
==import logging==
==logging.basicConfig(level=logging.INFO)====  
  
====logger = logging.getLogger(__name__)==
==class Trainer:====  
  
====...====  
  
====def train(self, X_train, y_train, X_val, y_val, epochs, learning_rate):====  
  
====for epoch in range(epochs):====  
  
====...==
```plain
        if epoch % 50 == 0:  
            logger.info(f'Epoch {epoch}: loss = {train\_loss}, val\_loss = {val\_loss}')
```
==At the start, we set up a logger to capture and display our training updates. This setup allows us to log the training and validation loss every 50 epochs, giving us a steady stream of feedback on how our model is doing. With this feedback, we can start to see patterns — maybe our loss is dropping nicely, or maybe it’s a bit too erratic, hinting that we might need to adjust our learning rate.====  
  
====一开始，我们设置了一个记录器来捕捉和显示我们的训练更新。通过这种设置，我们可以每 50 个历元记录一次训练和验证损失，从而获得有关模型运行情况的稳定反馈。有了这些反馈，我们就能开始发现一些规律--也许我们的损失下降得很好，也许它有点太不稳定，暗示我们可能需要调整学习速度。==
==def smooth_curve(points, factor=0.9):====  
  
====smoothed_points = []====  
  
====for point in points:====  
  
====if smoothed_points:====  
  
====previous = smoothed_points[-1]====  
  
====smoothed_points.append(previous * factor + point * (1 - factor))====  
  
====else:====  
  
====smoothed_points.append(point)====  
  
====return smoothed_points==
==smooth_train_loss = smooth_curve(trainer.train_loss)====  
  
====smooth_val_loss = smooth_curve(trainer.val_loss)==
==plt.plot(smooth_train_loss, label='Smooth Train Loss')====  
  
====plt.plot(smooth_val_loss, label='Smooth Val Loss')====  
  
====plt.title('Smooth Train and Val Loss')====  
  
====plt.xlabel('Epochs')====  
  
====plt.ylabel('Loss')====  
  
====plt.legend()====  
  
====plt.show()==
==The code above, instead, will allow us to plot training and validation loss to get a better understanding of how the losses behave during the training. Notice that we are adding an== ==`smoothing`== ==element, as we expect a little bit of noisiness for many iterations. Smoothing the noisiness will help us analyze the graph better.====  
  
====上面的代码可以让我们绘制训练和验证损失图，从而更好地了解损失在训练过程中的表现。请注意，我们添加了一个== ==`smoothing`== ==元素，因为我们预计在多次迭代中会有一点噪音。平滑噪声将有助于我们更好地分析图表。==
==Following this approach, once we kick off the training, we can expect to see logs pop up, providing a snapshot of our progress and helping us make informed adjustments along the way.====  
  
====按照这种方法，一旦我们开始培训，我们就能看到日志弹出，为我们的进展提供快照，并帮助我们做出明智的调整。==
[![](https://miro.medium.com/v2/resize:fit:700/1*cYd4cAA03PnwfmSYgm4deQ.png)](https://miro.medium.com/v2/resize:fit:700/1*cYd4cAA03PnwfmSYgm4deQ.png)
==Train and Validation Losses Logs — Image by Author====  
  
====列车和验证损失日志 - 图片作者==
==Then, we can plot the losses at the end of the training:====  
  
====然后，我们就可以绘制出训练结束时的损失图：==
[![](https://miro.medium.com/v2/resize:fit:584/1*FFNmdIrpqNRiK3yUi1PuvA.png)](https://miro.medium.com/v2/resize:fit:584/1*FFNmdIrpqNRiK3yUi1PuvA.png)
==Train and Validation Losses Plot — Image by Author====  
  
====列车和验证损失图 - 图片由作者提供==
==Seeing both training and validation losses steadily decrease is a good sign — it hints that bumping up the number of epochs and perhaps increasing the learning rate’s step size could work well for us. On the flip side, if we spot our losses yo-yo-ing, shooting up after a decrease, it’s a clear signal to dial down the learning rate’s step size. There’s a curious bit, though: between epoch 0 and epoch 50, something odd’s happening with our losses. We’ll circle back to figure that out.====  
  
====看到训练和验证损失都在稳步下降是个好兆头--它暗示我们，增加epochs的数量，或许还能增加学习率的步长，这对我们来说可能会很有效。反之，如果我们发现损失 "悠悠晃晃"，在下降后又上升，那么这就是一个明确的信号，我们应该减小学习率的步长。不过有一点很奇怪：在第 0 个历元和第 50 个历元之间，我们的损失发生了一些奇怪的变化。我们将回过头来弄清楚这个问题。==
==To zero in on that sweet spot for the learning rate, methods like learning rate annealing or adaptive learning rate techniques can be really handy. They fine-tune the learning rate on the fly, helping us stick to an optimal pace throughout the training.====  
  
====为了将学习率锁定在最佳位置，学习率退火或自适应学习率技术等方法非常有用。它们能即时微调学习率，帮助我们在整个训练过程中保持最佳速度。==
## ==3.2: Early Stopping Techniques====  
  
====3.2:早期停机技术==
==Early stopping is like a safety net — it watches how the model does on a validation set and calls time on training when things aren’t getting any better. This is our guard against overfitting, ensuring our model stays general enough to perform well on data it hasn’t seen before.====  
  
====早期停止就像一个安全网--它会观察模型在验证集上的表现，当情况没有任何好转时，就停止训练。这是我们防止过度拟合的措施，确保我们的模型保持足够的通用性，以便在未见过的数据上表现良好。==
==Here’s how to put it into action:====  
  
====下面是如何付诸行动的方法：==
1. ==**Validation Set**====: Carve out a slice of your training data to serve as a validation set. This is key because it means our stopping decision is based on fresh, unseen data.====  
      
    ====验证集：从训练数据中抽出一部分作为验证集。这一点很关键，因为它意味着我们的停止决策是基于新鲜的、未见过的数据。==
2. ==**Monitoring**====: Keep an eye on how the model fares on the validation set after each training epoch. Is it getting better, or has it plateaued?====  
      
    ====监测：在每个训练历元之后，密切关注模型在验证集上的表现。是越来越好，还是已经趋于稳定？==
3. ==**Stopping Criterion**====: Decide on a rule for when to stop. A common one is “no improvement in validation loss for 50 straight epochs.”====  
      
    ====停止标准：决定何时停止的规则。常见的规则是 "连续 50 个历元验证损失没有改善"。==
==Let’s dive into what the code for this might look like:====  
  
====让我们深入了解一下相关的代码：==
==class Trainer:====  
  
====def train(self, X_train, y_train, X_val, y_val, epochs, learning_rate,====  
  
====early_stopping=True, patience=10):====  
  
====best_loss = np.inf====  
  
====epochs_no_improve = 0==
```plain
    for epoch in range(epochs):  
       ...
          
        if early\_stopping:  
            if val\_loss < best\_loss:  
                best\_loss = val\_loss  
                best\_weights = \[layer\['weights'\] for layer in self.model.layers\]  
                epochs\_no\_improve = 0  
            else:  
                epochs\_no\_improve += 1
            if epochs\_no\_improve == patience:  
                print('Early stopping!')  
                  
                for i, layer in enumerate(self.model.layers):  
                    layer\['weights'\] = best\_weights\[i\]  
                break
```
==In the== ==`train`== ==method, we've introduced two new options:====  
  
====在== ==`train`== ==方法中，我们引入了两个新选项：==
- ==`early_stopping`====: This is a yes-or-no flag that lets us turn early stopping on or off.====  
      
    ====`early_stopping`== ==是或否：这是一个 "是 "或 "否 "标记，让我们可以打开或关闭提前停止功能。==
- ==`patience`====: This sets how many rounds of no improvements in validation loss we’re willing to wait before we call it quits on training.====  
      
    ====`patience`== ==：设定在验证损失没有改善的情况下，我们愿意等待多少轮后才停止训练。==
==We kick things off by setting== ==`best_loss`== ==to infinity. This acts as our benchmark for the lowest validation loss we've seen so far during training. Meanwhile,== ==`epochs_no_improve`== ==keeps a tally of how many epochs have gone by without any betterment in validation loss.====  
  
====我们将== ==`best_loss`== ==设置为无穷大。这是我们在训练过程中看到的最低验证损失的基准。与此同时，== ==`epochs_no_improve`== ==会记录在验证损失没有任何改善的情况下，已经进行了多少个历时。==
==As we loop through each epoch to train our model with the training data, we’re on the lookout for changes in validation loss after every pass (the actual training steps like forward propagation and backpropagation aren’t detailed here but are vital parts of the process).====  
  
====当我们循环使用训练数据训练模型时，我们会关注每次训练后验证损失的变化（实际训练步骤如前向传播和反向传播在此不做详细介绍，但它们是整个过程的重要组成部分）。==
==Post every epoch, we check if the current epoch’s validation loss (====`val_loss`====) dips below== ==`best_loss`====, it means we're making progress. We update== ==`best_loss`== ==to this new low, and also save the current model weights as== ==`best_weights`====. This way, we always have a snapshot of the model at its peak performance. We then reset the== ==`epochs_no_improve`== ==count to zero since we just saw an improvement.====  
  
====每过一个纪元，我们都会检查当前纪元的验证损失 (== ==`val_loss`== ==) 是否低于== ==`best_loss`== ==，这意味着我们正在取得进展。我们会将== ==`best_loss`== ==更新为这个新低，同时将当前模型权重保存为== ==`best_weights`== ==。这样，我们就始终拥有模型性能峰值的快照。然后，我们将== ==`epochs_no_improve`== ==计数重置为零，因为我们刚刚看到了进步。==
==If there’s no drop in== ==`val_loss`====, we increase== ==`epochs_no_improve`== ==by one, indicating another epoch has passed without betterment.====  
  
====如果== ==`val_loss`== ==没有下降，我们就将== ==`epochs_no_improve`== ==增加 1，这表明又过了一个纪元，但没有改善。==
==If our== ==`epochs_no_improve`== ==count hits the== ==`patience`== ==limit we've set, it's our cue that the model isn't likely to get any better, so we trigger early stopping. We let everyone know with a message and revert the model's weights back to== ==`best_weights`====, the gold standard we've been keeping track of. Then, we exit the training loop.====  
  
====如果我们的== ==`epochs_no_improve`== ==计数达到了我们设定的== ==`patience`== ==上限，这就提示我们，模型不可能再有任何改进，因此我们会触发提前停止。我们会用一条信息通知所有人，并将模型权重恢复到== ==`best_weights`== ==，也就是我们一直跟踪的黄金标准。然后，我们退出训练循环。==
==This approach gives us a balanced way to halt training — not too soon, so we give the model a fair chance to learn, but not too late, where we’re just wasting time or risking overfitting.====  
  
====这种方法为我们提供了一种平衡的方法来停止训练--不要太早，这样我们就能给模型一个公平的学习机会，但也不要太晚，否则我们就会浪费时间或冒过度拟合的风险。==
## ==3.3: Initialization Methods====  
  
====3.3:初始化方法==
==When setting up a neural network, how you kick off the weights can change the game in terms of how well and how quickly the network learns. Let’s go over a few different ways to initialize weights — random, zeros, Glorot (Xavier), and He initialization — and what makes each method unique.====  
  
====在建立神经网络时，如何启动权重可以改变网络学习的效果和速度。让我们来看看初始化权重的几种不同方法--随机、零、Glorot (Xavier) 和 He 初始化--以及每种方法的独特之处。==
==**Random Initialization 随机初始化====  
  
====**Going the random route means setting up the initial weights by pulling numbers from a distribution, usually either uniform or normal. This randomness helps ensure that no two neurons start the same, allowing them to learn different things as the network trains. The trick is picking a variance that’s just right — too much, and you risk blowing up the gradients; too little, and they might disappear.====  
  
====采用随机路线意味着通过从分布（通常是均匀分布或正态分布）中提取数字来设置初始权重。这种随机性有助于确保没有两个神经元的起点是相同的，从而让它们在网络训练过程中学到不同的东西。诀窍在于选择一个恰到好处的方差--方差太大，有可能会破坏梯度；方差太小，梯度可能会消失。==
==weights = np.random.randn(layers[i], layers[i + 1])==
==This line of code plucks weights from a standard normal distribution, setting the stage for each neuron to potentially go down its path of learning.====  
  
====这行代码从标准正态分布中提取权重，为每个神经元的潜在学习路径奠定基础。==
==**Pros**====: It’s a straightforward approach that helps prevent neurons from mimicking each other.====  
  
====优点这是一种直接的方法，有助于防止神经元之间相互模仿。==
==**Cons**====: Getting the variance wrong can cause the learning process to be unstable.====  
  
====缺点：弄错方差会导致学习过程不稳定。==
==**Zeros Initialization 零 初始化====  
  
====**Setting all weights to zero is about as simple as it gets. However, this method has a major downside: it makes every neuron in a layer effectively the same. This sameness can stunt the network’s learning, as every neuron on the same layer will update identically during training.====  
  
====将所有权重设置为零是最简单不过的方法了。但是，这种方法有一个很大的缺点：它会使一层中的每个神经元实际上都是一样的。这种相同会阻碍网络的学习，因为在训练过程中，同一层的每个神经元都会进行相同的更新。==
==weights = np.zeros((layers[i], layers[i + 1]))==
==Here, we end up with a weight matrix full of zeros. It’s neat and orderly, but it also means every path through the network initially carries the same weight, which isn’t great for learning diversity.====  
  
====在这里，我们最终会得到一个充满零的权重矩阵。这样虽然整齐有序，但也意味着网络中的每条路径最初都具有相同的权重，这对学习多样性来说并不是好事。==
==**Pros**====: Very easy to implement.====  
  
====优点非常容易实施。==
==**Cons**====: It handcuffs the learning process, usually resulting in subpar network performance.====  
  
====缺点：它束缚了学习过程，通常会导致网络性能不佳。==
==**Glorot Initialization Glorot 初始化====  
  
====**Designed specifically for networks with sigmoid activation functions, Glorot initialization sets the weights based on the number of input and output units in the network. It aims to maintain the variance of activations and back-propagated gradients through the layers, preventing the vanishing or exploding gradient problem.====  
  
====Glorot 初始化是专为具有 sigmoid 激活函数的网络设计的，它根据网络中输入和输出单元的数量设置权重。其目的是在各层中保持激活和反向传播梯度的方差，防止出现梯度消失或爆炸问题。==
==The weights in the Glorot initialization can be drawn either by a uniform distribution or a normal distribution. For uniform distribution, weights are initialized using the range [−_a_,== ==_a_====], where== ==_a_== ==is:====  
  
====Glorot 初始化的权重可以按均匀分布或正态分布绘制。对于均匀分布，权重初始化的范围是 [-a，a]，其中 a 是：==
[![](https://miro.medium.com/v2/resize:fit:264/1*G3HnNQgTj5e7jCO6-9Bm_w.png)](https://miro.medium.com/v2/resize:fit:264/1*G3HnNQgTj5e7jCO6-9Bm_w.png)
==Glorot Uniform Distribution — Image by Author====  
  
====格罗洛特统一配送 - 图片作者==
==def glorot_uniform(self, fan_in, fan_out):====  
  
====limit = np.sqrt(6 / (fan_in + fan_out))====  
  
====return np.random.uniform(-limit, limit, (fan_in, fan_out))==
==weights = glorot_uniform(layers[i - 1], layers[i])==
==This formula ensures the weights start spread evenly, are ready to catch, and maintain a good gradient flow.====  
  
====这种配方可确保砝码开始时均匀分布，随时可以抓住，并保持良好的梯度流动。==
==For a normal distribution:====  
  
====正态分布==
[![](https://miro.medium.com/v2/resize:fit:373/1*g17UmOiXJoIsHQud8po4Dg.png)](https://miro.medium.com/v2/resize:fit:373/1*g17UmOiXJoIsHQud8po4Dg.png)
==Glorot Normal Distribution — Image by Author====  
  
====格洛特正态分布 - 图片作者==
==def glorot_normal(self, fan_in, fan_out):====  
  
====stddev = np.sqrt(2. / (fan_in + fan_out))====  
  
====return np.random.normal(0., stddev, size=(fan_in, fan_out))==
==weights = self.glorot_normal(layers[i - 1], layers[i])==
==This adjustment keeps the weights spread just right for networks leaning on sigmoid activations.====  
  
====这种调整可使权重分布恰到好处，适合依赖于西格码激活的网络。==
==**Pros**====: Maintains gradient variance in a reasonable range, improving the stability of deep networks.====  
  
====优点将梯度方差保持在合理范围内，提高深度网络的稳定性。==
==**Cons**====: May not be optimal for layers with ReLU (or variants) activations due to different signal propagation characteristics.====  
  
====缺点：由于信号传播特性不同，对于具有 ReLU（或变体）激活的层可能不是最佳选择。==
==**He Initialization He 初始化====  
  
====**He initialization, tailored for layers with ReLU activation functions, adjusts the variance of the weights considering the non-linear characteristics of ReLU. This strategy helps maintain a healthy gradient flow through the network, especially important in deep networks where ReLU is commonly used.====  
  
====他的初始化是为具有 ReLU 激活函数的层量身定制的，考虑了 ReLU 的非线性特性，调整了权重的方差。这一策略有助于在网络中保持健康的梯度流，这对常用 ReLU 的深度网络尤为重要。==
==Like the Glorot initialization, the weights can be drawn either from a uniform or normal distribution.====  
  
====与 Glorot 初始化一样，权重可以从均匀分布或正态分布中提取。==
==For the uniform distribution, the weights are initialized using the range [−_a_,== ==_a_====], where== ==_a_== ==is calculated as:====  
  
====对于均匀分布，权重的初始化范围为 [-a，a]，其中 a 的计算公式为==
[![](https://miro.medium.com/v2/resize:fit:175/1*rGYeovFpns7HkMPqaxyQXw.png)](https://miro.medium.com/v2/resize:fit:175/1*rGYeovFpns7HkMPqaxyQXw.png)
==Thus, the weights== ==_W_== ==are drawn from a uniform distribution as:====  
  
====因此，权重 W 取自均匀分布，即==
[![](https://miro.medium.com/v2/resize:fit:544/1*Or9t5QYpI2f6t0E6PvN3RQ.png)](https://miro.medium.com/v2/resize:fit:544/1*Or9t5QYpI2f6t0E6PvN3RQ.png)
==def he_uniform(self, fan_in, fan_out):====  
  
====limit = np.sqrt(2 / fan_in)====  
  
====return np.random.uniform(-limit, limit, (fan_in, fan_out))==
==weights = self.he_uniform(layers[i - 1], layers[i])==
==When using a normal distribution, the weights are initialized according to the formula:====  
  
====使用正态分布时，权重根据公式初始化：==
[![](https://miro.medium.com/v2/resize:fit:284/1*MTkWrihWQyo13AEdsdoX0Q.png)](https://miro.medium.com/v2/resize:fit:284/1*MTkWrihWQyo13AEdsdoX0Q.png)
==He Normal Distribution — Image By Author====  
  
====He 正态分布 - 图片作者==
==where== ==_W_== ==represents the weights, N denotes the normal distribution, 0 is the mean of the distribution, and 2/n​ is the variance. _n-_in​ is the number of input units to the layer.====  
  
====其中，W 表示权重，N 表示正态分布，0 表示分布的均值，2/n 表示方差。==
==def he_normal(self, fan_in, fan_out):====  
  
====stddev = np.sqrt(2. / fan_in)====  
  
====return np.random.normal(0., stddev, size=(fan_in, fan_out))==
==weights = self.he_normal(layers[i - 1], layers[i])==
==In both cases, the initialization strategy aims to account for the properties of the ReLU activation function, which does not activate all neurons in the layer due to its non-negative output for positive input. This adjustment in the variance of the initial weights helps prevent the diminishing or exploding of gradients that can occur in deep networks, promoting a more stable and efficient training process.====  
  
====在这两种情况下，初始化策略的目的都是考虑 ReLU 激活函数的特性。由于 ReLU 激活函数的正输入非负输出，它不会激活层中的所有神经元。对初始权重方差的这种调整有助于防止深度网络中可能出现的梯度减小或爆炸，从而促进更稳定、更高效的训练过程。==
==**Pros**====: Facilitates deep learning models’ training by preserving gradient magnitudes in networks with ReLU activations.====  
  
====优点通过在具有 ReLU 激活的网络中保留梯度大小，促进深度学习模型的训练。==
==**Cons**====: It’s specifically optimized for ReLU and might not be as effective as other activation functions.====  
  
====缺点：它专门针对 ReLU 进行了优化，可能不如其他激活函数有效。==
==Let’s take a look now at how the== ==`NeuralNetwork`== ==class looks like after introducing the initializations:====  
  
====现在让我们看看== ==`NeuralNetwork`== ==类在引入初始化后的样子：==
==class NeuralNetwork:====  
  
====def __init__(self,====  
  
====layers,====  
  
====init_method='glorot_uniform',====  
  
====loss_func='mse', ):====  
  
====...==
```plain
    self.init\_method = init\_method
      
    for i in range(len(layers) - 1):  
        if self.init\_method == 'zeros':  
            weights = np.zeros((layers\[i\], layers\[i + 1\]))  
        elif self.init\_method == 'random':  
            weights = np.random.randn(layers\[i\], layers\[i + 1\])  
        elif self.init\_method == 'glorot\_uniform':  
            weights = self.glorot\_uniform(layers\[i\], layers\[i + 1\])  
        elif self.init\_method == 'glorot\_normal':  
            weights = self.glorot\_normal(layers\[i\], layers\[i + 1\])  
        elif self.init\_method == 'he\_uniform':  
            weights = self.he\_uniform(layers\[i\], layers\[i + 1\])  
        elif self.init\_method == 'he\_normal':  
            weights = self.he\_normal(layers\[i\], layers\[i + 1\])
        else:  
            raise ValueError(f'Unknown initialization method {self.init\_method}')
        self.layers.append({  
            'weights': weights,  
            'biases': np.zeros((1, layers\[i + 1\]))  
        })
    ...
...
def glorot\_uniform(self, fan\_in, fan\_out):  
    limit = np.sqrt(6 / (fan\_in + fan\_out))  
    return np.random.uniform(-limit, limit, (fan\_in, fan\_out))
def he\_uniform(self, fan\_in, fan\_out):  
    limit = np.sqrt(2 / fan\_in)  
    return np.random.uniform(-limit, limit, (fan\_in, fan\_out))
    def glorot\_normal(self, fan\_in, fan\_out):  
    stddev = np.sqrt(2. / (fan\_in + fan\_out))  
    return np.random.normal(0., stddev, size=(fan\_in, fan\_out))
def he\_normal(self, fan\_in, fan\_out):  
    stddev = np.sqrt(2. / fan\_in)  
    return np.random.normal(0., stddev, size=(fan\_in, fan\_out))
...
```
==Choosing the right weight initialization strategy is crucial for effective neural network training. While random and zeros initialization offers fundamental approaches, they might not always lead to optimal learning dynamics. In contrast, Glorot/Xavier and He initialization provides more sophisticated solutions that address the specific needs of deep learning models, considering the network architecture and activation functions used. These strategies help in balancing the trade-offs between too rapid and too slow learning, steering the training process towards more reliable convergence.====  
  
====选择正确的权重初始化策略对于有效的神经网络训练至关重要。虽然随机初始化和零初始化提供了基本方法，但它们并不总能带来最佳的学习动态。相比之下，Glorot/Xavier 和 He 初始化提供了更复杂的解决方案，能满足深度学习模型的特定需求，同时考虑到所使用的网络架构和激活函数。这些策略有助于平衡过快和过慢学习之间的权衡，引导训练过程实现更可靠的收敛。==
## ==3.4: Dropout 3.4:辍学==
==Dropout is a regularization technique designed to prevent overfitting in neural networks by temporarily and randomly removing units (neurons) along with their connections from the network during the training phase. This method was introduced by== [==Srivastava et al.==](https://jmlr.org/papers/v15/srivastava14a.html) ==in their 2014 paper as a simple yet effective way to train robust neural networks.====  
  
====Dropout是一种正则化技术，旨在通过在训练阶段临时随机地从网络中移除单元（神经元）及其连接来防止神经网络的过拟合。这种方法由 Srivastava 等人在 2014 年的论文中提出，是训练鲁棒神经网络的一种简单而有效的方法。==
[![](https://miro.medium.com/v2/resize:fit:650/1*wJ1baga-2pUCc4LdN3kULw.png)](https://miro.medium.com/v2/resize:fit:650/1*wJ1baga-2pUCc4LdN3kULw.png)
==Image by== [==Srivastava, Nitish, et al. ”Dropout: a simple way to prevent neural networks from==](https://jmlr.org/papers/v15/srivastava14a.html)==  
  
==[==图片来源：Srivastava, Nitish, et al.==](https://jmlr.org/papers/v15/srivastava14a.html)==  
  
==[==overfitting”, JMLR 2014 过拟合"，JMLR 2014==](https://jmlr.org/papers/v15/srivastava14a.html)
==During each training iteration, each neuron (including input units but typically not the output units) has a probability== ==_p_== ==of being temporarily “dropped out,” meaning it is entirely ignored during this forward and backward pass. This probability== ==_p_====, often referred to as the “dropout rate,” is a hyperparameter that can be adjusted to optimize performance. For instance, a dropout rate of 0.5 means each neuron has a 50% chance of being omitted from the computation on each training pass.====  
  
====在每次训练迭代中，每个神经元（包括输入单元，但通常不包括输出单元）都有一个被暂时 "丢弃 "的概率 p，这意味着它在前向和后向传递过程中会被完全忽略。这个概率 p 通常被称为 "退出率"，是一个超参数，可以通过调整来优化性能。例如，0.5 的丢弃率意味着每个神经元在每次训练中被忽略的概率为 50%。==
==The effect of this process is that the network becomes less sensitive to the specific weights of any one neuron. This is because it cannot rely on any individual neuron’s output when making predictions, thus encouraging the network to spread out importance among its neurons. It effectively trains a pseudo-ensemble of neural networks with shared weights, where each training iteration involves a different “thinned” version of the network. At test time, dropout is not applied, and instead, the weights are typically scaled by the dropout rate== ==_p_== ==to balance the fact that more units are active than during training.====  
  
====这一过程的效果是，网络对任何一个神经元的特定权重的敏感度降低。这是因为在进行预测时，它不能依赖于任何一个神经元的输出，从而鼓励网络在神经元之间分散重要性。它有效地训练了一个具有共享权重的神经网络伪集合，每次训练迭代都会涉及一个不同的 "精简 "网络版本。在测试时，不会应用掉线，相反，权重通常会按掉线率 p 进行缩放，以平衡比训练期间更多单元处于活动状态这一事实。==
==**Choosing the Right Dropout Rate====  
  
====选择正确的辍学率====  
  
====**The dropout rate is a hyperparameter that requires tuning for each neural network architecture and dataset. Commonly, a rate of 0.5 is used for hidden units as a starting point, as suggested in the original dropout paper.====  
  
====辍学率是一个超参数，需要根据不同的神经网络架构和数据集进行调整。通常情况下，根据原始辍学论文的建议，隐藏单元的辍学率以 0.5 为起点。==
==A high dropout rate (close to 1) means more neurons are dropped during training. This can lead to underfitting, as the network may not be able to learn the data sufficiently, struggling to model the complexity of the training data.====  
  
====高丢弃率（接近 1）意味着训练过程中丢弃的神经元较多。这会导致拟合不足，因为网络可能无法充分学习数据，难以对训练数据的复杂性进行建模。==
==Conversely, a low dropout rate (close to 0) results in fewer neurons being dropped, which might reduce the regularization effect of dropout and could lead to overfitting, where the model performs well on the training data but poorly on unseen data.====  
  
====相反，低丢弃率（接近 0）会导致丢弃的神经元数量减少，这可能会降低丢弃的正则化效果，并可能导致过拟合，即模型在训练数据上表现良好，但在未见数据上表现不佳。==
==**Code Implementation 代码执行====  
  
====**Let’s see how this looks in our code:====  
  
====让我们看看代码中是如何显示的：==
==class NeuralNetwork:====  
  
====def __init__(self,====  
  
====layers,====  
  
====init_method='glorot_uniform',====  
  
====loss_func='mse',====  
  
====dropout_rate=0.5 ):====  
  
====...==
```plain
    self.dropout\_rate = dropout\_rate
            ...
...
def forward(self, X, is\_training=True):  
    self.a = \[X\]  
    for i, layer in enumerate(self.layers):  
        z = np.dot(self.a\[-1\], layer\['weights'\]) + layer\['biases'\]  
        a = self.sigmoid(z)  
        if is\_training and i < len(self.layers) - 1:    
            dropout\_mask = np.random.rand(\*a.shape) > self.dropout\_rate  
            a \*= dropout\_mask  
        self.a.append(a)  
    return self.a\[-1\]
...
```
==Our neural network class has gotten an upgrade with new initialization parameters and a forward propagation method that now includes dropout regularization.====  
  
====我们的神经网络类进行了升级，增加了新的初始化参数和前向传播方法，现在还包括滤除正则化。==
==`dropout_rate`== ==: This is a setting that decides how likely it is for neurons to be temporarily removed from the network during training, helping to avoid overfitting. By setting it to 0.5, we’re saying there’s a 50% chance that any given neuron will be “dropped” in a training round. This randomness helps ensure the network doesn’t become too dependent on any single neuron, promoting a more robust learning process.====  
  
====`dropout_rate`== ==：这一设置决定了神经元在训练过程中被暂时从网络中删除的可能性，有助于避免过度拟合。将其设置为 0.5，就意味着在一轮训练中，任何一个神经元被 "剔除 "的几率为 50%。这种随机性有助于确保网络不会过于依赖任何一个神经元，从而促进更稳健的学习过程。==
==The== ==`is_training`== ==boolean flag tells the network whether it's currently being trained. This is important because dropout is something you'd only want to happen during training, not when you're evaluating the network's performance on new data.====  
  
====`is_training`== ==布尔标志会告诉网络当前是否正在接受训练。这一点很重要，因为只有在训练过程中，而不是在评估网络在新数据上的性能时，才会出现辍学现象。==
==As data (denoted as== ==`X`====) makes its way through the network, the network calculates a weighted sum (====`z`====) of the incoming data and the layer's biases. It then runs this sum through the sigmoid activation function to get the activations (====`a`====), which are the signals that will be passed on to the next layer.====  
  
====当数据（以== ==`X`== ==表示）通过网络时，网络会计算输入数据与该层偏置的加权和 (== ==`z`== ==)。然后，它将此和通过 sigmoid 激活函数来获得激活度 (== ==`a`== ==)，即传递给下一层的信号。==
==But before we proceed to the next layer during training, we might apply dropout:====  
  
====但在训练过程中，在进入下一层之前，我们可能会应用 dropout：==
- ==If== ==`is_training`== ==is true and we're not dealing with the output layer, we roll the dice for each neuron to see if it gets dropped. We do this by creating a== ==`dropout_mask`====—an array shaped just like== ==`a`====. Each element in this mask is the outcome of checking if a random number exceeds the== ==`dropout_rate`====.====  
      
    ====如果== ==`is_training`== ==为真，而且我们没有处理输出层，我们就会为每个神经元掷骰子，看它是否会被丢弃。为此，我们将创建一个== ==`dropout_mask`== ==--一个形状与== ==`a`== ==类似的数组。该掩码中的每个元素都是检查随机数是否超过== ==`dropout_rate`== ==的结果。==
- ==We then use this mask to zero out some of the activations in== ==`a`====, effectively simulating the temporary removal of neurons from the network.====  
      
    ====然后，我们使用该掩码将== ==`a`== ==中的部分激活归零，从而有效模拟从网络中暂时删除神经元的情况。==
==After we’ve applied dropout (when applicable), we add the resulting activations to== ==`self.a`====, our list that keeps track of the activations across all layers. This way, we're not just blindly moving signals from one layer to the next; we're also applying a technique that encourages the network to learn more robustly, making it less likely to rely too heavily on any specific pathway of neurons.====  
  
====在应用 "剔除"（如适用）后，我们会将由此产生的激活状态添加到== ==`self.a`== ==，我们的列表会跟踪所有层的激活状态。这样，我们就不会盲目地将信号从一层移到下一层；我们还采用了一种技术，鼓励网络更稳健地学习，使其不太可能过于依赖任何特定的神经元通路。==
## ==3.5: Gradient Clipping 3.5: 梯度剪切==
==Gradient clipping is a crucial technique in training deep neural networks, especially in dealing with the problem of exploding gradients. Exploding gradients occur when the derivatives or gradients of the loss function for the network’s parameters grow exponentially through the layers, leading to very large updates to the weights during training. This can cause the learning process to become unstable, often manifesting as NaN values in the weights or loss due to numerical overflow, which in turn prevents the model from converging to a solution.====  
  
====梯度剪切是训练深度神经网络的一项重要技术，尤其是在处理梯度爆炸问题时。当网络参数的损失函数的导数或梯度在各层中呈指数增长时，就会出现梯度爆炸，导致训练过程中的权重更新非常大。这会导致学习过程变得不稳定，通常表现为权重中的 NaN 值或数值溢出导致的损失，这反过来又会阻止模型收敛到一个解决方案。==
==Gradient clipping can be implemented in two primary ways: by value and by norm, each with its strategy for mitigating the issue of exploding gradients.====  
  
====梯度剪切有两种主要方式：数值剪切和规范剪切，每种方式都有自己的策略来缓解梯度爆炸的问题。==
==**Clipping by Value 按价值剪辑**====  
  
====This approach involves setting a predefined threshold value, and directly clipping each gradient component to be within a specified range if it exceeds this threshold. For example, if the threshold is set to 1, every gradient component greater than 1 is set to 1, and every component less than -1 is set to -1. This ensures that all gradients remain within the range [-1, 1], effectively preventing any gradient from becoming too large.====  
  
====这种方法包括设置一个预定义的阈值，如果每个梯度分量超过该阈值，则直接将其剪切到指定范围内。例如，如果阈值设置为 1，则每个大于 1 的梯度分量都会被设置为 1，而每个小于-1 的分量都会被设置为-1。这样可以确保所有梯度都保持在 [-1, 1] 的范围内，有效防止梯度过大。==
[![](https://miro.medium.com/v2/resize:fit:700/1*0t6cduZn7fIhEgA3n6El8w.png)](https://miro.medium.com/v2/resize:fit:700/1*0t6cduZn7fIhEgA3n6El8w.png)
==where _gi_​ represents each component of the gradient vector.====  
  
====其中，gi 代表梯度矢量的每个分量。==
==**Clipping by Norm 诺姆的剪报**====  
  
====Instead of clipping each gradient component individually, this method scales the whole gradient if its norm exceeds a certain threshold. This preserves the direction of the gradient while ensuring its magnitude does not exceed the specified limit. This is particularly useful in maintaining the relative direction of the updates across all parameters, which can be more beneficial for the learning process than clipping by value.====  
  
====这种方法不是对每个梯度成分进行单独剪切，而是在梯度值超过一定阈值时对整个梯度进行缩放。这样既能保持梯度的方向，又能确保其幅度不超过指定的限制。这对于保持所有参数更新的相对方向特别有用，比按值剪切更有利于学习过程。==
[![](https://miro.medium.com/v2/resize:fit:536/1*VDI7LcQaJ3HKgM3EXEBIhQ.png)](https://miro.medium.com/v2/resize:fit:536/1*VDI7LcQaJ3HKgM3EXEBIhQ.png)
==where== ==_g_== ==is the gradient vector and ∥_g_∥ is its norm.====  
  
====其中，g 是梯度向量，∥g∥ 是其规范。==
==**Application in Training 培训中的应用**==
==class NeuralNetwork:====  
  
====def __init__(self,====  
  
====layers,====  
  
====init_method='glorot_uniform',====  
  
====loss_func='mse',====  
  
====dropout_rate=0.5,====  
  
====clip_type='value',====  
  
====grad_clip=5.0 ):====  
  
====...==
```plain
    self.clip\_type = clip\_type  
    self.grad\_clip = grad\_clip
            ...
...
def backward(self, X, y, learning\_rate):  
    m = X.shape\[0\]  
    self.dz = \[self.a\[-1\] - y\]  
    self.gradient\_norms = \[\]  
    for i in reversed(range(len(self.layers) - 1)):  
        self.dz.append(np.dot(self.dz\[-1\], self.layers\[i + 1\]\['weights'\].T) \* self.sigmoid\_derivative(self.a\[i + 1\]))  
        self.gradient\_norms.append(np.linalg.norm(self.layers\[i + 1\]\['weights'\]))  
    self.dz = self.dz\[::-1\]  
    self.gradient\_norms = self.gradient\_norms\[::-1\]  
    for i in range(len(self.layers)):  
        grads\_w = np.dot(self.a\[i\].T, self.dz\[i\]) / m  
        grads\_b = np.sum(self.dz\[i\], axis=0, keepdims=True) / m
          
        if self.clip\_type == 'value':  
            grads\_w = np.clip(grads\_w, -self.grad\_clip, self.grad\_clip)  
            grads\_b = np.clip(grads\_b, -self.grad\_clip, self.grad\_clip)  
        elif self.clip\_type == 'norm':  
            grads\_w = self.clip\_by\_norm(grads\_w, self.grad\_clip)  
            grads\_b = self.clip\_by\_norm(grads\_b, self.grad\_clip)
        self.layers\[i\]\['weights'\] -= learning\_rate \* grads\_w  
        self.layers\[i\]\['biases'\] -= learning\_rate \* grads\_b
def clip\_by\_norm(self, grads, clip\_norm):  
    l2\_norm = np.linalg.norm(grads)  
    if l2\_norm > clip\_norm:  
        grads = grads / l2\_norm \* clip\_norm  
    return grads
    ...
```
==During the initialization, we now have the type of gradient clipping to use (====`clip_type`====), and the gradient clipping threshold (====`grad_clip`====).====  
  
====在初始化过程中，我们现在有了要使用的梯度削波类型 (== ==`clip_type`== ==) 和梯度削波阈值 (== ==`grad_clip`== ==) 。==
==`clip_type`== ==can be either== ==`'value'`== ==for clipping gradients by value or== ==`'norm'`== ==for clipping gradients by their L2 norm.== ==`grad_clip`== ==specifies the threshold or limit for the clipping.====  
  
====`clip_type`== ==可以是 （按值剪切梯度），也可以是 （按 L2 准则剪切梯度）。 指定剪切的阈值或限制。== ==`'value'`== ==`'norm'`== ==`grad_clip`==
==Then, during the backward pass, the function computes the gradients for each layer in the network by performing backpropagation. It calculates the derivatives of the loss for the weights (====`grads_w`====) and biases (====`grads_b`====) for each layer.====  
  
====然后，在后向传递过程中，函数通过反向传播计算网络中每一层的梯度。它为每一层的权重 (== ==`grads_w`== ==) 和偏置 (== ==`grads_b`== ==) 计算损失的导数。==
==If== ==`clip_type`== ==is== ==`'value'`====, gradients are clipped to be within the range== ==`[-grad_clip, grad_clip]`== ==using== ==`np.clip`====. This ensures no gradient component exceeds these bounds.====  
  
====如果== ==`clip_type`== ==是== ==`'value'`== ==，梯度将通过== ==`np.clip`== ==被剪切到== ==`[-grad_clip, grad_clip]`== ==的范围内。这样可以确保梯度分量不会超出这些范围。==
==If== ==`clip_type`== ==is== ==`'norm'`====, the== ==`clip_by_norm`== ==method is called to scale down the gradients if their norm exceeds== ==`grad_clip`====, preserving their direction but limiting their magnitude.====  
  
====如果== ==`clip_type`== ==是== ==`'norm'`== ==，则调用== ==`clip_by_norm`== ==方法，在梯度值超过== ==`grad_clip`== ==时缩小梯度值，保留梯度值的方向，但限制梯度值的大小。==
==After clipping, the gradients are used to update the weights and biases of each layer, scaled by the learning rate.====  
  
====剪切后，梯度被用于更新各层的权重和偏置，并按学习率缩放。==
==Lastly, we create a== ==`clip_by_norm`== ==method, which scales the gradients if their L2 norm exceeds the specified== ==`clip_norm`====. It calculates the L2 norm of the gradients and, if it's greater than== ==`clip_norm`====, scales the gradients down to the== ==`clip_norm`== ==while preserving their direction. This is achieved by dividing the gradients by their L2 norm and multiplying by== ==`clip_norm`====.====  
  
====最后，我们创建一个== ==`clip_by_norm`== ==方法，如果梯度的 L2 准则超过指定的== ==`clip_norm`== ==，该方法就会对梯度进行缩放。它计算梯度的 L2 准则，如果大于== ==`clip_norm`== ==，则将梯度缩放到== ==`clip_norm`== ==，同时保留梯度的方向。具体方法是将梯度除以其 L2 准则，再乘以== ==`clip_norm`== ==。==
==**Benefits of Gradient Clipping====  
  
====梯度剪切的优点====  
  
====**By preventing excessively large updates to the model’s weights, gradient clipping contributes to a more stable and reliable training process. It allows the optimizer to make consistent progress in minimizing the loss function, even in cases where the calculation of gradients might otherwise lead to instability due to the scale of updates. This makes it a valuable tool in the training of deep neural networks, particularly in tasks such as training recurrent neural networks (RNNs), where the problem of exploding gradients is more prevalent.====  
  
====通过防止对模型权重进行过大的更新，梯度削波有助于提高训练过程的稳定性和可靠性。它允许优化器在最小化损失函数方面取得一致进展，即使在梯度计算可能因更新规模而导致不稳定的情况下也是如此。这使它成为训练深度神经网络的重要工具，尤其是在训练递归神经网络（RNN）等任务中，梯度爆炸问题更为普遍。==
==Gradient clipping represents a straightforward yet powerful technique to enhance the stability and performance of neural network training. By ensuring that gradients do not become excessively large, it helps avoid the pitfalls of training instability, such as overfitting, underfitting, and slow convergence, making it easier for neural networks to learn effectively and efficiently.====  
  
====梯度削波是一种简单而强大的技术，可以提高神经网络训练的稳定性和性能。通过确保梯度不会变得过大，它有助于避免训练不稳定带来的隐患，如过度拟合、拟合不足和收敛缓慢等，从而使神经网络更容易有效学习。==
## ==4: Determining the Optimal Number of Layers====  
  
====4: 确定最佳层数==
==One of the pivotal decisions in designing a neural network is determining the right number of layers. This aspect significantly influences the network’s ability to learn from data and generalize to new, unseen data. The depth of a neural network — how many layers it has — can either empower its learning capacity or lead to challenges like overfitting or underlearning.====  
  
====设计神经网络的关键决策之一是确定正确的层数。这一点对网络从数据中学习并泛化到新的、未见过的数据的能力有重大影响。神经网络的深度--它有多少层--既能增强其学习能力，也会导致过度拟合或学习不足等挑战。==
## ==4.1: Layer Depth and Model Performance====  
  
====4.1:层深度和模型性能==
==Adding more layers to a neural network enhances its learning capacity, enabling it to capture more complex patterns and relationships in the data. This is because additional layers can create more abstract representations of the input data, moving from simple features to more complex combinations.====  
  
====为神经网络添加更多层可以增强其学习能力，使其能够捕捉数据中更复杂的模式和关系。这是因为更多的层可以为输入数据创建更抽象的表征，从简单的特征到更复杂的组合。==
==While deeper networks can model complex patterns, there’s a tipping point where additional depth might lead to overfitting. Overfitting occurs when the model learns the training data too well, including its noise, making it perform poorly on new data.====  
  
====虽然深度网络可以对复杂的模式进行建模，但也存在一个临界点，即增加深度可能会导致过度拟合。过度拟合是指模型对训练数据的学习效果太好，包括对噪声的学习效果，从而导致模型在新数据上表现不佳。==
==The ultimate goal is to have a model that not only learns well from the training data but can also generalize this learning to perform accurately on data it hasn’t seen before. Finding the right balance in layer depth is crucial for this; too few layers might underfit, while too many can overfit.====  
  
====我们的最终目标是建立一个模型，它不仅能很好地学习训练数据，还能将这种学习泛化到它以前从未见过的数据上，从而准确地执行任务。为此，在层深度上找到适当的平衡点至关重要；层数太少可能会拟合不足，而层数太多则可能会拟合过度。==
## ==4.2: Strategies for Testing and Selecting the Appropriate Depth====  
  
====4.2:测试和选择适当深度的策略==
==**Incremental Approach 渐进方法====  
  
====**Begin with a simpler model, then gradually add layers until you notice a significant improvement in validation performance. This approach helps in understanding the contribution of each layer to the overall performance.====  
  
====从较简单的模型开始，然后逐步增加层数，直到发现验证性能有明显改善。这种方法有助于了解每一层对整体性能的贡献。==
==Use the model’s performance on a validation set (a subset of the training data not used during training) as a benchmark for deciding whether adding more layers improves the model’s ability to generalize.====  
  
====以模型在验证集（训练过程中未使用的训练数据子集）上的表现为基准，决定增加更多层次是否能提高模型的泛化能力。==
==**Regularization Techniques====  
  
====正则化技术====  
  
====**Employ regularization methods like dropout or L2 regularization as you add more layers. These techniques can mitigate the risk of overfitting, allowing for a fair assessment of the added layers’ value to the model’s learning capacity.====  
  
====在添加更多层时，采用正则化方法（如 dropout 或 L2 正则化）。这些技术可以降低过度拟合的风险，从而公平地评估添加层对模型学习能力的价值。==
==**Observing Training Dynamics====  
  
====观察培训动态====  
  
====**Monitor the training and validation loss as you add more layers. A divergence between these two metrics — where training loss decreases but validation loss does not — might indicate overfitting, suggesting that the current depth might be excessive.====  
  
====在添加更多层时，监控训练和验证损失。如果这两个指标出现分歧，即训练损失减少而验证损失没有减少，则可能表明过度拟合，说明当前深度可能过大。==
[![](https://miro.medium.com/v2/resize:fit:700/1*YEILLescXv8_f2rKQ5-aFg.png)](https://miro.medium.com/v2/resize:fit:700/1*YEILLescXv8_f2rKQ5-aFg.png)
==Training and Validation Losses Plots — Image by Author====  
  
====训练和验证损失图 - 图片作者==
==The two graphs represent two different scenarios that can occur during the training of a machine learning model.====  
  
====这两幅图代表了机器学习模型训练过程中可能出现的两种不同情况。==
==In the first graph, both the training loss and the validation loss decrease and converge to a similar value. This is an ideal scenario, indicating that the model is learning and generalizing well. The model’s performance is improving on both the training data and unseen validation data. This suggests that the model is neither underfitting nor overfitting the data.====  
  
====在第一张图中，训练损失和验证损失都在减少，并收敛到一个相似的值。这是一种理想的情况，表明模型的学习和泛化效果良好。在训练数据和未见验证数据上，模型的性能都在提高。这表明模型既没有对数据拟合不足，也没有对数据拟合过度。==
==In the second graph, the training loss decreases, but the validation loss increases. This is a classic sign of overfitting. The model is learning the training data too well, including its noise and outliers, and is failing to generalize to unseen data. As a result, its performance on the validation data gets worse over time. This indicates that the model’s complexity may need to be reduced, or other techniques to prevent overfitting may need to be applied, such as regularization or dropout.====  
  
====在第二张图中，训练损失减少了，但验证损失却增加了。这是典型的过拟合迹象。该模型对训练数据（包括噪声和异常值）的学习效果太好，无法泛化到未见过的数据。因此，随着时间的推移，模型在验证数据上的表现越来越差。这表明可能需要降低模型的复杂度，或者应用其他技术来防止过拟合，例如正则化或剔除。==
==**Automated Architecture Search====  
  
====自动架构搜索====  
  
====**Utilize neural architecture search (NAS) tools or hyperparameter optimization frameworks like Optuna to explore different architectures systematically. These tools can automate the search for an optimal number of layers by evaluating numerous configurations and selecting the one that performs best on validation metrics.====  
  
====利用神经架构搜索（NAS）工具或超参数优化框架（如 Optuna）系统地探索不同的架构。这些工具可以通过评估众多配置，自动搜索最佳层数，并选择在验证指标上表现最佳的配置。==
==Determining the optimal number of layers in a neural network is a nuanced process that balances the model’s complexity with its ability to learn and generalize. By adopting a methodical approach to layer addition, employing cross-validation, and integrating regularization techniques, you can identify a network depth that suits your specific problem, optimizing your model’s performance on unseen data.====  
  
====确定神经网络的最佳层数是一个微妙的过程，需要在模型的复杂性与其学习和泛化能力之间取得平衡。通过有条不紊地增加层数、采用交叉验证和整合正则化技术，您可以确定适合特定问题的网络深度，优化模型在未见数据上的性能。==
## ==5: Automated Fine-Tuning with Optuna====  
  
====5: 使用 Optuna 进行自动微调==
==Fine-tuning neural networks to achieve optimal performance involves a delicate balance of various hyperparameters, which can often feel like finding a needle in a haystack due to the vast search space. This is where automated hyperparameter optimization tools like Optuna come into play.====  
  
====微调神经网络以获得最佳性能涉及到各种超参数之间的微妙平衡，由于搜索空间巨大，这常常让人感觉像大海捞针。这就是 Optuna 等自动超参数优化工具发挥作用的地方。==
## ==5.1: Introduction to Optuna====  
  
====5.1:Optuna 简介==
==Optuna is an open-source optimization framework designed to automate the selection of optimal hyperparameters. It simplifies the complex task of identifying the best combination of parameters that lead to the most efficient neural network model. Here, Optuna employs sophisticated algorithms to explore the hyperparameter space more effectively, reducing both the computational resources required and the time to convergence.====  
  
====Optuna 是一个开源优化框架，旨在自动选择最佳超参数。它简化了确定最佳参数组合以建立最高效神经网络模型的复杂任务。在这里，Optuna 采用了复杂的算法来更有效地探索超参数空间，从而减少了所需的计算资源和收敛时间。==
## ==5.2: Integrating Optuna for Neural Network Optimization====  
  
====5.2: 整合 Optuna 实现神经网络优化==
==Optuna uses a variety of strategies, such as Bayesian optimization, tree-structured Parzen estimators, and even evolutionary algorithms, to intelligently navigate the hyperparameter space. This approach allows Optuna to quickly hone in on the most promising hyperparameters, significantly speeding up the optimization process.====  
  
====Optuna 采用贝叶斯优化、树状结构 Parzen 估计器甚至进化算法等多种策略，智能地浏览超参数空间。这种方法使 Optuna 能够快速找到最有前途的超参数，从而大大加快优化过程。==
==Integrating Optuna into the neural network training workflow involves defining an objective function that Optuna will aim to minimize or maximize. This function typically includes the model training and validation process, with the goal being to minimize the validation loss or maximize validation accuracy.====  
  
====将 Optuna 集成到神经网络训练工作流程中，需要定义一个目标函数，Optuna 将以该函数最小化或最大化为目标。该函数通常包括模型训练和验证过程，目标是最小化验证损失或最大化验证精度。==
- ==**Defining the Search Space**====: You specify the range of values for each hyperparameter (e.g., number of layers, learning rate, dropout rate) that Optuna will explore.====  
      
    ====定义搜索空间：您可以指定 Optuna 将探索的每个超参数（如层数、学习率、辍学率）的取值范围。==
- ==**Trial and Evaluation**====: Optuna conducts trials, each time selecting a new set of hyperparameters to train the model. It evaluates the model’s performance on a validation set and uses this information to guide the search.====  
      
    ====试验和评估：Optuna 会进行试验，每次都会选择一组新的超参数来训练模型。它在验证集上评估模型的性能，并利用这些信息指导搜索。==
## ==5.3: Practical Implementation====  
  
====5.3:具体实施==
==import optuna==
==def objective(trial):==
```plain
n\_layers = trial.suggest\_int('n\_layers', 1, 10)  
hidden\_sizes = \[trial.suggest\_int(f'hidden\_size\_{i}', 32, 128) for i in range(n\_layers)\]  
dropout\_rate = trial.suggest\_uniform('dropout\_rate', 0.0, 0.5)    
learning\_rate = trial.suggest\_loguniform('learning\_rate', 1e-3, 1e-1)  
init\_method = trial.suggest\_categorical('init\_method', \['glorot\_uniform', 'glorot\_normal', 'he\_uniform', 'he\_normal', 'random'\])  
clip\_type = trial.suggest\_categorical('clip\_type', \['value', 'norm'\])  
clip\_value = trial.suggest\_uniform('clip\_value', 0.0, 1.0)  
epochs = 10000
layers = \[input\_size\] + hidden\_sizes + \[output\_size\]
  
nn = NeuralNetwork(layers=layers, loss\_func=loss\_func, dropout\_rate=dropout\_rate, init\_method=init\_method, clip\_type=clip\_type, grad\_clip=clip\_value)  
trainer = Trainer(nn, loss\_func)  
trainer.train(X\_train, y\_train, X\_test, y\_test, epochs, learning\_rate, early\_stopping=False)
  
predictions = np.argmax(nn.forward(X\_test), axis=1)  
accuracy = np.mean(predictions == y\_test\_labels)
return accuracy
```
==study = optuna.create_study(study_name='nn_study', direction='maximize')====  
  
====study.optimize(objective, n_trials=100)==
==print(f"Best trial: {study.best_trial.params}")====  
  
====print(f"Best value: {study.best_trial.value:.3f}")==
==The core of the Optuna optimization process is the== ==`objective`== ==function, which defines the trial's objective and is called by Optuna for each trial.====  
  
====Optuna 优化过程的核心是== ==`objective`== ==函数，该函数定义了试验的目标，Optuna 会为每次试验调用该函数。==
==Here====`n_layers`== ==is the number of hidden layers in the neural network, suggested between 1 and 10. Varying the number of layers allows exploration of shallow versus deep network architectures.====  
  
====`n_layers`== ==是神经网络的隐藏层数，建议在 1 到 10 之间。改变层数可以探索浅层和深层网络架构。==
==`hidden_sizes`== ==stores the size (number of neurons) for each layer, suggesting a number between 32 and 128, allowing the model to explore different capacities.====  
  
====`hidden_sizes`== ==存储每层的大小（神经元数量），建议在 32 到 128 之间，以便模型探索不同的容量。==
==`dropout_rate`== ==is uniformly suggested between 0.0 (no dropout) and 0.5, enabling regularization flexibility across trials.====  
  
====`dropout_rate`== ==统一建议介于 0.0（无遗漏）和 0.5 之间，以便在不同试验中灵活正则化。==
==`learning_rate`== ==is suggested on a log scale between 1e-3 and 1e-1, ensuring a wide search space that spans orders of magnitude, which is common for learning rate optimization due to its sensitivity.====  
  
====`learning_rate`== ==建议采用 1e-3 和 1e-1 之间的对数尺度，以确保具有跨越数量级的广阔搜索空间，由于学习率的敏感性，这在学习率优化中很常见。==
==`init_method`== ==for the neural network weights, chosen from a set of common strategies. This choice affects the starting point of training and thus the convergence behavior.====  
  
====`init_method`== ==为神经网络权重，从一组常用策略中选择。这种选择会影响训练的起点，从而影响收敛行为。==
==`clip_type`== ==and== ==`clip_value`== ==define the gradient clipping strategy and value, helping to prevent exploding gradients by either clipping by value or norm.====  
  
====`clip_type`== ==和 定义了梯度剪切策略和值，有助于防止通过值或规范剪切造成梯度爆炸。== ==`clip_value`==
==Then, the====`NeuralNetwork`== ==instance is created and trained using the defined hyperparameters. Note that early stopping is disabled to allow each trial to run for a fixed number of epochs, ensuring consistent comparison. The performance is evaluated based on the accuracy of the model’s predictions on the test set.====  
  
====然后，创建== ==`NeuralNetwork`== ==实例，并使用定义的超参数进行训练。需要注意的是，为了让每次试验都能运行固定数量的历时（epochs），我们禁用了提前停止功能，以确保比较结果的一致性。性能评估基于模型对测试集预测的准确性。==
==Once the objective function and the== ==`NeuralNetwork`== ==instance are defined, we can move on to the Optuna study, whose object is created to maximize the objective function (====`'maximize'`====), which in this context is the accuracy of the neural network.====  
  
====一旦定义了目标函数和== ==`NeuralNetwork`== ==实例，我们就可以开始 Optuna 研究，其目标是最大化目标函数 (== ==`'maximize'`== ==)，在本例中就是神经网络的精确度。==
==The study calls the== ==`objective`== ==function multiple times (====`n_trials=100`====), each time with a different set of hyperparameters suggested by Optuna's internal optimization algorithms. Optuna intelligently adjusts its suggestions based on the history of trials to explore the hyperparameter space efficiently.====  
  
====研究多次调用== ==`objective`== ==函数（== ==`n_trials=100`== ==），每次都使用 Optuna 内部优化算法建议的一组不同的超参数。Optuna 会根据试验历史智能调整建议，以有效探索超参数空间。==
==The process yields the best set of hyperparameters found across all trials (====`study.best_trial.params`====) and the highest accuracy achieved (====`study.best_trial.value`====). This output provides insights into the optimal configuration of the neural network for the task at hand.====  
  
====这一过程会产生在所有试验中找到的最佳超参数集 (== ==`study.best_trial.params`== ==) 和达到的最高准确率 (== ==`study.best_trial.value`== ==) 。这一结果有助于深入了解神经网络在当前任务中的最佳配置。==
## ==5.4: Benefits and Outcomes====  
  
====5.4: 效益和成果==
==By integrating Optuna, developers can not only automate the hyperparameter tuning process but also gain deeper insights into how different parameters affect their models. This leads to more robust and accurate neural networks, optimized in a fraction of the time it would take through manual experimentation.====  
  
====通过集成 Optuna，开发人员不仅可以实现超参数调整过程的自动化，还能深入了解不同参数对模型的影响。这将带来更强大、更准确的神经网络，而优化所需的时间仅为人工实验所需时间的一小部分。==
==Optuna’s systematic approach to fine-tuning brings a new level of precision and efficiency to neural network development, empowering developers to achieve higher performance standards and push the boundaries of what their models can accomplish.====  
  
====Optuna 的系统化微调方法将神经网络开发的精度和效率提升到了一个新的水平，使开发人员能够达到更高的性能标准，并突破其模型所能达到的极限。==
## ==5.5: Limitations 5.5: 局限性==
==While Optuna offers a powerful and flexible approach to hyperparameter optimization, several limitations and considerations should be acknowledged when integrating it into machine learning workflows:====  
  
====虽然 Optuna 为超参数优化提供了一种强大而灵活的方法，但在将其集成到机器学习工作流中时，仍需注意一些局限性和注意事项：==
==**Computational Resources 计算资源====  
  
====**Each trial involves training a neural network from scratch, which can be computationally expensive, especially with deep networks or large datasets. Running hundreds or thousands of trials to explore the hyperparameter space thoroughly can require significant computational resources and time.====  
  
====每次试验都需要从头开始训练一个神经网络，这可能会耗费大量计算资源，尤其是对于深度网络或大型数据集而言。运行成百上千次试验以彻底探索超参数空间可能需要大量的计算资源和时间。==
==**Hyperparameter Search Space====  
  
====超参数搜索空间====  
  
====**The effectiveness of Optuna’s search depends heavily on how the search space is defined. If the range of values for hyperparameters is too broad or not properly aligned with the problem, Optuna might spend time exploring suboptimal regions. Conversely, too narrow a search space might miss the optimal configurations.====  
  
====Optuna 的搜索效果在很大程度上取决于搜索空间的定义。如果超参数的取值范围过宽或与问题不匹配，Optuna 可能会花费时间探索次优区域。反之，搜索空间太窄可能会错过最佳配置。==
==As the number of hyperparameters increases, the search space grows exponentially, a phenomenon known as the “curse of dimensionality.” This can make it challenging for Optuna to efficiently navigate the space and find the best hyperparameters within a reasonable number of trials.====  
  
====随着超参数数量的增加，搜索空间呈指数增长，这种现象被称为 "维度诅咒"。这使得 Optuna 要在合理的试验次数内有效地浏览空间并找到最佳超参数变得非常困难。==
==**Evaluation Metrics 评估指标====  
  
====**The choice of the objective function and evaluation metrics can significantly impact the outcomes of optimization. Metrics that do not adequately capture the model’s performance or objectives of the task might lead to suboptimal hyperparameter configurations.====  
  
====目标函数和评价指标的选择会对优化结果产生重大影响。不能充分体现模型性能或任务目标的指标可能会导致次优的超参数配置。==
==The performance evaluation of a model can vary due to factors like random initialization, data shuffling, or inherent noise in the dataset. This variability can introduce noise into the optimization process, potentially affecting the reliability of the results.====  
  
====模型的性能评估可能会因随机初始化、数据洗牌或数据集中的固有噪声等因素而变化。这种变化会在优化过程中引入噪音，从而可能影响结果的可靠性。==
==**Algorithmic Limitations 算法限制====  
  
====**Optuna uses sophisticated algorithms to navigate the search space, but the efficiency and effectiveness of these algorithms can vary depending on the problem. In some cases, certain algorithms might converge to local optima or require adjustment in their settings to better suit the specific characteristics of the hyperparameter space.====  
  
====Optuna 使用复杂的算法来引导搜索空间，但这些算法的效率和效果可能因问题而异。在某些情况下，某些算法可能会收敛到局部最优状态，或者需要调整其设置以更好地适应超参数空间的具体特征。==
## ==6: Conclusion 6: 结束语==
==As we wrap up our deep dive into fine-tuning neural networks, it’s a good moment to look back on the path we’ve traveled. We started with the basics of how neural networks function and steadily progressed to more sophisticated techniques that boost their performance and efficiency.====  
  
====在我们结束对微调神经网络的深入研究之际，不妨回顾一下我们走过的道路。我们从神经网络的基本功能入手，逐步掌握了更复杂的技术，从而提高了神经网络的性能和效率。==
## ==6.1: What’s Next 6.1:下一步行动==
==While we’ve covered a lot of ground in optimizing neural networks, it’s clear we’ve only scratched the surface. The landscape of neural network optimization is vast and continuously evolving, brimming with techniques and strategies we haven’t yet explored. In our upcoming articles, we’re set to dive deeper, exploring more complex neural network architectures and the advanced techniques that can unlock even higher levels of performance and efficiency.====  
  
====虽然我们在优化神经网络方面已经做了很多工作，但很明显，我们只是触及了皮毛。神经网络优化的领域十分广阔，而且还在不断发展，其中充满了我们尚未探索的技术和策略。在接下来的文章中，我们将深入探讨更复杂的神经网络架构，以及能够释放更高性能和效率的先进技术。==
==There’s a whole array of optimization techniques and concepts we plan to delve into, including:====  
  
====我们计划深入研究一系列优化技术和概念，包括==
- ==**Batch Normalization**====: A method that helps speed up training and improves stability by normalizing the input layer by adjusting and scaling the activations.====  
      
    ====批量归一化：这是一种通过调整和缩放激活值对输入层进行归一化处理的方法，有助于加快训练速度并提高稳定性。==
- ==**Optimization algorithms**====: including SGD and Adam, provide us with tools to navigate the complex landscape of the loss function more effectively, ensuring more efficient training cycles and better model performance.====  
      
    ====优化算法：包括 SGD 和 Adam，为我们提供了更有效地驾驭复杂的损失函数的工具，确保更高效的训练周期和更好的模型性能。==
- ==**Transfer Learning and Fine-Tuning**====: Leveraging pre-trained models and adapting them to new tasks can drastically reduce training time and improve model accuracy on tasks with limited data.====  
      
    ====迁移学习和微调：利用预先训练好的模型并使其适应新任务，可以大大缩短训练时间，并提高模型在数据有限的任务中的准确性。==
- ==**Neural Architecture Search (NAS)**====: Using automation to discover the best architecture for a neural network, potentially uncovering efficient models that might not be intuitive to human designers.====  
      
    ====神经架构搜索（NAS）：利用自动化发现神经网络的最佳架构，从而发现人类设计者可能无法直观感知的高效模型。==
==These topics represent just a taste of what’s out there, each offering unique advantages and challenges. As we move forward, we aim to unpack these techniques, providing insights into how they work, when to use them, and the impact they can have on your neural network projects.====  
  
====这些主题仅代表了现有技术的一部分，每种技术都具有独特的优势和挑战。在今后的工作中，我们将继续深入探讨这些技术，深入了解它们的工作原理、使用时机以及对神经网络项目的影响。==
## ==Further Resources 更多资源==
- ==**“Deep Learning” by Ian Goodfellow, Yoshua Bengio, and Aaron Courville**====: This comprehensive text offers an in-depth overview of deep learning techniques and principles, including advanced neural network architectures and optimization methods.====  
      
    ===="深度学习》（Deep Learning），作者：Ian Goodfellow、Yoshua Bengio 和 Aaron Courville：这本内容全面的教科书深入概述了深度学习技术和原理，包括先进的神经网络架构和优化方法。==
- ==**“Neural Networks and Deep Learning: A Textbook” by Charu C. Aggarwal**====: This book provides a detailed exploration of neural networks, with a focus on deep learning and its applications. It’s an excellent resource for understanding complex concepts in neural network design and optimization.====  
      
    ===="神经网络与深度学习》：教科书》，作者：Charu C. AggarwalAggarwal 著：本书详细探讨了神经网络，重点介绍了深度学习及其应用。它是理解神经网络设计和优化中复杂概念的绝佳资源。==
==You made it to the end. Congrats! I hope you enjoyed this article, if so consider leaving a like and following me, as I will regularly post similar articles. My goal is to recreate all the most popular algorithms from scratch and make machine learning accessible to everyone.====  
  
====你坚持到了最后。恭喜你！希望你喜欢这篇文章，如果喜欢，请点赞并关注我，因为我会定期发布类似文章。我的目标是从头开始重新创建所有最流行的算法，让每个人都能接触到机器学习。==