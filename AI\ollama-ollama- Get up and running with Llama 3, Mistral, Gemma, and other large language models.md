---
Updated: 2024-05-04T19:20
tags:
  - AI->-LocalLLMUI
  - AI->-llama
Created: 2024-05-04T19:20
---
[![](https://opengraph.githubassets.com/251f00203077273525dc4f3a7e5f5eeb651699f60d307e54351abbb852d2714f/ollama/ollama)](https://opengraph.githubassets.com/251f00203077273525dc4f3a7e5f5eeb651699f60d307e54351abbb852d2714f/ollama/ollama)
[![](https://private-user-images.githubusercontent.com/3325447/*********-8dc9c472-9d72-4b39-95ae-2c85ada375b9.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MTQ4MTgyNzgsIm5iZiI6MTcxNDgxNzk3OCwicGF0aCI6Ii8zMzI1NDQ3LzMyNjk1MDIxMy04ZGM5YzQ3Mi05ZDcyLTRiMzktOTVhZS0yYzg1YWRhMzc1YjkucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI0MDUwNCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNDA1MDRUMTAxOTM4WiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9ZWM2NWFmYjlkZWNmMzdkZDFiMmI0ZTQ0MGIxYTA3MWRmYmQ5YzUyYzE0MjI2OThhYzUxYWFjNTdlODZkYmNmYSZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QmYWN0b3JfaWQ9MCZrZXlfaWQ9MCZyZXBvX2lkPTAifQ.iRl76w7t-ymmzbOF8_CGl56_ia6O9tBrKV8K2anFZIU)](https://private-user-images.githubusercontent.com/3325447/*********-8dc9c472-9d72-4b39-95ae-2c85ada375b9.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MTQ4MTgyNzgsIm5iZiI6MTcxNDgxNzk3OCwicGF0aCI6Ii8zMzI1NDQ3LzMyNjk1MDIxMy04ZGM5YzQ3Mi05ZDcyLTRiMzktOTVhZS0yYzg1YWRhMzc1YjkucG5nP1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9QUtJQVZDT0RZTFNBNTNQUUs0WkElMkYyMDI0MDUwNCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNDA1MDRUMTAxOTM4WiZYLUFtei1FeHBpcmVzPTMwMCZYLUFtei1TaWduYXR1cmU9ZWM2NWFmYjlkZWNmMzdkZDFiMmI0ZTQ0MGIxYTA3MWRmYmQ5YzUyYzE0MjI2OThhYzUxYWFjNTdlODZkYmNmYSZYLUFtei1TaWduZWRIZWFkZXJzPWhvc3QmYWN0b3JfaWQ9MCZrZXlfaWQ9MCZyZXBvX2lkPTAifQ.iRl76w7t-ymmzbOF8_CGl56_ia6O9tBrKV8K2anFZIU)
## ==New models:==
- [==Llama 3==](https://ollama.com/library/llama3)==: a new model by Meta, and the most capable openly available LLM to date==
- [==Phi 3 Mini==](https://ollama.com/library/phi3)==: a new 3.8B parameters, lightweight, state-of-the-art open model by Microsoft.==
- [==Moondream==](https://ollama.com/library/moondream) ==moondream is a small vision language model designed to run efficiently on edge devices.==
- [==Llama 3 Gradient 1048K==](https://ollama.com/library/llama3-gradient)==: A Llama 3 fine-tune by Gradient to support up to a 1M token context window.==
- [==Dolphin Llama 3==](https://ollama.com/library/dolphin-llama3)==: The uncensored Dolphin model, trained by Eric Hartford and based on Llama 3 with a variety of instruction, conversational, and coding skills.==
- [==Qwen 110B==](https://ollama.com/library/qwen:110b)==: The first Qwen model over 100B parameters in size with outstanding performance in evaluations==
- [==Llama 3 Gradient==](https://ollama.com/library/llama3-gradient)==: A fine-tune of Llama 3 the supports a context window of up 1M tokens.==
## ==What's Changed==
- ==Fixed issues where the model would not terminate, causing the API to hang.==
- ==Fixed a series of out of memory errors on Apple Silicon Macs==
- ==Fixed out of memory errors when running Mixtral architecture models==
## ==Experimental concurrency features==
==New concurrency features are coming soon to Ollama. They are available==
- ==`OLLAMA_NUM_PARALLEL`====: Handle multiple requests simultaneously for a single model==
- ==`OLLAMA_MAX_LOADED_MODELS`====: Load multiple models simultaneously==
==To enable these features, set the environment variables for== ==`ollama serve`====. For more info see== [==this guide==](https://github.com/ollama/ollama/blob/main/docs/faq.md#how-do-i-configure-ollama-server)==:==
```plain
OLLAMA_NUM_PARALLEL=4 OLLAMA_MAX_LOADED_MODELS=4 ollama serve
```
## ==New Contributors==
- [==@hmartinez82==](https://github.com/hmartinez82) ==made their first contribution in== [==#3972==](https://github.com/ollama/ollama/pull/3972)
- [==@Cephra==](https://github.com/Cephra) ==made their first contribution in== [==#4037==](https://github.com/ollama/ollama/pull/4037)
- [==@arpitjain099==](https://github.com/arpitjain099) ==made their first contribution in== [==#4007==](https://github.com/ollama/ollama/pull/4007)
- [==@MarkWard0110==](https://github.com/MarkWard0110) ==made their first contribution in== [==#4031==](https://github.com/ollama/ollama/pull/4031)
- [==@alwqx==](https://github.com/alwqx) ==made their first contribution in== [==#4073==](https://github.com/ollama/ollama/pull/4073)
- [==@sidxt==](https://github.com/sidxt) ==made their first contribution in== [==#3705==](https://github.com/ollama/ollama/pull/3705)
- [==@ChengenH==](https://github.com/ChengenH) ==made their first contribution in== [==#3789==](https://github.com/ollama/ollama/pull/3789)
- [==@secondtruth==](https://github.com/secondtruth) ==made their first contribution in== [==#3503==](https://github.com/ollama/ollama/pull/3503)
- [==@reid41==](https://github.com/reid41) ==made their first contribution in== [==#3612==](https://github.com/ollama/ollama/pull/3612)
- [==@ericcurtin==](https://github.com/ericcurtin) ==made their first contribution in== [==#3626==](https://github.com/ollama/ollama/pull/3626)
- [==@JT2M0L3Y==](https://github.com/JT2M0L3Y) ==made their first contribution in== [==#3633==](https://github.com/ollama/ollama/pull/3633)
- [==@datvodinh==](https://github.com/datvodinh) ==made their first contribution in== [==#3655==](https://github.com/ollama/ollama/pull/3655)
- [==@MapleEve==](https://github.com/MapleEve) ==made their first contribution in== [==#3817==](https://github.com/ollama/ollama/pull/3817)
- [==@swuecho==](https://github.com/swuecho) ==made their first contribution in== [==#3810==](https://github.com/ollama/ollama/pull/3810)
- [==@brycereitano==](https://github.com/brycereitano) ==made their first contribution in== [==#3895==](https://github.com/ollama/ollama/pull/3895)
- [==@bsdnet==](https://github.com/bsdnet) ==made their first contribution in== [==#3889==](https://github.com/ollama/ollama/pull/3889)
- [==@fyxtro==](https://github.com/fyxtro) ==made their first contribution in== [==#3855==](https://github.com/ollama/ollama/pull/3855)
- [==@natalyjazzviolin==](https://github.com/natalyjazzviolin) ==made their first contribution in== [==#3962==](https://github.com/ollama/ollama/pull/3962)
==**Full Changelog**====:== [==v0.1.32...v0.1.33==](https://github.com/ollama/ollama/compare/v0.1.32...v0.1.33)