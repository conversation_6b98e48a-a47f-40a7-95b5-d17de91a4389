---
Updated: 2023-11-07T23:06
tags:
  - AI->-Business
  - AI->-Programming
  - AI->-TimeSeries
Created: 2023-11-07T23:06
---
[![](https://opengraph.githubassets.com/45fbed9de5d05772005b5f15bb520407dd6bee992f159a64e82ac4e9e2749b0c/ferrygun/AutoGen_StockPrice-Chart)](https://opengraph.githubassets.com/45fbed9de5d05772005b5f15bb520407dd6bee992f159a64e82ac4e9e2749b0c/ferrygun/AutoGen_StockPrice-Chart)
## ==Create list==
==Create a list to organize your starred repositories.==
==Name .==
==32 remaining==
==Description .==
==160 remaining==
==.==
==Create==
==**Tip:**== ==type== ==`:`== ==to add emoji to the name or description.==
==Beta Lists are currently in beta.== [==Share feedback and report bugs.==](https://github.com//github/feedback/discussions/categories/lists)
==**== [==AutoGen_StockPrice-Chart==](https://github.com/ferrygun/AutoGen_StockPrice-Chart) ==** Public==
[![](https://avatars.githubusercontent.com/u/3617343?s=48&v=4)](https://avatars.githubusercontent.com/u/3617343?s=48&v=4)
- ==Unwatch Stop ignoring Watch 1==
    
    ### ==Notifications==
    
    ==Get push notifications on== [==iOS==](https://apps.apple.com/app/apple-store/id1477376905?ct=watch-dropdown&mt=8&pt=524675) ==or== [==Android==](https://play.google.com/store/apps/details?id=com.github.android&referrer=utm_campaign%3Dwatch-dropdown%26utm_medium%3Dweb%26utm_source%3Dgithub)==.==
    
- [==Fork 0==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/fork) ==Fork your own copy of ferrygun/AutoGen_StockPrice-Chart==
==Notification settings==
==AutoGen_StockPrice-Chart==
[==1 star==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/stargazers) [==0 forks==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/forks)
==1 watching==
[==Activity==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/activity)
==Public repository==
[==Open in github.dev==](https://github.dev/) [==Open in a new github.dev tab==](https://github.dev/) [==Open in codespace==](https://github.com/codespaces/new/ferrygun/AutoGen_StockPrice-Chart?resume=1)
==This commit does not belong to any branch on this repository, and may belong to a fork outside of the repository.==
==main==
==Switch branches/tags==
==Could not load branches==
==Nothing to show==
==[{{ refName }} default](https://github.com/ferrygun/AutoGen_StockPrice-Chart/tree/{{ urlEncodedRefName }})==
==Could not load tags==
==Nothing to show==
==[{{ refName }} default](https://github.com/ferrygun/AutoGen_StockPrice-Chart/tree/{{ urlEncodedRefName }})==
[==**1**==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/branches) [==branch==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/branches) [==**0**==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/tags) [==tags==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/tags)
[==Go to file==](https://github.com/ferrygun/AutoGen_StockPrice-Chart?search=1)
==Add file==
- [==Go to file==](https://github.com/ferrygun/AutoGen_StockPrice-Chart?search=1)
- [==Upload files==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/upload/main)
==Code==
- ==Clone==
    
    ==Use Git or checkout with SVN using the web URL.==
    
    ==Use a password-protected SSH key.==
    
    ==Work fast with our official CLI.== [==Learn more about the CLI==](https://cli.github.com/)==.==
    
- [==Open with GitHub Desktop==](https://desktop.github.com/)
- [==Download ZIP==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/archive/refs/heads/main.zip)
### ==Launching GitHub Desktop==
==If nothing happens,== [==download GitHub Desktop==](https://desktop.github.com/) ==and try again.==
### ==Launching GitHub Desktop==
==If nothing happens,== [==download GitHub Desktop==](https://desktop.github.com/) ==and try again.==
### ==Launching Xcode==
==If nothing happens,== [==download Xcode==](https://developer.apple.com/xcode/) ==and try again.==
### ==Launching Visual Studio Code==
==Your codespace will open once ready.==
==There was a problem preparing your codespace, please try again.==
[==Branches==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/branches) [==Tags==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/tags)
## ==Latest commit==
[==ferrygun==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/commits?author=ferrygun) [==Create stock_chart.html==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/commit/788aa968bc0433e5eb39fae4f1c06628e8e7ec09)
[==788aa96==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/commit/788aa968bc0433e5eb39fae4f1c06628e8e7ec09)
[==Oct 19, 2023==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/commit/788aa968bc0433e5eb39fae4f1c06628e8e7ec09)
[==Create stock_chart.html==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/commit/788aa968bc0433e5eb39fae4f1c06628e8e7ec09)
==`788aa96`==
## ==Git stats==
- [==**4**==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/commits/main) [==commits==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/commits/main)
## ==Files==
[==Permalink==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/tree/788aa968bc0433e5eb39fae4f1c06628e8e7ec09)
==Failed to load latest commit information.==
==Type==
==Name==
==Latest commit message==
==Commit time==
[==AutoGen_StockPrice_Chart.ipynb==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/blob/main/AutoGen_StockPrice_Chart.ipynb)
[==Add files via upload==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/commit/b29bc4e3958e8193acb693d61d66e7add242d85d)
==October 20, 2023 09:31==
[==OAI_CONFIG_LIST==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/blob/main/OAI_CONFIG_LIST)
[==Create OAI_CONFIG_LIST==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/commit/8e82b8c6c963f54f5adcf8ebf1c600408d165c46)
==October 20, 2023 09:32==
[==README.md==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/blob/main/README.md)
[==Initial commit==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/commit/f1bdfbf95a29fa217737c2091f0e2154062871f2)
==October 20, 2023 09:30==
[==stock_chart.html==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/blob/main/stock_chart.html)
[==Create stock_chart.html==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/commit/788aa968bc0433e5eb39fae4f1c06628e8e7ec09)
==October 20, 2023 09:32==
## [==README.md==](https://github.com/ferrygun/AutoGen_StockPrice-Chart#readme)
## [==AutoGen_StockPrice-Chart==](https://github.com/ferrygun/AutoGen_StockPrice-Chart#autogen_stockprice-chart)
==AutoGen_StockPrice-Chart==
## ==About==
==AutoGen_StockPrice-Chart==
### ==Resources==
[==Readme==](https://github.com/ferrygun/AutoGen_StockPrice-Chart#readme)
[==Activity==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/activity)
### ==Stars==
[==**1**==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/stargazers) [==star==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/stargazers)
### ==Watchers==
[==**1**==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/watchers) [==watching==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/watchers)
### ==Forks==
[==**0**==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/forks) [==forks==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/forks)
[==Report repository==](https://github.com/contact/report-content?content_url=https%3A%2F%2Fgithub.com%2Fferrygun%2FAutoGen_StockPrice-Chart&report=ferrygun+%28user%29)
## [==Releases==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/releases)
==No releases published==
## [==Packages==](https://github.com/users/ferrygun/packages?repo_name=AutoGen_StockPrice-Chart)
==No packages published==
## ==Languages==
- [==Jupyter Notebook 77.0%==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/search?l=jupyter-notebook)
- [==HTML 23.0%==](https://github.com/ferrygun/AutoGen_StockPrice-Chart/search?l=html)