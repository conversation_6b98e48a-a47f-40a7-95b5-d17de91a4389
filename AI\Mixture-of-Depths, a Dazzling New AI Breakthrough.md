---
Updated: 2024-04-19T18:46
tags:
  - AI->-Theory
Created: 2024-04-19T18:46
---
[![](https://miro.medium.com/v2/da:true/resize:fit:1200/0*7LUbxU6KX9negNKd)](https://miro.medium.com/v2/da:true/resize:fit:1200/0*7LUbxU6KX9negNKd)
## ==Conditional Computing is Finally Here====  
  
====条件计算终于来了==
[![](https://miro.medium.com/v2/resize:fill:88:88/1*<EMAIL>)](https://miro.medium.com/v2/resize:fill:88:88/1*<EMAIL>)
[![](https://miro.medium.com/v2/resize:fit:1971/0*7LUbxU6KX9negNKd)](https://miro.medium.com/v2/resize:fit:1971/0*7LUbxU6KX9negNKd)
==Few times I’ve been more excited about a research paper, and let me tell you I read many.====  
  
====很少有时候我对一篇研究论文更感兴趣，让我告诉你我读了很多。==
==Presented by Google Deepmind and going by the name of== ==**Mixture-of-Depths (MoD)**====, this research paper has everything it takes to become a seminal piece for future generations of state-of-the-art models.====  
  
====这篇研究论文由Google Deepmind提出，名为Mixture-of-Depths（MoD），它具有成为未来几代最先进模型的开创性作品所需的一切。==
==_Its principle?_== ==Not all thoughts are made equal.====  
  
====其原则？并非所有的思想都是平等的。==
==In other words,== ==**MoD models can dynamically allocate compute to each prediction**====, just like a human would, tackling one of the greatest issues our current frontier models have.====  
  
====换句话说，MoD模型可以动态地为每个预测分配计算，就像人类一样，解决我们当前前沿模型面临的最大问题之一。==
==This probably won’t say much to you right now, but I guarantee you are going to be very excited by the end, as MoDs not only drastically reduce computing requirements to run models,== ==**but they present the opportunity to create smarter and more powerful ones.**====  
  
====这可能不会对你说太多，但我保证你会非常兴奋，因为MoD不仅大大降低了运行模型的计算需求，而且它们提供了创建更智能，更强大的模型的机会。==
==_And the best thing?_== ==This can be applied to every single LLM in the world.====  
  
====最棒的是？这可以适用于世界上的每一个LLM。==
## ==Not all Thoughts are Created Equal====  
  
====并非所有的思想都是平等的==
==Humans, when faced with a task, can decide how much thought, or effort, they will dedicate to the problem.====  
  
====人类在面对一项任务时，可以决定他们将在这个问题上投入多少思考或努力。==
==While some issues can be resolved quickly, even unconsciously, other tasks need your complete attention and focus.====  
  
====虽然有些问题可以很快解决，即使是无意识的，但其他任务需要你的全部注意力和焦点。==
==In short,== ==**humans allocate compute to a task depending on its predicted complexity.**====  
  
====简而言之，人类根据其预测的复杂性将计算分配给任务。==
==_But do AI models do the same?_== ==Hard no. Our current models dedicate the exact compute to every single prediction, no matter how hard — or easy — it is.====  
  
====但AI模型也能做到这一点吗？我们目前的模型致力于精确计算每一个预测，无论多么困难或容易。==
==This begs the question: if models allocate the same compute to the simpler tasks than to the hard tasks,== ==_could it be that AI models, specifically Transformers like ChatGPT, consume more compute than actually needed?_====  
  
====这就引出了一个问题：如果模型将相同的计算分配给简单的任务而不是硬任务，那么AI模型，特别是像ChatGPT这样的变形金刚，会消耗比实际需要更多的计算吗？==
==_What’s more, could this open the door to making them smarter?_== ==This is precisely what we are covering today.====  
  
====更重要的是，这是否可以打开让它们变得更聪明的大门？这正是我们今天要讨论的问题。==
## ==Letting Models Decide 让模型决定==
==First, we need to clarify how Transformer LLMs work to understand Mixture-of-Depths.====  
  
====首先，我们需要澄清Transformer LLMs如何工作以理解深度混合。==
==We are focusing on Transformer-only LLMs, as the paper only focuses on those. But you could potentially apply MoDs to other architectures like== [==Mamba==](https://arxiv.org/pdf/2312.00752.pdf)==, or== [==Hyena==](https://arxiv.org/pdf/2302.10866.pdf)==.====  
  
====我们只关注Transformer-only LLMs，因为本文只关注这些。但是你可以将MoD应用到其他架构中，比如Mamba或Hyena。==
## ==Everyone Gets Attended 人人都有份==
==Transformer models, with prominent examples like== ==**ChatGPT**====,== ==**Gemini**====, or== ==**Claude**====, are sequence-to-sequence models that receive an input sequence, like text, and output another sequence, usually the continuation of that text.====  
  
====Transformer模型，例如ChatGPT、Gemini或Claude，是序列到序列模型，它接收输入序列（如文本），并输出另一个序列（通常是该文本的延续）。==
==They are made by a concatenation of ‘Transformer blocks’, which have two main layers:====  
  
====它们是由“Transformer块”串联而成的，这些块有两个主要层：==
1. ==A== ==**multi-head attention layer**====, to compute the attention mechanism====  
      
    ====一个多头注意层，计算注意机制==
2. ==A== ==**Feedforward Layer (FFN)**====, to improve feature extraction====  
      
    ====前馈层（FFN），用于改进特征提取==
==You stack these blocks one after the other, and in the last layer of the model, you rank all the possible words that can be outputted and choose one of the top-k most reasonable continuations to the sequence.====  
  
====你将这些块一个接一个地堆叠起来，在模型的最后一层，你对所有可能输出的单词进行排名，并选择前k个最合理的延续序列之一。==
==The resulting model looks something as below, where a series of concatenated Transformer blocks process the input sequence, and the last layer, depicted below as ‘Softmax’, chooses the next word.====  
  
====生成的模型如下所示，其中一系列串联的Transformer块处理输入序列，最后一层（下面描述为“Softmax”）选择下一个单词。==
==Although the exact architecture varies between models, the overall procedure is always as below.====  
  
====尽管不同型号的具体架构有所不同，但总体过程始终如下所示。==
[![](https://miro.medium.com/v2/resize:fit:770/1*y1BeibrPR6YLL_hHXSuYCA.png)](https://miro.medium.com/v2/resize:fit:770/1*y1BeibrPR6YLL_hHXSuYCA.png)
[==Source 源==](https://www.researchgate.net/figure/Decoder-only-Transformer-architecture-The-input-to-the-decoder-is-tokenized-text-and_fig2_373183262)
==The image above contains several components that aren’t addressed in this article.== [==For a higher intuition of what attention is and how it works, click here==](https://thewhitebox.ai/the-attention-mechanism/)==_._====  
  
====上面的图片包含了几个本文没有提到的组件。想要更直观地了解注意力是什么以及它是如何工作的，请点击这里。==
==_But why do we need many blocks?_====  
  
====_但是为什么我们需要很多块呢？_==
==Well, the more blocks you have, aka the larger the model’s depth is,== ==**the more nuances it can capture on the relationships between words.**====  
  
====好吧，你有越多的块，也就是模型的深度越大，它就能捕捉到单词之间关系的更多细微差别。==
==Recent research might suggest that we might be exceeding the depths of our models.== [==This article==](https://medium.com/gitconnected/llm-redundancy-it-is-time-for-a-massive-layoff-of-layers-948827aa926e) ==by====  
  
====最近的研究可能表明，我们可能超出了模型的深度。本文由==
==will give you more intuition on this.====  
  
====会给予你更多的直觉==
==That being said, depth is still a key part of creating powerful AI models, we simply aren’t that good today at finding the ‘right depth’.====  
  
====话虽如此，深度仍然是创建强大AI模型的关键部分，我们今天根本不擅长找到“正确的深度”。==
==Overall, the essence of the Transformer is that over this concatenation of blocks, the different words in the sequence are updated with information provided by other words.====  
  
====总的来说，Transformer的本质是，在块的级联上，序列中的不同单词用其他单词提供的信息进行更新。==
==For example, if I ask you what the word ‘bat’ means, you will answer that ‘it depends on the context’.====  
  
====例如，如果我问你“蝙蝠”这个词是什么意思，你会回答说“这取决于上下文”。==
==Well, that’s precisely what attention does, it updates the value of all words in a sequence so that they can account for their surrounding context (words), so that the meaning of ‘bat’ becomes an animal or a baseball club depending on the situation.====  
  
====好吧，这正是注意力所做的，它更新了序列中所有单词的值，这样它们就可以解释它们周围的上下文（单词），所以“bat”的含义根据情况变成了动物或棒球俱乐部。==
[==**For a more detailed explanation of Transformers, read here.**==](https://thewhitebox.ai/transformers-the-great-paradigm-shift/)==  
  
==[==**关于变形金刚的更详细的解释，请阅读这里。**==](https://thewhitebox.ai/transformers-the-great-paradigm-shift/)
==But here’s the thing. 但问题是==
==With standard attention, every word in the sequence gets to attend to every single other word, which is very expensive and, maybe…== ==_totally unnecessary?_====  
  
====有了标准的注意力，序列中的每个词都要注意其他每个词，这是非常昂贵的，也许.完全没有必要？==
==And here is where== [==MoD==](https://arxiv.org/pdf/2404.02258.pdf) ==comes in.====  
  
====这就是国防部的用武之地。==
## ==A Routing Problem 一个路由问题==
==In simple terms, what MoD does is, for every word in a sequence and every block in the model, decide if that word gets updated or not. This can be visually seen in the image below.====  
  
====简单地说，MoD所做的就是，对于序列中的每个单词和模型中的每个块，决定该单词是否更新。这可以在下面的图片中直观地看到。==
==‘Updated’ means that the word undergoes the attention process we described earlier.====  
  
====“更新”意味着这个词经历了我们前面描述的注意过程。==
[![](https://miro.medium.com/v2/resize:fit:770/0*Q07wf2wChVi7rplM)](https://miro.medium.com/v2/resize:fit:770/0*Q07wf2wChVi7rplM)
==Source: Google 来源：Google==
==Here ‘MLP’ is used analogously to the feedforward layer.====  
  
====这里，“MLP”类似于前馈层使用。==
==As shown above, before a Transformer block, every token — word or subword — is inserted into a ‘router’, which assigns it a weight.====  
  
====如上所示，在一个Transformer块之前，每个标记（字或子字）都被插入到一个“路由器”中，该路由器为其分配一个权重。==
==**This weight determines how important that word is to that block**====, which is akin to saying== ==_“This word should either attend other words or be attended to”_====. If not chosen among the preferable ones, the router is suggesting that the token in question is irrelevant in that case.====  
  
====这个权重决定了这个词对这个块的重要性，这类似于说“这个词应该加入其他词或者被加入”。如果没有在优选的令牌中选择，则路由器建议所讨论的令牌在这种情况下是不相关的。==
==Importantly, this process is entirely predictable. By imposing a ‘compute budget’ for a block,== ==**we can determine the exact number of words that the router chooses to compute.**====  
  
====重要的是，这个过程是完全可以预测的。通过对一个块施加“计算预算”，我们可以确定路由器选择计算的确切单词数。==
==This means that we have a static computation graph. In layman’s terms, we know the exact compute we will consume for every prediction.====  
  
====这意味着我们有一个静态计算图。通俗地说，我们知道每个预测将消耗的确切计算量。==
==In turn,== ==**this implies that we have absolute control over the amount of compute we consume,**== ==which also happens to be ideal for today’s hardware stack, which has been designed much more in favor of static — predictable — computation budgets instead of dynamic ones.====  
  
====反过来，这意味着我们可以绝对控制我们消耗的计算量，这对于今天的硬件堆栈来说也是理想的，它的设计更倾向于静态-可预测-计算预算而不是动态预算。==
==Another important element to point out is that the model also learns this routing process as part of the training.====  
  
====需要指出的另一个重要因素是，模型还将此路由过程作为训练的一部分进行学习。==
==_But what do I mean by that?_== ==Simply put, the parameters of the router (its neurons) ‘learn’ to make such decisions.====  
  
====但这是什么意思呢？简单地说，路由器的参数（它的神经元）“学习”做出这样的决定。==
==Researchers actually tried to make this routing stochastic, or ‘random’, meaning that for every computation words were chosen randomly, but the results were terrible. Instead,== ==**this router was trained to actively predict how relevant each word is.**====  
  
====研究人员实际上试图使这种路由随机，或“随机”，这意味着每次计算的单词都是随机选择的，但结果很糟糕。相反，这个路由器被训练来主动预测每个单词的相关性。==
==Although neural networks are usually explained as a ‘bunch of neurons’, they actually have specific components to them. Hence, think of every component as a ‘pack of neurons’ that behave as ‘knowledge workers’ for a certain role they play in the network. For example:====  
  
====虽然神经网络通常被解释为“一堆神经元”，但它们实际上有特定的组件。因此，可以将每个组件视为一组“神经元”，它们在网络中扮演着“知识工作者”的角色。举例来说：==
==- Attention neurons, part of the attention layers, focus on performing attention.====  
  
====- 注意神经元是注意层的一部分，专注于执行注意力。==
==- Feedforward neurons, part of the feedforward layers, focus on capturing the nuances hidden in the different words.====  
  
====- 前馈神经元是前馈层的一部分，专注于捕捉隐藏在不同单词中的细微差别。==
==- And in this particular case,== ==**the neurons inside the router have the role of predicting how relevant a word is to be computed.**====  
  
====- 在这个特定的例子中，路由器内部的神经元的作用是预测一个单词被计算的相关程度。==
==And as shown on the right-hand side of the previous image, what this means is that, depending on the current word prediction,== ==**the model gets to decide what previous words in the sequence are relevant to predict the new word.**====  
  
====如上一幅图的右侧所示，这意味着，根据当前单词的预测，模型可以决定序列中之前的单词与预测新单词相关。==
==As this is the most important part of the process and probably the most abstract, let’s use an example.====  
  
====由于这是该过程中最重要的部分，也可能是最抽象的部分，让我们使用一个示例。==
## ==Oh Jane, where are you from?====  
  
====简，你从哪里来？==
==Let’s say we have the sequence== ==_**“Jane is French, born in the capital, and likes ice cream. Thus, Jane was born in…“**_====  
  
====假设我们有一个序列“简是法国人，出生在首都，喜欢冰淇淋。简出生在……”==
==For the next-word prediction, which we all know is ‘Paris’, the model has to decide what previous words are relevant for such prediction.====  
  
====对于下一个词的预测，我们都知道是“巴黎”，模型必须决定之前的词与这种预测相关。==
==For instance,== ==_do you think that “and likes ice cream” is relevant to the model’s capacity to output ‘Paris’ as the next word?_====  
  
====例如，你认为“并且喜欢冰淇淋”与模型输出“巴黎”作为下一个单词的能力相关吗？==
==Obviously not, which is precisely what MoD intends to avoid, as:====  
  
====显然不是，这正是国防部打算避免的，因为：==
- ==A standard Transformer would indeed attend to all previous words,== ==**including the unnecessary ones.**====  
      
    ====一个标准的Transformer会处理所有前面的单词，包括不必要的单词。==
- ==On the other hand, a smarter MoD Transformer decides what tokens are irrelevant and, thus, are routed to the residual connections (depicted as empty arrows at the sides in the picture above), skipping all computation. In layman’s terms,== ==**the fact that she likes ice cream is irrelevant and thus those words are ignored for that precise prediction.**====  
      
    ====另一方面，一个更聪明的MoD Transformer决定哪些令牌是不相关的，因此，被路由到剩余的连接（如上图中两侧的空箭头所示），跳过所有计算。用外行人的话来说，她喜欢冰淇淋的事实是无关紧要的，因此这些话被忽略了准确的预测。==
==_See the point?_== ==MoD models are ‘aware’ of the value of each word in a sequence to predict the next one!====  
  
====明白了吗？MoD模型能够“感知”序列中每个单词的值，从而预测下一个单词！==
==An important note, a word not being chosen doesn’t mean it can’t be chosen in the future; it just depends on the current prediction.====  
  
====重要的是，一个词没有被选中并不意味着它在未来不能被选中;它只是取决于当前的预测。==
==So,== ==_what results does MoD obtain?_====  
  
====那么，国防部取得了哪些成果呢？==
==Understanding the key principles driving the evolution of frontier AI models is hard.== ==**But it doesn’t have to be.**====  
  
====理解推动前沿人工智能模型演变的关键原则是困难的。但也没必要这样。==
==If you want to be up-to-date with the frenetic world of AI while also feeling inspired to take action or, at the very least, to be well-prepared for the future ahead of us, my newsletter might be for you== ==**🏝**====  
  
====如果你想跟上疯狂的人工智能世界，同时也想采取行动，或者至少为我们的未来做好准备，我的时事通讯可能适合你。==
## ==An Exciting Discovery 一个令人振奋的发现==
==Overall, MoD’s results are incredible.====  
  
====总的来说，国防部的成果令人难以置信。==
==For a fixed compute comparison between MoDs and standard Transformers, MoDs not only are much more efficient,== ==**but**== ==**they are also smarter**====.====  
  
====对于MOD和标准变压器之间的固定计算比较，MOD不仅更高效，而且更智能。==
==With an 87.5% capacity reduction, meaning that almost 9 out of 10 words are not computed for any given routable block,== ==**the model was still competitive with standard models.**====  
  
====由于容量减少了87.5%，这意味着对于任何给定的可路由块，10个字中几乎有9个没有计算，该模型仍然与标准模型竞争。==
==This means that, despite saving almost 90% of the compute cost in those layers,== ==**the model was still on par with the 100% compute-allocated model.**== ==And not only that, for the same compute,== ==**they actually perform better than the baseline.**====  
  
====这意味着，尽管在这些层中节省了近90%的计算成本，但该模型仍然与100%计算分配模型相当。不仅如此，对于相同的计算，它们实际上比基线更好。==
==In other words,== ==**for a given cost, they are better models.**====  
  
====换句话说，对于给定的成本，它们是更好的模型。==
==One reason this might be happening is that, by rejecting the computation over unnecessary tokens, we avoid introducing unnecessary noise with words that aren’t relevant to the prediction.====  
  
====可能发生这种情况的一个原因是，通过拒绝对不必要的标记进行计算，我们避免了用与预测无关的单词引入不必要的噪音。==
==Thus, for every given prediction,== ==**the signal-to-noise ratio seems to be much better**== ==and, thus, improves the overall result.====  
  
====因此，对于每个给定的预测，信噪比似乎要好得多，从而改善了整体结果。==
==It must be said that the best results they obtained occurred when alternating ‘routable’ blocks with standard ones. In layman’s terms, over the sequence of, let’s say, 20 Transformer blocks, 10 offer the possibility of routing words and 10 don’t.====  
  
====必须说，他们获得的最佳结果发生在交替“可路由”块与标准块时。通俗地说，在20个Transformer块的序列中，10个提供路由字的可能性，10个不提供。==
==This avoids the risk of having words that, were all blocks routable, would be always rejected despite playing some minor role that should be eventually considered.====  
  
====这避免了这样的风险，即所有块都是可路由的，尽管扮演了一些最终应该考虑的次要角色，但总是会被拒绝。==
==One last thing to mention is their relationship with Mixture-of-Experts (MoE) models.====  
  
====最后一点要提到的是它们与专家混合（MoE）模型的关系。==
## ==MoDE models, the Future of AI?====  
  
====MoDE模型，AI的未来？==
==MoE is another form of conditional computing that, instead of choosing what tokens participate in the prediction, breaks down the model into ‘experts’ so that each part of the model becomes more specialized per topic and more efficient to run.== ==**GPT-4, Mixtral, Gemini 1.5, or Claude 3 are all MoEs.**====  
  
====莫伊是另一种形式的条件计算，它不是选择参与预测的令牌，而是将模型分解为“专家”，以便模型的每个部分在每个主题上变得更加专业化，并且运行起来更有效。GPT-4、Mixtral、Gemini 1.5或Claude 3都是MoE。==
[==To fully understand what Mixture-of-Experts is, read here.==](https://thewhitebox.ai/mixture-of-experts-the-new-standard-for-llms/)==  
  
==[==要充分了解什么是专家混合，请阅读此处。==](https://thewhitebox.ai/mixture-of-experts-the-new-standard-for-llms/)
==_But does that mean we have to choose between one or the other?_== ==Luckily, no.====  
  
====但这是否意味着我们必须在两者之间做出选择？很幸运，没有。==
==While MoEs work at the width dimension,== ==**MoD works at the depth dimension**====, where a model has a fixed compute budget and has to be clever about what tokens (words in LLMs’ case) to engage for each prediction.====  
  
====MoE在宽度维度上工作，MoD在深度维度上工作，其中模型具有固定的计算预算，并且必须巧妙地了解每个预测要使用什么标记（LLMs'情况下的单词）。==
==In other words, they can be combined.====  
  
====换句话说，它们可以结合起来。==
==What’s more, MoD’s researchers tried it, achieving a highly promising result where the model,== ==**for a specific compute budget, achieved lower loss by combining both methods.**====  
  
====更重要的是，国防部的研究人员尝试了这种方法，取得了非常有希望的结果，对于特定的计算预算，模型通过结合两种方法实现了更低的损失。==
[![](https://miro.medium.com/v2/resize:fit:770/0*vTDy_4vZDAE-Fnlw)](https://miro.medium.com/v2/resize:fit:770/0*vTDy_4vZDAE-Fnlw)
==Source: Google 来源：Google==
## ==Setting a Precedent 构成先例==
==In my humble opinion,== ==**this is one of those research papers that could set a precedent.**====  
  
====以我的拙见，这是那些可以开创先例的研究论文之一。==
==Today’s AI is surprisingly powerful at it is great at consuming electricity. Thus, although we must not set guardrails to innovation, innovation not only must push us to more powerful AI models, but also to more sustainable ones.====  
  
====今天的人工智能是惊人的强大，它是伟大的消耗电力。因此，尽管我们不能为创新设置护栏，但创新不仅必须推动我们实现更强大的人工智能模型，而且必须推动我们实现更可持续的模型。==
==Fascinatingly, however, based on its results,== ==**it can be interpreted that Mixture-of-Depths plays a key role in both dimensions.**====  
  
====然而，有趣的是，根据其结果，可以解释为深度混合在两个维度中都起着关键作用。==
==Besides creating more efficient models, it makes them smarter overall by making them more ‘aware’ of allocating computing to what matters,== ==**akin to humans dedicating more thought effort to a hard problem to gain better results.**====  
  
====除了创建更高效的模型外，它还通过使它们更“意识到”将计算分配给重要的事情来使它们整体上更智能，类似于人类将更多的思考精力投入到一个难题中以获得更好的结果。==
==What’s more, this idea of making models more capable of eliminating unnecessary or redundant computations for the sake of efficiency may be a ‘must’ in the== ==_“long-inference future”_== ==AI seems to be heading toward, AI models that for complex tasks explore the realm of possibilities before even predicting a single token, be that a word or a video frame.====  
  
====更重要的是，为了提高效率，让模型更有能力消除不必要或冗余的计算的想法可能是人工智能似乎正在走向的“长期推理未来”中的“必须”，对于复杂任务，人工智能模型甚至在预测单个令牌之前探索可能性的领域，无论是单词还是视频帧。==
==Overall, unless we make model predictions more efficient and cheaper, that world we seem to be rushing toward is nothing more than a dystopia.====  
  
====总的来说，除非我们让模型预测更有效、更便宜，否则我们似乎正在奔向的世界只不过是一个反乌托邦。==
==Therefore, while it has yet to be proven at scale,== ==**we could soon see all models being MoDs.**====  
  
====因此，虽然它还没有得到大规模的证明，但我们很快就会看到所有的模型都是MoD。==
==On a final note, if you have enjoyed this article, I share similar thoughts in a more comprehensive and simplified manner for free on my== [==LinkedIn==](https://www.linkedin.com/in/ignacio-de-gregorio-noblejas/)==.====  
  
====最后，如果你喜欢这篇文章，我在LinkedIn上以更全面和简化的方式免费分享类似的想法。==
==If preferable, you can connect with me through== [==X==](https://twitter.com/TheTechOasis1)==.====  
  
====如果你愿意，你可以通过X与我联系。==
==Looking forward to connecting with you.====  
  
====期待与您联系。==