---
Updated: 2024-08-17T11:21
tags:
  - AI->-Voice
URL: https://mp.weixin.qq.com/s?__biz=MzA4NzgzMjA4MQ==&mid=2453446851&idx=1&sn=4ea42607a69eece38fd144ea6e22450c&chksm=87fb63bdb08ceaabaa876e882b4bf72cfd1adc37f92a9ed7a736b7970fa15280c666365d75fe&exptype=unsubscribed_card_recommend_article_u2i_mainprocess_coarse_sort_tlfeeds&ranksessionid=1723824874_1&scene=169&subscene=200&sessionid=1723824873&clicktime=1543001&enterid=1543001&ascene=56&fasttmpl_type=0&fasttmpl_fullversion=7341563-zh_CN-zip&fasttmpl_flag=0&realreporttime=1723824886885&devicetype=android-34&version=28003145&nettype=WIFI&lang=zh_CN&session_us=gh_a93be09821ac&countrycode=JP&exportkey=n_ChQIAhIQvHAS9%2FYE59yfPxR7KpYnuxLxAQIE97dBBAEAAAAAAJyNEFjcbv8AAAAOpnltbLcz9gKNyK89dVj0PDZHCV47L2kL9sIgjxLXJEb3hCbsUJdSAHh3UrloVhQscqipUTJncZnY8PFDRUeUNbbzfu8yOXaQ9k7jdhbJJ2cBQh%2BunQS8G4olJRg%2FZL4zLSltyZP5f5fUsishkEmfXehw4w9IfGSp7J7YP2D8x4i8wUtxokXtnT8lKRP6zNI6aL7W6dLGVheatnjkWJT7QbezuFVO1%2Bz0S0vsPx2EKvASYZ2vAgutJo6Mikccb7gApiB4mh5a4to79%2FtxUuoy%2BfWUlja5vsQWjoY%3D&pass_ticket=92JpXQopFXGv3mkK4PFU40bOzgQVrpso9wHIh8RWEEAyb%2BDwqOmozre93TJ5pmfx&wx_header=3
Created: 2024-08-17T01:15
---
来自Hugging Face的Vaibhav Srivastav刚刚在推特上宣布了一个**令人震惊的突破**：**Speech to Speech**语音交互系统！
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/M3PrhSUICnEmZPRic0f6T3svprmGwjOEujhCa7iaoJ7KQqc170PtSLZFn8nHo9EWiblyqTah692lwChgM4lAsnibZg/640?wx_fmt=jpeg&wxfrom=16)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/M3PrhSUICnEmZPRic0f6T3svprmGwjOEujhCa7iaoJ7KQqc170PtSLZFn8nHo9EWiblyqTah692lwChgM4lAsnibZg/640?wx_fmt=jpeg&wxfrom=16)
它不仅**100%保护隐私**，而且**延迟低至500毫秒，没错，就是0.5秒**！
想象一下，你对着手机说话，**0.5秒后**就能得到AI的语音回复，这简直就是面对面交谈的感觉啊！
当然，这个指标目前比GPT-4o 还是慢了一些，但基本已经能接近真人对面交谈的水平了，毕竟这还只是初始版本。
更令人兴奋的是，这个系统还是**跨平台**的，Mac和CUDA都能用！
那么，这个神奇的系统是怎么做到的呢？Vaibhav给我们揭开了它的神秘面纱：
1. **语音活动检测**：使用Silero VAD v5
2. **语音转文本**：采用Whisper模型
3. **语言模型**：可以使用任何指令型模型
4. **文本转语音**：选择Parler-TTS
不仅如此，他们还整合了**AWQ、GPTQ、BnB**等量化方案，让系统更省内存、推理更快！
关键的是，这还是在设备上本地运行啊。
**并且项目还开源了！**
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/M3PrhSUICnEmZPRic0f6T3svprmGwjOEuibJUfr2esPa1Opc535ibqLEazlPb3dtyngvJCBp8CLOEduWlmYKG91mw/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/M3PrhSUICnEmZPRic0f6T3svprmGwjOEuibJUfr2esPa1Opc535ibqLEazlPb3dtyngvJCBp8CLOEduWlmYKG91mw/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
Vaibhav在推文中给出了GitHub链接：
https://github.com/eustlb/speech-to-speech
有开发者朋友已经迫不及待地想尝试了：

> "我能不能把语言模型换成翻译模型，做一个实时语音翻译器呢？"
Vaibhav的回答也是相当鼓舞人心：

> "当然可以！只需要换一下LLM的骨干网络，再给一个自定义的翻译指令就行。几行代码就能搞定！"
系统的**可扩展性**也是很强！
不过，Vaibhav也提醒我们，这只是第一个迭代版本。他们正在努力让它变得**更好、更快**！
👇
👇
👇
👇
### 本文同步自于知识星球《AGI Hunt》
星球实时采集和监控推特、油管、discord、电报等平台的热点AI 内容，并基于数个资讯处理的 AI agent 挑选、审核、翻译、总结到星球中。
- 每天约监控6000 条消息，可节省约800+ 小时的阅读成本。
- 每天挖掘出10+ 热门的/新的 github 开源 AI 项目
- 每天转译、点评 10+ 热门 arxiv AI 前沿论文
**星球非免费。**定价99元/年，0.27元/天。(每+100人，+20元。元老福利~）
- 一是运行有成本，我希望它能自我闭环，这样才能长期稳定运转；
- 二是对人的挑选，鱼龙混杂不是我想要的，希望找到关注和热爱 AI 的人。
**欢迎你的加入！**
[![](https://mmbiz.qpic.cn/sz_mmbiz_jpg/M3PrhSUICnHXibpqRaXEUKnxOW4icGNH9pP9Wy5qNnTwfosQvEmPLbokV4Nnlo2k8oY001iblm8fFIcrsH5ibHF8ibg/640?wx_fmt=jpeg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)](https://mmbiz.qpic.cn/sz_mmbiz_jpg/M3PrhSUICnHXibpqRaXEUKnxOW4icGNH9pP9Wy5qNnTwfosQvEmPLbokV4Nnlo2k8oY001iblm8fFIcrsH5ibHF8ibg/640?wx_fmt=jpeg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)