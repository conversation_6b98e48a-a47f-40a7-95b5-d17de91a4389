---
Updated: 2024-04-30T09:00
tags:
  - AI->-SQL
Created: 2024-04-30T09:00
---
[![](https://opengraph.githubassets.com/4335d0b2c54b841df7be8825cd963b1f1de3308c688da14c1d7436cc1da232a1/chat2db/Chat2DB)](https://opengraph.githubassets.com/4335d0b2c54b841df7be8825cd963b1f1de3308c688da14c1d7436cc1da232a1/chat2db/Chat2DB)
[![](https://camo.githubusercontent.com/951e2ed07583ae007d03ea7f5c6ade2018aa63de598d3b76ea6ea336d39fd1c1/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f636f7665722e706e67)](https://camo.githubusercontent.com/951e2ed07583ae007d03ea7f5c6ade2018aa63de598d3b76ea6ea336d39fd1c1/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f636f7665722e706e67)
==**Share Chat2DB Repository**==
[![](https://camo.githubusercontent.com/f5ab63eba06abc7d9396638857734fc31c3236763b6e757db75cac070f153b5a/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f666f6c6c6f772f5f436861743244423f6c6162656c3d5368617265206f6e2054776974746572267374796c653d736f6369616c)](https://camo.githubusercontent.com/f5ab63eba06abc7d9396638857734fc31c3236763b6e757db75cac070f153b5a/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f666f6c6c6f772f5f436861743244423f6c6162656c3d5368617265206f6e2054776974746572267374796c653d736f6369616c)
[![](https://camo.githubusercontent.com/f43ba33909ccead9ed929a8375fe605231c8b5bfb592246f1aaf907f4bce6f03/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f75726c3f6c6162656c3d54656c656772616d266c6f676f3d54656c656772616d267374796c653d736f6369616c2675726c3d68747470733a2f2f6769746875622e636f6d2f636861743264622f43686174324442)](https://camo.githubusercontent.com/f43ba33909ccead9ed929a8375fe605231c8b5bfb592246f1aaf907f4bce6f03/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f75726c3f6c6162656c3d54656c656772616d266c6f676f3d54656c656772616d267374796c653d736f6369616c2675726c3d68747470733a2f2f6769746875622e636f6d2f636861743264622f43686174324442)
[![](https://camo.githubusercontent.com/9890b5836c50ebe8b416b85db572dc3d0ce97670dd4fe49a2f500a73836874b4/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f75726c3f6c6162656c3d7768617473617070266c6f676f3d7768617473617070267374796c653d736f6369616c2675726c3d68747470733a2f2f6769746875622e636f6d2f636861743264622f43686174324442)](https://camo.githubusercontent.com/9890b5836c50ebe8b416b85db572dc3d0ce97670dd4fe49a2f500a73836874b4/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f75726c3f6c6162656c3d7768617473617070266c6f676f3d7768617473617070267374796c653d736f6369616c2675726c3d68747470733a2f2f6769746875622e636f6d2f636861743264622f43686174324442)
[![](https://camo.githubusercontent.com/74714ef40707f60f540787842e8542ebcb0e0b69fa3d4609006e847615ec63a0/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f75726c3f6c6162656c3d526564646974266c6f676f3d526564646974267374796c653d736f6369616c2675726c3d68747470733a2f2f6769746875622e636f6d2f636861743264622f43686174324442)](https://camo.githubusercontent.com/74714ef40707f60f540787842e8542ebcb0e0b69fa3d4609006e847615ec63a0/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f75726c3f6c6162656c3d526564646974266c6f676f3d526564646974267374796c653d736f6369616c2675726c3d68747470733a2f2f6769746875622e636f6d2f636861743264622f43686174324442)
[![](https://camo.githubusercontent.com/3d698b67699175df4417323fba5334daa64a551662afb22698b15200e4f71241/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f75726c3f6c6162656c3d476d61696c266c6f676f3d476d61696c267374796c653d736f6369616c2675726c3d68747470733a2f2f6769746875622e636f6d2f636861743264622f43686174324442)](https://camo.githubusercontent.com/3d698b67699175df4417323fba5334daa64a551662afb22698b15200e4f71241/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f75726c3f6c6162656c3d476d61696c266c6f676f3d476d61696c267374796c653d736f6369616c2675726c3d68747470733a2f2f6769746875622e636f6d2f636861743264622f43686174324442)
==If you think Chat2DB helpful, please click ⭐ Star== ==and Fork this repo in the top right corner==
[![](https://camo.githubusercontent.com/b0490ae33d9e1fb22cc94a89c8e6bc2fb7da8f97d08c364cad67ff2ab4d092fa/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f73746172732f636861743264622f436861743244422e7376673f7374796c653d666c61742d737175617265266c6162656c3d5374617273266c6f676f3d676974687562)](https://camo.githubusercontent.com/b0490ae33d9e1fb22cc94a89c8e6bc2fb7da8f97d08c364cad67ff2ab4d092fa/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f73746172732f636861743264622f436861743244422e7376673f7374796c653d666c61742d737175617265266c6162656c3d5374617273266c6f676f3d676974687562)
==Or upvote on Product Hunt== ==your support is the biggest motivation for Chat2DB to get better==
[![](https://camo.githubusercontent.com/18a1b9195d12fed1ad63d796268f906299285bd386e57820e41b8a4d382d4ae8/68747470733a2f2f6170692e70726f6475637468756e742e636f6d2f776964676574732f656d6265642d696d6167652f76312f66656174757265642e7376673f706f73745f69643d343530303137267468656d653d6c69676874)](https://camo.githubusercontent.com/18a1b9195d12fed1ad63d796268f906299285bd386e57820e41b8a4d382d4ae8/68747470733a2f2f6170692e70726f6475637468756e742e636f6d2f776964676574732f656d6265642d696d6167652f76312f66656174757265642e7376673f706f73745f69643d343530303137267468656d653d6c69676874)
## ==NEWS==
### ==🚀🚀🚀 The long-awaited Chat2DB Pro version is finally here, with the following key highlights added.==
### ==🔥🔥🔥AI-driven intelligent SQL development==
==  
  
==[](https://chat2db.ai/)[](https://chat2db.ai/)==  
  
==[](https://chat2db.ai/)==  
  
==[](https://chat2db.ai/)[](https://chat2db.ai/)==  
  
==[](https://chat2db.ai/)[](https://chat2db.ai/)==  
  
==[](https://chat2db.ai/)==  
  
==[](https://chat2db.ai/)[](https://chat2db.ai/)[](https://chat2db.ai/)[](https://chat2db.ai/)==  
  
==[](https://chat2db.ai/)
[![](https://camo.githubusercontent.com/793694ed659dbbbe74c36a8c52aa3ef59562171abef033020706a2215cdfad5a/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f39363634663266642d323530622d343738652d613261312d3462393634363462613731382e676966)](https://camo.githubusercontent.com/793694ed659dbbbe74c36a8c52aa3ef59562171abef033020706a2215cdfad5a/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f39363634663266642d323530622d343738652d613261312d3462393634363462613731382e676966)
[![](https://camo.githubusercontent.com/793694ed659dbbbe74c36a8c52aa3ef59562171abef033020706a2215cdfad5a/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f39363634663266642d323530622d343738652d613261312d3462393634363462613731382e676966)](https://camo.githubusercontent.com/793694ed659dbbbe74c36a8c52aa3ef59562171abef033020706a2215cdfad5a/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f39363634663266642d323530622d343738652d613261312d3462393634363462613731382e676966)
[![](https://camo.githubusercontent.com/a33156d6eef623bc76aa6b4b0ba2817d82a58cdae85f1b38e57323fb0698c2ce/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f66613961303161342d373834352d343839332d393363632d6334396131316636323536392e676966)](https://camo.githubusercontent.com/a33156d6eef623bc76aa6b4b0ba2817d82a58cdae85f1b38e57323fb0698c2ce/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f66613961303161342d373834352d343839332d393363632d6334396131316636323536392e676966)
[![](https://camo.githubusercontent.com/a33156d6eef623bc76aa6b4b0ba2817d82a58cdae85f1b38e57323fb0698c2ce/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f66613961303161342d373834352d343839332d393363632d6334396131316636323536392e676966)](https://camo.githubusercontent.com/a33156d6eef623bc76aa6b4b0ba2817d82a58cdae85f1b38e57323fb0698c2ce/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f66613961303161342d373834352d343839332d393363632d6334396131316636323536392e676966)
[![](https://camo.githubusercontent.com/a5ed61adce83ddf667965cfae036ff8bead516ff24cb3f3568659eb59b93afe1/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f34376136316165372d326532312d343565372d613431652d6532353539663531393139642e676966)](https://camo.githubusercontent.com/a5ed61adce83ddf667965cfae036ff8bead516ff24cb3f3568659eb59b93afe1/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f34376136316165372d326532312d343565372d613431652d6532353539663531393139642e676966)
[![](https://camo.githubusercontent.com/a5ed61adce83ddf667965cfae036ff8bead516ff24cb3f3568659eb59b93afe1/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f34376136316165372d326532312d343565372d613431652d6532353539663531393139642e676966)](https://camo.githubusercontent.com/a5ed61adce83ddf667965cfae036ff8bead516ff24cb3f3568659eb59b93afe1/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f34376136316165372d326532312d343565372d613431652d6532353539663531393139642e676966)
### ==✨✨✨AI-driven Intelligent Reports==
[](https://chat2db.ai/)[](https://chat2db.ai/)==  
  
==[](https://chat2db.ai/)
[![](https://camo.githubusercontent.com/1f81bad3da923841dc2b3495d8b0f8f31624edce981f4909c3a50e4e3a31d44f/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f64303730636131312d386264642d346666342d613433352d6563666336323331303363622e676966)](https://camo.githubusercontent.com/1f81bad3da923841dc2b3495d8b0f8f31624edce981f4909c3a50e4e3a31d44f/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f64303730636131312d386264642d346666342d613433352d6563666336323331303363622e676966)
[![](https://camo.githubusercontent.com/1f81bad3da923841dc2b3495d8b0f8f31624edce981f4909c3a50e4e3a31d44f/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f64303730636131312d386264642d346666342d613433352d6563666336323331303363622e676966)](https://camo.githubusercontent.com/1f81bad3da923841dc2b3495d8b0f8f31624edce981f4909c3a50e4e3a31d44f/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f64303730636131312d386264642d346666342d613433352d6563666336323331303363622e676966)
### ==🎉🎉🎉AI-driven Data Exploration==
[![](https://camo.githubusercontent.com/ca7a3cb87e8ba4fe40d8bdb7f842e64abd7c7f9c97a81685e7109b11b55afe44/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f576563686174494d47353133382e6a7067)](https://camo.githubusercontent.com/ca7a3cb87e8ba4fe40d8bdb7f842e64abd7c7f9c97a81685e7109b11b55afe44/68747470733a2f2f636861743264622d63646e2e6f73732d75732d776573742d312e616c6979756e63732e636f6d2f776562736974652f696d672f576563686174494d47353133382e6a7067)
### ==🎁🎁🎁 We have also open-sourced our first GLM,== [==Chat2DB-SQL-7B==](https://github.com/chat2db/Chat2DB-GLM)==. You can refer more details from below links.==
- ==github:== [==Chat2DB-SQL-7B==](https://github.com/chat2db/Chat2DB-GLM)
- ==huggingface🤗：==[==Chat2DB-SQL-7B==](https://huggingface.co/Chat2DB/Chat2DB-SQL-7B)
- ==modelscope：==[==Chat2DB-SQL-7B==](https://modelscope.cn/models/Chat2DB/Chat2DB-SQL-7B/summary)
## ==🚀 Supported databases==
==Chat2DB Pro supports all the following databases, including the most requested Redis feature.==
- ==MySQL==
- ==PostgreSQL==
- ==H2==
- ==Oracle==
- ==SQLServer==
- ==SQLite==
- ==MariaDB==
- ==ClickHouse==
- ==DM==
- ==Presto==
- ==DB2==
- ==OceanBase==
- ==Hive==
- ==KingBase==
- ==MongoDB==
- ==Redis==
- ==Snowflake==
## ==⏬ Download and Install Chat2DB(Pro)==
- ==Download installation package from== [==official website==](https://chat2db.ai/download)
- ==Double click package to install==
## ==🌰 Using Chat2DB==
==Refer to the== [==Quick Start Guide==](https://docs.chat2db.ai/) ==to get started with Chat2DB.==
## ==Contribution Guide==
==We welcome and encourage community members to contribute to the Chat2DB project. Whether it's by reporting issues, proposing new features, or directly submitting code fixes and improvements, your help is invaluable. If you're interested in contributing, please follow our contribution guidelines:==
- ==Report Issues: Report any issues or bugs encountered via GitHub Issues.==
- ==Submit Pull Requests: If you wish to contribute directly to the codebase, please fork the repository and submit a pull request (PR).==
- ==Improve Documentation: Contributions to best practices, example code, documentation improvements, etc., are welcome.==
## ==Stargazers==
[![](https://camo.githubusercontent.com/5ad92a7bb79d7d71bcab2a36504a12e152f33e3f518d37f2495c0120fa420f2b/68747470733a2f2f7265706f726f737465722e636f6d2f73746172732f636861743264622f43686174324442)](https://camo.githubusercontent.com/5ad92a7bb79d7d71bcab2a36504a12e152f33e3f518d37f2495c0120fa420f2b/68747470733a2f2f7265706f726f737465722e636f6d2f73746172732f636861743264622f43686174324442)
## ==Forkers==
[![](https://camo.githubusercontent.com/69a2ac5ee4aa6cef1159758d1492ae0edab177d84db79efea74b5c8236ac09f5/68747470733a2f2f7265706f726f737465722e636f6d2f666f726b732f636861743264622f43686174324442)](https://camo.githubusercontent.com/69a2ac5ee4aa6cef1159758d1492ae0edab177d84db79efea74b5c8236ac09f5/68747470733a2f2f7265706f726f737465722e636f6d2f666f726b732f636861743264622f43686174324442)
## ==👋 Join Us==
### ==On WeChat==
[![](https://private-user-images.githubusercontent.com/22975773/307237480-81d13eff-c615-49f5-aee3-4107089593e0.jpeg?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MTQ0MzU0MzQsIm5iZiI6MTcxNDQzNTEzNCwicGF0aCI6Ii8yMjk3NTc3My8zMDcyMzc0ODAtODFkMTNlZmYtYzYxNS00OWY1LWFlZTMtNDEwNzA4OTU5M2UwLmpwZWc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjQwNDI5JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI0MDQyOVQyMzU4NTRaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT05YjRjMmExY2Q2NTRiOWJjZGJkMTQ2YmEyY2E0MmYyZWJjZGYzMzM5ZDZhNjhmNDhmMTkzNjM2YWEyM2Q2MTYxJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCZhY3Rvcl9pZD0wJmtleV9pZD0wJnJlcG9faWQ9MCJ9.248md8gL4b1RoWNEWAASNq6teJIiDOo_C4hf3CJ7TX8)](https://private-user-images.githubusercontent.com/22975773/307237480-81d13eff-c615-49f5-aee3-4107089593e0.jpeg?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MTQ0MzU0MzQsIm5iZiI6MTcxNDQzNTEzNCwicGF0aCI6Ii8yMjk3NTc3My8zMDcyMzc0ODAtODFkMTNlZmYtYzYxNS00OWY1LWFlZTMtNDEwNzA4OTU5M2UwLmpwZWc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjQwNDI5JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI0MDQyOVQyMzU4NTRaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT05YjRjMmExY2Q2NTRiOWJjZGJkMTQ2YmEyY2E0MmYyZWJjZGYzMzM5ZDZhNjhmNDhmMTkzNjM2YWEyM2Q2MTYxJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCZhY3Rvcl9pZD0wJmtleV9pZD0wJnJlcG9faWQ9MCJ9.248md8gL4b1RoWNEWAASNq6teJIiDOo_C4hf3CJ7TX8)
### ==On Discord==
[![](https://camo.githubusercontent.com/69fdf079e42d81710ec7b289f127a7f63d94b94484acb4c0cb1d4fda8a472003/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f2d4a6f696e25323075732532306f6e253230446973636f72642d2532333732383944412e7376673f7374796c653d666c6174266c6f676f3d646973636f7264266c6f676f436f6c6f723d7768697465)](https://camo.githubusercontent.com/69fdf079e42d81710ec7b289f127a7f63d94b94484acb4c0cb1d4fda8a472003/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f2d4a6f696e25323075732532306f6e253230446973636f72642d2532333732383944412e7376673f7374796c653d666c6174266c6f676f3d646973636f7264266c6f676f436f6c6f723d7768697465)
## ==LICENSE==
==The primary license used by this software is the== [==Apache License 2.0==](https://www.apache.org/licenses/LICENSE-2.0)==, supplemented by the== [==Chat2DB License==](https://github.com/chat2db/Chat2DB/blob/main/Chat2DB_LICENSE)==.==
## ==❤️ Acknowledgements==
==Thanks to all who contributed to Chat2DB~~==
[![](https://camo.githubusercontent.com/2992348d30eef72617e14218b52374f1894afe5a8fa7647b0badbc3309edb0c1/68747470733a2f2f636f6e747269622e726f636b732f696d6167653f7265706f3d636861743264622f43686174324442)](https://camo.githubusercontent.com/2992348d30eef72617e14218b52374f1894afe5a8fa7647b0badbc3309edb0c1/68747470733a2f2f636f6e747269622e726f636b732f696d6167653f7265706f3d636861743264622f43686174324442)
## ==Star History==
[![](https://camo.githubusercontent.com/1b80283c7e8dc7d10d18544ef05f8a8fb84da97feb328bc36608488cdbe3244a/68747470733a2f2f6170692e737461722d686973746f72792e636f6d2f7376673f7265706f733d636861743264622f6368617432646226747970653d44617465)](https://camo.githubusercontent.com/1b80283c7e8dc7d10d18544ef05f8a8fb84da97feb328bc36608488cdbe3244a/68747470733a2f2f6170692e737461722d686973746f72792e636f6d2f7376673f7265706f733d636861743264622f6368617432646226747970653d44617465)