---
Updated: 2024-06-04T14:54
tags:
  - AI->-Theory
URL: https://mp.weixin.qq.com/s?chksm=c04d7db1f73af4a7ff349cac828a817271d6de029184401f9e7acfae4024b1834baa2000b734&exptype=unsubscribed_card_recommend_article_u2i_mainprocess_coarse_sort_tlfeeds&ranksessionid=1717457585_2&mid=2247485909&sn=2ae276b2c0f35bef3c77361295f3b5af&idx=1&__biz=Mzg5OTkwMDY4Mw%3D%3D&scene=169&subscene=200&sessionid=1717457584&flutter_pos=14&clicktime=1717459379&enterid=1717459379&finder_biz_enter_id=5&ascene=56&fasttmpl_type=0&fasttmpl_fullversion=7234854-zh_CN-zip&fasttmpl_flag=0&realreporttime=1717459379976&devicetype=android-34&version=28003156&nettype=WIFI&lang=zh_CN&session_us=gh_943d225ad118&countrycode=JP&exportkey=n_ChQIAhIQv%2Bqb%2BgM4UMvbHsweAKQuIxLxAQIE97dBBAEAAAAAAHlUGNPSoYYAAAAOpnltbLcz9gKNyK89dVj0LM38E0RKn%2FDv4dRkY0tb5diLgvQ3mv2sIUgU%2Fp3y%2FWnXtLmc80yNg6IgRigDHMO1uCJ2o4v0i6pduq0xLs7MWPKsqeN1ORkYqPCTy8Mn%2FpRXjXK5JsXAojDTPGn8p7J5tHNLQHjAWrNGD1Kxm4xdUj9Zk4hIgwN24A2I%2BD1AOYk0%2BTkBaHTgFDeaqKsvV61TReuss6I%2FEv%2BFnNS4Ma9vFjUk9PwkIim7XTFQgXjw2kblwEsrQPAzH78ssLNbtLjfGeCfBFZkJUk%2Fe7Y%3D&pass_ticket=3s0RkO%2BcXE6ukSUW403ia4hp4t1Cutaswk5PMq9Hn2tHwZzYa8XChcTitE69eJE7&wx_header=3
Created: 2024-06-04T14:37
---
一只小鸭子，咿呀 AI for Research _2024-06-01 22:35_ _广东_
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/0XibHbUBQBddGsGmCTZUoibM1vv58lm0vVDI2zeLunChr7Rrk1RnJiap8HBT7ZyHN52Ve5WmDY618WSmeibETiaug2g/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)](https://mmbiz.qpic.cn/sz_mmbiz_png/0XibHbUBQBddGsGmCTZUoibM1vv58lm0vVDI2zeLunChr7Rrk1RnJiap8HBT7ZyHN52Ve5WmDY618WSmeibETiaug2g/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
这是一场关于如何最大化地提高大型语言模型性能的分享。John Allard和Colin分别介绍了不同的技术，包括提示工程、检索增强生成（RAG）和微调等，以及如何将这些技术应用到实际问题中。
John首先介绍了自己在OpenAI微调产品团队的工作，并提到了微调的一些进展，如函数调用数据的微调、持续微调和平台内全UI微调。他还提到了与不同规模的开发者合作的经验，以及他们如何使用LLM解决各种问题。
Colin接着讨论了优化LLM的难点，包括难以从噪声中分离信号、性能难以衡量以及难以确定解决问题的方法。他强调了建立问题框架的重要性，并介绍了用于解决问题的工具。
演讲中提到，**优化LLM性能并不总是线性的，有时需要结合使用提示工程、RAG和微调**。提示工程是开始的好地方，可以快速测试和学习。RAG允许模型访问特定领域的内容，而微调则可以强化模型遵循一致指令的能力。
John和Colin分享了实际案例，包括成功和失败的案例，以及如何使用不同的技术来解决问题。他们还讨论了如何评估和优化RAG系统，并强调了在微调过程中数据质量比数量更重要。
以下是他们分享的2个经典的应用案例：
**客户需求1**：
他们有大量文档（比如10万份），希望模型只基于这些文档进行知识检索。
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/0XibHbUBQBddGsGmCTZUoibM1vv58lm0vVDtAEAib1aicofciaVloeSlJDYCKsSsziaocV9UwDc78ybV4RjzELicpNuRA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)](https://mmbiz.qpic.cn/sz_mmbiz_png/0XibHbUBQBddGsGmCTZUoibM1vv58lm0vVDtAEAib1aicofciaVloeSlJDYCKsSsziaocV9UwDc78ybV4RjzELicpNuRA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
**解决方案：**
1.  直接将PDF和docx文件嵌入，准确率是45%。
2. 经过20次调优迭代，解决细节小Bug - 准确率到65%
3. 基于规则进行优化，譬如先判断问题属于什么领域（退一步思考），然后再回答，效果提升到85%
4. 发现数据里有一些是结构化数据（如表格），为此定制提取解决，准确率提升到98%
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/0XibHbUBQBddGsGmCTZUoibM1vv58lm0vVwvLbT7BLNhktGxgtU0VAWkopibyPcHufdYicz7rsicFmOYOXcyUANxPNg/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)](https://mmbiz.qpic.cn/sz_mmbiz_png/0XibHbUBQBddGsGmCTZUoibM1vv58lm0vVwvLbT7BLNhktGxgtU0VAWkopibyPcHufdYicz7rsicFmOYOXcyUANxPNg/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
**客户需求2**：
文本生成SQL（利用自然语言问题和数据库模式生成语法正确的SQL查询，比如给定一个数据库模式和一个问题，能否生成相应的SQL查询）。
**解决方案：**
1. 首先，我们用最简单的方法，比如余弦相似性，寻找类似问题的SQL查询，进行问题相似性搜索。
我们测试了不同的嵌入和提示工程，但结果不太好。后来，我们意识到不同的数据库模式对问题的答案可能完全不同。
2. 所以针对一个问题进行相似性搜索意义不大，但用预设答案进行搜索可能效果更好。
我们使用预设的文档嵌入，生成一个假设的SQL查询进行相似性搜索，性能大大提升。对于这个特定问题，我们尝试了上下文检索，通过简单过滤将问题按难度排名，只带回同等难度的例子，这带来了改进。
我们还尝试了一些先进技术，比如链式推理，让系统识别列、表，最后构建查询。但我们决定采用更简单的方法进行自我一致性检查，让系统构建查询并运行，出错时给出提示，再次尝试。
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/0XibHbUBQBddGsGmCTZUoibM1vv58lm0vVEbVzFJXibLDbMv5GT2Hnkd0GwGC9oUzkl4BntJicyC72uc53NP12BxwQ/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)](https://mmbiz.qpic.cn/sz_mmbiz_png/0XibHbUBQBddGsGmCTZUoibM1vv58lm0vVEbVzFJXibLDbMv5GT2Hnkd0GwGC9oUzkl4BntJicyC72uc53NP12BxwQ/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
3. 我们从69%开始，然后添加了一些示例，得到了几个改进点，这表明RAG可以进一步改进。
我们尝试这个问题，看到性能提升了3%，然后使用答案，假设的文档嵌入使我们进一步提升了5%。
4. 我们决定试试微调，看能否更进一步。先建立了我们的基线，与上一张幻灯片里的69%基线一样，使用简单的提示工程技术。
然后，用简单的提示工程对GPT-4进行微调，结果模型准确率提升到接近82%。
再稍微调整模型使用um rag技术，把一些示例动态注入上下文窗口，准确率达到83.5%。
这些都很简单，没有复杂的数据预处理或后处理。结果显示简单的微调和提示工程也能接近最先进的技术。
[![](https://mmbiz.qpic.cn/sz_mmbiz_png/0XibHbUBQBddGsGmCTZUoibM1vv58lm0vVAXqAPlf20GNqPv1W0ocGIGxRs8kAExKKibcwOKC5hXYAiajoFMX3g1gA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)](https://mmbiz.qpic.cn/sz_mmbiz_png/0XibHbUBQBddGsGmCTZUoibM1vv58lm0vVAXqAPlf20GNqPv1W0ocGIGxRs8kAExKKibcwOKC5hXYAiajoFMX3g1gA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)
总而言之：
当你想提高语言模型（LM）的性能时，可以从提示工程技术开始，这些技术投资小，能够快速迭代，验证解决问题的可行性。
这个过程不是线性的，可能需要多次迭代才能满意，你会在这些技术之间来回切换。
1. 我们隔着一条鸿沟远远地观望，我们会以为某件事情很简单
2. 但是当我们建立好评测标准、样本集合，并且实践之后，才会发现有很多原先没考虑到的问题。
3. 当我们解决一个问题之后，会发现指标提高一点（也可能证明不可行），这个过程不是线性的，没有一帆风顺的情况。都是启发式地，碰到问题，然后解决问题。
—— 完 ——
参考：
1、https://www.youtube.com/watch?v=ahnGLM-RC1Y
2、https://x.com/seclink/status/1796504016282935323