---
Updated: 2024-05-05T11:33
tags:
  - AI->-Theory
Created: 2024-05-05T11:33
---
[![](https://arxiv.org/static/browse/0.3.4/images/arxiv-logo-fb.png)](https://arxiv.org/static/browse/0.3.4/images/arxiv-logo-fb.png)
## ==Computer Science > Computation and Language==
==**arXiv:2405.00208**== ==(cs)==
==View a PDF of the paper titled A Primer on the Inner Workings of Transformer-based Language Models, by <PERSON> and 3 other authors==
[==View PDF==](https://arxiv.org/pdf/2405.00208)
==Abstract:The rapid progress of research aimed at interpreting the inner workings of advanced language models has highlighted a need for contextualizing the insights gained from years of work in this area. This primer provides a concise technical introduction to the current techniques used to interpret the inner workings of Transformer-based language models, focusing on the generative decoder-only architecture. We conclude by presenting a comprehensive overview of the known internal mechanisms implemented by these models, uncovering connections across popular approaches and active research directions in this area.==
## ==Submission history==
==From: <PERSON> [==[==view email==](https://arxiv.org/show-email/5f5315cf/2405.00208)==]====  
  
==[==**[v1]**==](https://arxiv.org/abs/2405.00208v1) ==Tue, 30 Apr 2024 21:20:17 UTC (3,012 KB)====  
  
====**[v2]**== ==Thu, 2 May 2024 01:29:17 UTC (3,012 KB)==
==Full-text links:==
## ==Access Paper:==
==View a PDF of the paper titled A Primer on the Inner Workings of Transformer-based Language Models, by Javier Ferrando and 3 other authors==
- [==View PDF==](https://arxiv.org/pdf/2405.00208)
- [==TeX Source==](https://arxiv.org/src/2405.00208)
- [==Other Formats==](https://arxiv.org/format/2405.00208)
==Current browse context:==
==cs.CL==
==export BibTeX citation==
### ==Bookmark==
[![](https://arxiv.org/static/browse/0.3.4/images/icons/social/bibsonomy.png)](https://arxiv.org/static/browse/0.3.4/images/icons/social/bibsonomy.png)
[![](https://arxiv.org/static/browse/0.3.4/images/icons/social/reddit.png)](https://arxiv.org/static/browse/0.3.4/images/icons/social/reddit.png)
==Bibliographic Tools==
## ==Bibliographic and Citation Tools==
==Bibliographic Explorer Toggle==
==Code, Data, Media==
## ==Code, Data and Media Associated with this Article==
==Demos==
## ==Demos==
==Related Papers==
## ==Recommenders and Search Tools==
==About arXivLabs==
## ==arXivLabs: experimental projects with community collaborators==
==arXivLabs is a framework that allows collaborators to develop and share new arXiv features directly on our website.==
==Both individuals and organizations that work with arXivLabs have embraced and accepted our values of openness, community, excellence, and user data privacy. arXiv is committed to these values and only works with partners that adhere to them.==
==Have an idea for a project that will add value for arXiv's community?== [==**Learn more about arXivLabs**==](https://info.arxiv.org/labs/index.html)==.==