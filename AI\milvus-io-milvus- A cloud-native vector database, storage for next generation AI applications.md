---
Updated: 2023-04-27T21:21
tags:
  - AI->-Embedding
  - AI->-<PERSON><PERSON><PERSON><PERSON>
Created: 2023-04-27T01:12
---
![[998c09ca-cfa6-4c01-ac75-3dfad7f4862b]]
![[68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4a6f696e2d536c61636b2d6f72616e67653f6c6f676f3d736c61636b266c6f676f436f6c6f723d7768697465267374796c653d666c61742d737175617265]]
![[68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f6c6963656e73652f6d696c7675732d696f2f6d696c767573]]
![[68747470733a2f2f696d672e736869656c64732e696f2f646f636b65722f70756c6c732f6d696c76757364622f6d696c767573]]
## What is Mil<PERSON><PERSON>?
![[milvus-horizontal-color.png]]
Milvus is an open-source vector database built to power embedding similarity search and AI applications. Milvus makes unstructured data search more accessible, and provides a consistent user experience regardless of the deployment environment.
Milvus 2.0 is a cloud-native vector database with storage and computation separated by design. All components in this refactored version of Milvus are stateless to enhance elasticity and flexibility. For more architecture details, see [Milvus Architecture Overview](https://milvus.io/docs/architecture_overview.md).
Milvus was released under the [open-source Apache License 2.0](https://github.com/milvus-io/milvus/blob/master/LICENSE) in October 2019. It is currently a graduate project under [LF AI & Data Foundation](https://lfaidata.foundation/).
## Key features
Details Details Details Details Details Details Details
## Quick start
### Start with Zilliz Cloud
Zilliz Cloud is a fully managed service on cloud and the simplest way to deploy LF AI Milvus®, See [Zilliz Cloud Quick Start Guide](https://zilliz.com/doc/quick_start) and start your [free trial](https://cloud.zilliz.com/signup).
### Install Milvus
- [Standalone Quick Start Guide](https://milvus.io/docs/v2.0.x/install_standalone-docker.md)
    
- [Cluster Quick Start Guide](https://milvus.io/docs/v2.0.x/install_cluster-docker.md)
    
- [Advanced Deployment](https://github.com/milvus-io/milvus/wiki)
    
### Build Milvus from source code
Check the requirements first.
Linux systems (Ubuntu 20.04 or later recommended):
```Plain
go: >= 1.18
cmake: >= 3.18
gcc: 7.5
```
MacOS systems with x86_64 (Big Sur 11.5 or later recommended):
```Plain
go: >= 1.18
cmake: >= 3.18
llvm: >= 15
```
MacOS systems with Apple Silicon (Monterey 12.0.1 or later recommended):
```Plain
go: >= 1.18 (Arch=ARM64)
cmake: >= 3.18
llvm: >= 15
```
Clone Milvus repo and build.
```Plain
# Clone github repository.
$ git clone https://github.com/milvus-io/milvus.git
# Install third-party dependencies.
$ cd milvus/
$ ./scripts/install_deps.sh
# Compile Milvus.
$ make
```
For the full story, see [developer's documentation](https://github.com/milvus-io/milvus/blob/master/DEVELOPMENT.md).

> **IMPORTANT** The master branch is for the development of Milvus v2.0. On March 9th, 2021, we released Milvus v1.0, the first stable version of Milvus with long-term support. To use Milvus v1.0, switch to [branch 1.0](https://github.com/milvus-io/milvus/tree/1.0).
## Milvus 2.0 vs. 1.x: Cloud-native, distributed architecture, highly scalable, and more
See [Milvus 2.0 vs. 1.x](https://github.com/milvus-io/milvus/blob/master/milvus20vs1x.md) for more information.
## Real world demos
|Title|Image search|Property|1|Files|
|---|---|---|---|---|
|[[AI/milvus-io-milvus- A cloud-native vector database, storage for next generation AI applications/Untitled Database/Untitled\|Untitled]]|[Image search](https://milvus.io/milvus-demos)|[Chatbots](https://milvus.io/milvus-demos)|[Chemical structure search](https://milvus.io/milvus-demos)||
  
  
### Image Search
Images made searchable. Instantaneously return the most similar images from a massive database.
### Chatbots
Interactive digital customer service that saves users time and businesses money.
### Chemical Structure Search
Blazing fast similarity search, substructure search, or superstructure search for a specified molecule.
## Bootcamps
Milvus [bootcamp](https://github.com/milvus-io/bootcamp) is designed to expose users to both the simplicity and depth of the vector database. Discover how to run benchmark tests as well as build similarity search applications spanning chatbots, recommendation systems, reverse image search, molecular search, and much more.
## Contributing
Contributions to Milvus are welcome from everyone. See [Guidelines for Contributing](https://github.com/milvus-io/milvus/blob/master/CONTRIBUTING.md) for details on submitting patches and the contribution workflow. See our [community repository](https://github.com/milvus-io/community) to learn about our governance and access more community resources.
### All contributors
![[68747470733a2f2f696d672e736869656c64732e696f2f62616467652f616c6c2d2d636f6e7472696275746f72732d3238362d6f72616e6765]]
![[26602940]]
![[24547351]]
![[9635216]]
![[33119433]]
![[12489985]]
![[90505226]]
![[115786031]]
![[40494761]]
![[114047052]]
![[13449703]]
![[54123439]]
![[53458891]]
![[72331432]]
![[40255591]]
![[36720318]]
![[37905059]]
![[34762375]]
![[4417873]]
![[47516502]]
![[1457728]]
![[58072531]]
![[45638240]]
![[64460989]]
![[34002927]]
![[8857059]]
![[15663612]]
![[88903134]]
![[55842817]]
![[50101579]]
![[40229765]]
![[57477222]]
![[3136012]]
![[54059881]]
![[35055583]]
![[17645053]]
![[2274405]]
![[53512883]]
![[35321989]]
![[29594737]]
![[33142505]]
![[20135478]]
![[24795136]]
![[58654486]]
![[27696701]]
![[64019322]]
![[7533395]]
![[81553353]]
![[1573213]]
![[47274057]]
![[116052805]]
![[74401713]]
![[109071306]]
![[5742796]]
![[64403786]]
![[49336176]]
![[5410298]]
![[27598602]]
![[6872198]]
![[57280231]]
![[106942883]]
![[9876551]]
![[29282370]]
![[45846277]]
![[21985684]]
![[4702509]]
![[10348819]]
![[66636289]]
![[43040147]]
![[36157116]]
![[104345188]]
![[27288593]]
![[41352919]]
![[11934432]]
![[20420181]]
![[11371498]]
![[45359033]]
![[39627130]]
![[51370125]]
![[74396087]]
![[48198922]]
![[3941604]]
![[35092554]]
![[68629395]]
![[48044391]]
![[350928]]
![[36330442]]
![[22510720]]
![[2356895]]
![[45024769]]
![[104769013]]
![[44166374]]
![[50362613]]
![[35889327]]
![[3009596]]
![[56624819]]
![[42060877]]
![[30914966]]
![[83755740]]
![[24309515]]
![[2155120]]
![[69145751]]
![[18375889]]
![[2233492]]
![[31087327]]
![[12387235]]
![[3439961]]
![[24692397]]
![[480395]]
![[83751381]]
![[23704769]]
![[14269809]]
![[84113973]]
![[1071648]]
![[39671710]]
![[653101]]
![[18002438]]
![[3992404]]
![[83751452]]
![[26356194]]
![[59249785]]
![[48800335]]
![[13999666]]
![[24242249]]
![[41563853]]
![[56623710]]
![[61805754]]
![[1488134]]
![[11514434]]
![[14878830]]
![[64584368]]
![[37609214]]
![[79587688]]
![[48523564]]
![[25433850]]
![[64510805]]
![[81822489]]
![[1094052]]
![[49153041]]
![[3909908]]
![[4769989]]
![[67679556]]
![[14368181]]
![[66515297]]
![[56617657]]
![[8500564]]
![[33207684]]
![[53246671]]
![[52496626]]
![[528003]]
![[15938850]]
![[103482615]]
![[98305308]]
![[1656002]]
![[75291211]]
![[81401074]]
![[21039333]]
![[64533877]]
![[86251631]]
![[4024711]]
![[24581746]]
![[1737680]]
![[93511422]]
![[2297455]]
![[52057195]]
![[34296482]]
![[31717785]]
![[67673717]]
![[48270786]]
![[58837504]]
![[22048793]]
![[9720105]]
![[14940941]]
![[16740944]]
![[45382760]]
![[66864]]
![[105927039]]
![[31131753]]
![[102851605]]
![[78945582]]
![[21237232]]
![[15364733]]
![[31589260]]
![[3893940]]
![[26682620]]
![[5290110]]
![[9841409]]
![[57976772]]
![[183388]]
![[95194]]
![[22544815]]
![[37039827]]
![[87847967]]
![[5696721]]
![[11742913]]
![[20559208]]
![[1751024]]
![[31059690]]
![[34152706]]
![[5617677]]
![[37684963]]
![[7496278]]
![[11356471]]
![[14035577]]
![[11576622]]
![[7888889]]
![[51972064]]
![[58909377]]
![[35092029]]
![[10884762]]
![[59124772]]
![[3996622]]
![[100122127]]
![[17746814]]
![[40378371]]
![[33335490]]
![[185051]]
![[46514371]]
![[49774184]]
![[39143280]]
![[53459423]]
![[200878]]
![[26541600]]
![[64083300]]
![[7374640]]
![[19733683]]
![[107831450]]
![[69466447]]
![[1500781]]
![[56469371]]
![[93502486]]
![[60854658]]
![[34635663]]
![[3454260]]
![[10708326]]
![[9817127]]
![[83750738]]
![[219938]]
![[17634030]]
![[24822588]]
![[814232]]
![[56624359]]
![[13817362]]
![[89676996]]
![[26307815]]
![[36727480]]
![[13234561]]
![[18096561]]
![[108661493]]
![[27938020]]
![[14267759]]
![[62299611]]
![[48882296]]
![[12595343]]
![[5432721]]
![[46886508]]
![[28949072]]
![[93316470]]
![[46207236]]
![[83447078]]
![[39088547]]
![[20124155]]
![[34647972]]
![[35444753]]
![[57252655]]
![[12216890]]
![[10089260]]
![[82361606]]
![[97278661]]
![[2282099]]
![[23047684]]
![[80064917]]
![[103410837]]
![[9016120]]
![[62009483]]
![[32416908]]
![[28284116]]
![[11961641]]
![[57790060]]
![[11935707]]
![[51014996]]
![[2993941]]
![[51948620]]
![[12268675]]
![[8857976]]
![[15153901]]
![[29620478]]
## Documentation
For guidance on installation, development, deployment, and administration, check out [Milvus Docs](https://milvus.io/docs). For technical milestones and enhancement proposals, check out [milvus confluence](https://wiki.lfaidata.foundation/display/MIL/Milvus+Home)
### SDK
The implemented SDK and its API documentation are listed below:
- [PyMilvus SDK](https://github.com/milvus-io/pymilvus)
- [Java SDK](https://github.com/milvus-io/milvus-sdk-java)
- [Go SDK](https://github.com/milvus-io/milvus-sdk-go)
- [Cpp SDK](https://github.com/milvus-io/milvus-sdk-cpp)(under development)
- [Node SDK](https://github.com/milvus-io/milvus-sdk-node)
- [Rust SDK](https://github.com/milvus-io/milvus-sdk-rust)(under development)
- [CSharp SDK](https://github.com/milvus-io/milvus-sdk-csharp)(under development)
- [Ruby SDK](https://github.com/andreibondarev/milvus)(under development)
### Attu
Attu provides an intuitive and efficient GUI for Milvus.
- [Quick start](https://github.com/zilliztech/milvus-insight#quick-start)
## Community
Join the Milvus community on [Slack](https://milvusio.slack.com/join/shared_invite/zt-1slimkif6-8uWK0XPL8adve6vSD4jSwg) to share your suggestions, advice, and questions with our engineering team.
![[68747470733a2f2f6173736574732e7a696c6c697a2e636f6d2f726561646d655f736c61636b5f346130376334633932662e706e67]]
You can also check out our [FAQ page](https://milvus.io/docs/performance_faq.md) to discover solutions or answers to your issues or questions.
Subscribe to Milvus mailing lists:
- [Technical Steering Committee](https://lists.lfai.foundation/g/milvus-tsc)
- [Technical Discussions](https://lists.lfai.foundation/g/milvus-technical-discuss)
- [Announcement](https://lists.lfai.foundation/g/milvus-announce)
Follow Milvus on social media:
- [Medium](https://medium.com/@milvusio)
- [Twitter](https://twitter.com/milvusio)
- [Youtube](https://www.youtube.com/channel/UCMCo_F7pKjMHBlfyxwOPw-g)
## Reference
Reference to cite when you use Milvus in a research paper:
```Plain
@inproceedings{2021milvus,
 title={Milvus: A Purpose-Built Vector Data Management System},
 author={Wang, Jianguo and Yi, Xiaomeng and Guo, Rentong and Jin, Hai and Xu, Peng and Li, Shengjun and Wang, Xiangyu and Guo, Xiangzhou and Li, Chengming and Xu, Xiaohai and others},
 booktitle={Proceedings of the 2021 International Conference on Management of Data},
 pages={2614--2627},
 year={2021}
}
@article{2022manu,
 title={Manu: a cloud native vector database management system},
 author={Guo, Rentong and Luan, Xiaofan and Xiang, Long and Yan, Xiao and Yi, Xiaomeng and Luo, Jigao and Cheng, Qianya and Xu, Weizhi and Luo, Jiarui and Liu, Frank and others},
 journal={Proceedings of the VLDB Endowment},
 volume={15},
 number={12},
 pages={3548--3561},
 year={2022},
 publisher={VLDB Endowment}
}
```
## Acknowledgments
Milvus adopts dependencies from the following:
- Thanks to [FAISS](https://github.com/facebookresearch/faiss) for the excellent search library.
- Thanks to [etcd](https://github.com/coreos/etcd) for providing great open-source key-value store tools.
- Thanks to [Pulsar](https://github.com/apache/pulsar) for its wonderful distributed pub-sub messaging system.
- Thanks to [RocksDB](https://github.com/facebook/rocksdb) for the powerful storage engines.
Milvus is adopted by following opensource project:
- [Towhee](https://github.com/towhee-io/towhee) a flexible, application-oriented framework for computing embedding vectors over unstructured data.
- [Haystack](https://github.com/deepset-ai/haystack) an open source NLP framework that leverages Transformer models
- [Langchain](https://github.com/hwchase17/langchain) Building applications with LLMs through composability
- [GPTCache](https://github.com/zilliztech/GPTCache) a library for creating semantic cache to store responses from LLM queries.