---
Updated: 2024-03-11T14:42
tags:
  - AI->-Embedding
  - AI->-Voice
URL: https://www.sciencedirect.com/science/article/abs/pii/S0952197623014161
Created: 2024-03-10T23:27
---
## Abstract
The construction of speaker-specific acoustic models for automatic speaker recognition is almost exclusively based on deep neural network-based speaker embeddings. This work aims to review the recent progress in speaker embedding development and to perform an experimental benchmark experimental comparison among the state-of-the-art deep speaker representations for a Speaker Verification (SV) task. The performance evaluation of the existing and proposed models on the VoxCeleb1 benchmark database shows that the [SV systems](https://www.sciencedirect.com/topics/computer-science/verification-system) based on r-vectors with a Res2Net convolutional architecture including multi-head attention pooling and additive margin softmax outperform other solutions such as d-vectors, x-vectors and conventional r-vectors. In addition, an ensemble network is proposed that fuses the best-performing speaker embeddings. It was found that different types of embeddings can contain complementary speaker-related information. We show that a concatenation of x-vectors and r-vectors can further improve the performance of the SV system. The best-performing embedding ensemble achieves an [Equal Error Rate](https://www.sciencedirect.com/topics/engineering/equal-error-rate) of 2.52% within the Voxceleb1 benchmark test, which is lower than other published results and obtained on the same dataset using the standard Voxceleb1 evaluation methodology.
## Introduction
Speech is a fundamental way for humans to communicate. One of the properties of speech, its dependence on the speaker, allows us to recognize familiar voices, exemplified by identifying a friend’s voice over the phone. Humans can extract speaker-specific voice features (e.g., pitch, timbre, prosody), crafting latent speaker identity representations for recognition despite challenging acoustic conditions. In speaker identification, invariant features persist consistently, distinguishing individual voices even amidst variations such as emotions or health conditions (Hansen and Hasan, 2015).
Automatic speaker recognition is traditionally divided into two tasks: identification and verification. Speaker Identification (SI) is a task in which a person is recognized among multiple enrolled speakers. SI is categorized as a closed-set identification problem in cases where the test utterance is produced by one of the speakers already included in the enrolled set. When the identification involves new speakers not included in the system’s training data, it is categorized as an open-set problem. Speaker Verification (SV) involves assessing whether a person’s claimed identity is valid, leading to either acceptance or rejection based on the score. As noted in Bai and Zhang (2021), SV can be regarded as a specific instance of open-set speaker identification.
Typical include cases are voice authentication for logging into a mobile application, authentication of callers in call centres or secure access in telephone banking. The demand for SV solutions is currently increasing with the advent of new emerging services and technologies, such as voice cloud services for intelligent vehicles, chatbots, voice agents (Gao et al., 2018), social robots, IoT device control and interaction (Zhang et al., 2022, Kaur et al., 2020), or e-commerce applications (Drusinsky, 2021). In many cases, speaker recognition is an important part of multifactor or multimodal authentication for highly secure access (Drusinsky, 2021, Wu et al., 2019).
This study addresses the problem of the text-independent SV task, which finds broader application in real-world systems (AboElenein et al., 2016, Wayman et al., 2005) and is also increasingly emerging in benchmark initiatives (Doddington et al., 2000, Nagrani et al., 2020, Brown et al., 2022, Heo et al., 2020, Chung et al., 2019). The text-independent SV task has no constraints on what is said. The speaker recognition process relies on features influenced by both the physical structure of an individual’s vocal tract and the individual’s behavioural characteristics but should remain invariant to phonological content.
For almost a decade, the embeddings extracted from the generative probabilistic model were the dominant approach in automatic speaker recognition. However, in other machine learning domains, such as text or image processing, related embeddings were extracted through discriminatively trained Deep Neural Networks (DNNs). This deep learning paradigm was swiftly embraced in speech processing, allowing models to learn latent representations in a novel feature space. Here, intra-class distances (within the same speaker) are minimized, while inter-speaker distances are maximized to a considerable extent (Bai and Zhang, 2021).
Following the success of the i-vector-based speaker embedding approach (Dehak et al., 2011), which has enabled the deployment of the SV/SI systems into practical applications, numerous novel speaker embedding solutions (utilizing deep learning) have been proposed (Poddar et al., 2018, Bai and Zhang, 2021, Jung et al., 2022). The term embedding refers to a unique ”fingerprint”, which is a compressed representation of each utterance or recording and becomes a high-level feature for further classification. The speaker embedding is a simple but effective methodology to represent the speaker’s identity in a compact way as a vector of fixed size.
The state-of-the-art SV procedures make it possible to achieve excellent recognition accuracy in acoustically controlled conditions, however, in real acoustic environments, speaker recognition is still a challenging task (Poddar et al., 2018, Bai and Zhang, 2021, Hanifa et al., 2021). The performance of SV systems decreases significantly when the speech signal is damaged by interfering factors (interfering speech, background sounds, distortion of the transmission channel, and reverberation) (Al-Karawi, 2021). Therefore, robustness to interference is a key factor when deploying recognition systems in real-world environments.
The motivation behind this article is to carry out a benchmark to assess the performance of the state-of-the-art DNN-based SV systems in the wild. To the best of our knowledge, an objective and complex benchmark comparison of the speaker embedding-based systems is lacking, due to diverse testing conditions. In this study, several strategies for learning speaker embeddings using different DNN architectures are investigated and evaluated. The main contributions of this paper are summarized as follows:
- An extensive benchmark evaluation of all possible state-of-the-art architectures for speaker embedding extraction is conducted. The comparison is performed in terms of both recognition accuracy and computational load for the text-independent SV task.
    
- Further improvements to existing DNN architectures using different types of network components and parameters are proposed.
    
- An ensemble network fusing the best-performing speaker embeddings is proposed. The performance evaluation of such an ensemble network shows that the concatenation of embeddings of different types can further increase SV accuracy.
    
The proposed solutions outperform other state-of-the-art systems published, whose results have been obtained on the same dataset using the standard Voxceleb1 evaluation methodology (Bai and Zhang, 2021).
The rest of the paper is organized as follows. Section 2 introduces the theoretical background of speaker embedding extraction. This section also addresses the impact of pooling strategies, Softmax functions, and back-end scoring on the performance of DNN-based SV systems. Section 3 provides a description of the benchmark evaluation methodology and the configuration specifications of the systems that were subjected to testing. Section 4 summarizes and discusses the obtained evaluation results of the existing and proposed SV systems. The obtained results are also compared with state-of-the-art systems published in the literature. The conclusion of the paper is presented in Section 5.
## Section snippets
## Background and related work
A basic block diagram of the SV system utilizing DNN as a speaker extractor is shown in Fig. 1. Two approaches to SV are distinguished: end-to-end and two-step. The end-to-end SV systems take a pair of enrolment and test utterances as input and directly compute the similarity score using pairwise loss functions such as Binary Cross-Entropy or Contrastive loss (Fig. 1a) (Bai and Zhang, 2021). A more advanced approach involves a two-step SV process, in which embedding extraction is performed
## Experiment setup
Following the state-of-the-art approaches reviewed in Section 2, we designed speaker embedding-based solutions, which are further selected for experimental evaluation on the text-independent SV task. Standard d-vector, r-vector and x-vector-based systems as well as a variety of their modifications are examined. In all the developed systems, the speaker embeddings are extracted by DNN from the conventionally pre-processed speech: audio recordings are initially divided into overlapping signal
## Impact of the network components on the x-vector system performance
In this section, we explore modifications of the x-vector architectures. We experimented with the network components listed in Table 6, aiming to further increase the performance of the system and investigate the impact of the selected network components on the overall performance.
In particular, we investigated the impact of the input speech features, the types of the pooling layer, the Softmax function, and the adaptation algorithm on the system’s performance. The selected component types (see
## Conclusions
In this work, we carried out an extensive experimental benchmark comparison of the state-of-the-art speaker verification system. We also proposed and evaluated several improvements to the existing speaker embedding models, as follows: Several modifications of Softmax functions as well as different improved pooling layers were tested. In addition, we proposed an ensemble embedding model that combines the most discriminative deep speaker features. The results of the experimental study and the
## CRediT authorship contribution statement
**Maros Jakubec:** Methodology, Software, Investigation, Writing – original draft, Project administration. **Roman Jarina:** Conceptualization, Validation, Writing – review & editing, Supervision, Funding acquisition. **Eva Lieskovska:** Formal analysis, Writing – review & editing. **Peter Kasak:** Resources, Data curation, Visualization.
## Declaration of competing interest
The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper.
## Acknowledgements
This work was supported by the project of Operational Programme Integrated Infrastructure: Independent research and development of technological kits based on wearable electronics products, Slovakia, as tools for raising hygienic standards in a society exposed to the virus causing the COVID-19 disease, ITMS2014+ code 313011ASK8. The project is co-funding by European Regional Development Fund .
## References (82)
- BaiZ. _et al._(2021)
    
    ### [Speaker recognition based on deep learning: An overview](https://www.sciencedirect.com/science/article/pii/S0893608021000848)
    
    ### Neural Netw.
    
- DoddingtonG.R. _et al._(2000)
    
    ### [The NIST speaker recognition evaluation–overview, methodology, systems, results, perspective](https://www.sciencedirect.com/science/article/pii/S0167639399000801)
    
    ### Speech Commun.
    
- LangK.J. _et al._(1990)
    
    ### [A time-delay neural network architecture for isolated word recognition](https://www.sciencedirect.com/science/article/pii/089360809090044L)
    
    ### Neural Netw.
    
- LiuY. _et al._(2019)
    
    ### [Introducing phonetic information to speaker embedding for speaker verification](https://www.sciencedirect.com/science/article/pii/S0378475418302775)
    
    ### EURASIP J. Audio Speech Music Process.
    
- ReynoldsD.A. _et al._(2000)
    
    ### [Speaker verification using adapted Gaussian mixture models](https://www.sciencedirect.com/science/article/pii/S1051200499903615)
    
    ### Digital Signal Process.
    
- WangC. _et al._
    
    ### [Hierarchically attending time-frequency and channel features for improving speaker verification](https://www.sciencedirect.com/science/article/pii/S0168583X21002512)
    
- ZhengQ. _et al._(2023)
    
    ### [MSRANet: Learning discriminative embeddings for speaker verification via channel and spatial attention mechanism in alterable scenarios](https://www.sciencedirect.com/science/article/pii/S095741742300012X)
    
    ### Expert Syst. Appl.
    
- Abdel-HamidO. _et al._(2014)
    
    ### Convolutional neural networks for speech recognition
    
    ### IEEE/ACM Trans. Audio Speech Lang. Process.
    
- AboEleneinN.M. _et al._
    
    ### Improved text-independent speaker identification system for real time applications
    
- Al-KarawiK.A. (2021)
    
    ### Mitigate the reverberation effect on the speaker verification performance using different methods
    
    ### Int. J. Speech Technol.