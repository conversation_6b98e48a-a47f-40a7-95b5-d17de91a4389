---
Updated: 2023-12-20T12:00
tags:
  - AI->-Programming
  - AI->Automation
URL: https://brain.ai/#/
Created: 2023-12-20T00:03
---
We've had our smartphones for the last 14 years. On July 28, 2021, <PERSON> is reimagining the way we interact with our phones.
Introducing Natural - the first generative interface that allows software to be in sync with your intention. You no longer go to apps, apps come to you. Simply say what you need and the right app forms itself around your words.
08:05
% buffered
00:00
Share link
0
WHO ARE WE?
At Brain.ai, we build computers that think. We invent new technologies and design metaphors that allow computers to become an extension of our minds instead of simply our hands. As we transition from the information age to the intelligence age, we bridge the existing world of software with emerging general intelligence in a natural and humane way.
As pioneers of the few-shot learning approach to NLP in 2016, we stand at the intersection of advancing the latest technology and design. Our investors include <PERSON><PERSON>, Goodwater Capital, Scott Cook and WTT Investment.
![[load-back.png]]
0
# Natural AI
Natural AI, our first consumer product, is the world's first generative interface. You no longer go to Apps, Apps come to you. Simply say what you need and the right app forms itself around your words - now fulfilling millions of requests each month.
Natural clears away the clutter on your screen. It allows you to focus on what you want, not how to get there.
WHO AREWE LOOKING FOR ?
Subscribe
If you are extremely curious, and won't settle for a tech company that makes incremental improvements. You want your work to be remembered, to add up to something big. You want to do work that keeps you up at night from excitement. You want your life to be filled with 'insane moments of realization.'
UX Designer
Apply
San Mateo，CA Full-time
Responsibilities:
We are looking for a UX Designer to join us in inventing the metaphors and patterns that make up this experience. The UX Designer thinks laterally and finds clarity through making. They are excited about metaphors, and jump at the chance to play with new tools and technologies. They move between engineering and design with fluidity, and values vision over pure craftsmanship. They are flexible about ideas, and uncompromising about doing the right thing.
Qualifications and Skills:
- 5+ years of progressive experience in product design, web design, UI/UX design and/or art direction with a focus on web, mobile and/or enterprise products.
- Strong portfolio of wireframes, user interface mockups, and prototypes for real products - on the market.
- Experience working with stakeholders in developing value proposition canvas, customer journey mapping, and identifying KPI/OKRs.
- Deep knowledge of design tools - Figma, Sketch and any other tools needed.
- Able to design autonomously, meet milestones and comfort working in agile, fast-paced surroundings.
- Excited about next-generation interfaces.
- Strong, demonstrated visual design aptitude – a great sense for color, form, typography and other design elements is shown in your work.
- Meticulous attention to detail, detail-oriented, organized and strong project management skills.
- Comfort with ambiguity in early production definition stages.
- Mindset for creating clean, “less is more” user interface designs.
Apply
Senior NLP Engineer
Apply
San Mateo，CA Full-time
Responsibilities:
In this role, you’ll be sitting at the intersection of UX, NLP and engineering, designing improvements to our natural language engine, the core technology behind the intelligence of our product. You’ll get to solve difficult problems like semantic understanding, entity recognition, dialogue state tracking, knowledge base induction, personalization and active learning. On this path to product launch, you’ll have a hand in shaping a highly personal and intent-driven user experience — bringing the Brain.ai product vision to life.
Qualifications and Skills:
- 2+ years of experience in prototyping, evaluating, and deploying production NLP systems
- 3+ years of experience with Python
- Background and experience with both knowledge-based (e.g, dependency parsing) and model-based (e.g., deep learning) NLP techniques
- Familiarity and experience with tools for prototyping and deploying deep learning models (Tensorflow, PyTorch, TFX, etc.)
- Fluency in state-of-the-art deep learning and NLP methods (CNNs, RNNs, transformers, pre-training, fine-tuning, transfer learning, entity recognition & typing, attention, ontologies etc.)
Nice to Haves:
- MS/PhD with a focus in NLP, or related publications.
- Experience in building ontologies, including data acquisition, cleaning, and organizing.
- Expertise in developing voice assistants, dialogue systems or similar conversational AI products.
- Previous experience in a startup environment and an appetite for building consumer products.
Apply
Senior Backend Engineer
Apply
San Mateo，CA Full-time
Responsibilities:
As a backend engineer at Brain, you will be responsible for developing and maintaining our backend services, collaborating with a team of diverse engineers to bring a new, paradigm-shifting technology to market. In this position, you have the opportunity to act as a key contributor in our technology, and solve new and challenging problems each day. We are excited about people who can think critically, collaborate with others, and focus on creating a revolutionary product in a progressive startup setting.
Qualifications and Skills:
- You have excellent software development skills in multiple languages and environments (NodeJS, Python, Go).
- You have experience with database and schema design (e.g. MySQL, Postgres, DocumentDB).
- Experience with Infrastructure-as-code processes (via Terraform, Ansible, Docker, GKE).
- Working knowledge of Amazon Web Services or Google Cloud Platform.
- Empathy - you can clearly communicate your thoughts and opinions to others.
- You enjoy solving problems and helping others meet our goals.
Preferred Qualifications:
- Bachelor's degree in Computer Science, Engineering, or equivalent experience.
- Master’s degree in Computer Science, Computer Engineering.
Apply
Marketing Manager
Apply
San Mateo，CA Full-time
Responsibilities:
We are looking for a high-performing, creative, and growth-oriented marketing leader to lead Mobile Apps at Brain Technologies. In this role you will build and execute marketing plans (audience, budget, metrics, channels) that drive adoption of the Natural app, work closely with our product team by carrying out experiment and communicating insights. The position is strategic, but more importantly, executional. The successful candidate possesses sharp product and business acumen, is results oriented and works collaboratively in a fast-paced environment. You are data driven and a curious learner to glean insights and tell a story based on data. You thrive in an ever changing, dynamic environment and lead cross functional teams in highly matrixed organization.
Qualifications and Skills:
- 5+ years of marketing experience across tech enabled consumer products, consulting, CPG or multi-sided global marketplaces. MBA preferred but not required.
- Successfully built and shaped the planning and execution of multiple, complex marketing programs, e.g., experimentation, ROI monitoring, incrementality testing.
- Deep analytics background and a high degree of proficiency with different product analytics tools. e.g., Amplitude. Data-driven decision making within imperfect or incomplete information environments.
- Strong ability to develop creative briefs to inform creative execution for marketing strategies. Review, provide feedback and approvals in the creative development process.
- Strong leadership presence and ability to lead directly and through influence to drive execution of strategy.
- Knowledge of paid and funnel tracking platforms (e.g. Facebook Ad Manager, Google AdWords, Google Tag Manager, Tiktok Ad Manager etc).
Apply
Software Engineer, NLP Platform
Apply
San Mateo，CA Full-time
Responsibilities:
As a Software Engineering for the NLP platform at Brain, you’ll be sitting at the intersection of NLP and engineering, putting state-of-the-art customer understanding technologies out there “in the wild,” servicing thousands of users on our iOS consumer app. You’ll also be building data pipelines that create the training data necessary for high-performing NLP and develop and maintain critical dashboards that help us understand how we’re doing on a day-to-day basis.
Qualifications and Skills:
- 2+ years experience deploying production web services using Python, including real-time performance implications/issues.
- Experience with SQL and NoSQL databases such as MySQL, Postgres, Elasticsearch, MongoDB, GraphQL, etc…
- Background and experience with Python data cleaning/manipulation: pandas, etc...
- Experience designing, developing, and maintaining data and ETL pipelines.
- Empathy - you can clearly communicate your thoughts and opinions to others.
- You are fearlessly self-driven, yet enjoy helping others meet our goals.
- You enjoy solving problems and helping others meet our goals.
Nice to Haves:
- M.S. in Computer Science or related field.
- Background in both knowledge-based (e.g, dependency parsing) and model-based (e.g., deep learning) NLP techniques.
- Experience with formal knowledge base technologies: Neo4j, OWL, Sparql, etc…
- Experience deploying Python web services on a large scale using common cloud platforms: AWS, GCloud, Azure, etc…
- Previous experience in a startup environment and an appetite for building consumer products.
Apply
Quality Assurance iOS Engineer
Apply
San Mateo，CA Full-time
Responsibilities:
We learn directly from our users to iterate improvements. We complete that feedback loop by coordinating with customer support, analytics, and scrubbing bug reports. From there, we coordinate with design and development to integrate functional tests into continuous integration.
Qualifications and Skills:
- At least 2 years iOS Engineering.
- Have solid understanding of Swift.
- Experience with XCTest.
- Experience with testing mobile platforms on both physical devices and simulators.
- Integrate UX tracking/ performance Analytics.
Preferred Qualifications:
Bachelor's degree in Computer Science, Engineering, or equivalent experience.
Nice to Haves:
- Bilingual in Mandarin.
- Familiarity with Firebase, Amplitude, UXCam, Instabug.
Apply
Natural Language Processing Engineer
Responsibilities:
- Learning from user interactions with our NLP system to identify failure modes and ship robust solutions to production.
- Collaborating with and assisting other engineers and designers to develop new computational-linguistics or deep-learning based solutions for natural language understanding and reasoning.
- Improving Brain’s understanding of the real-world from a NLP perspective – of restaurants, brands, products, contextual information and more.
- Build and maintain Python web services for NLP that support thousands of users of Natural IOS application.
- Design and build pipelines for automatic training and evaluation of state-of-art NLP models.
- Extend and maintain critical dashboards used to monitor application performance.
- Interface with both NLP and back-end engineering team.
- Develop automated Natural Language Processing application, which can update the model based on the data distribution and inference accuracy.
- Integrate the state-of-art Natural Language Processing model, like GPT3, into our app
Qualifications and Skills:
- Job entails working with and requires Master’s degree in Computational Linguistics, Computer Science, Software Engineering, or equivalent with 2 years of experience including: Python, Linguistic analysis, Machine Learning, Deep Learning, Natural Language Understanding, Natural Language Generation.
- Employer will accept any suitable combination of education, training or experience. This should be read to mean that the employer requires: Master’s degree in Computational Linguistics, Computer Science, Software Engineering or equivalent with 2 years of experience in the job offered, NLP Engineer, Software Engineer, Machine learning Engineer, or equivalent.
To apply please mention Job Code B02 and send resumes to:
Attn. Mila Hernandez, HR Payroll Manager, Brain Technologies, Inc., 135 W 25th Avenue, \#938, San Mateo, CA 94403.
LOOKING FORAN INVITE?
With Brain's AI, your words can take you anywhere. You can now apply to test out the first ever Generative Computer Interface. Simply enter your email below to apply. Once approved, download instructions will be sent to you via email.