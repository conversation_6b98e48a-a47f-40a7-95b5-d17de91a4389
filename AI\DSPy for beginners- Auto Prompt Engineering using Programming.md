---
Updated: 2024-05-27T22:04
tags:
  - AI->-Prompt
Created: 2024-05-27T22:04
---
[![](https://miro.medium.com/v2/da:true/resize:fit:1200/0*0mC1H6UcbQnyTUw7)](https://miro.medium.com/v2/da:true/resize:fit:1200/0*0mC1H6UcbQnyTUw7)
## ==<PERSON><PERSON><PERSON><PERSON>’s alternate for building Generative AI apps==
[![](https://miro.medium.com/v2/resize:fill:55:55/1*vyvhK_h4zA05mg_Y-n2qBA.jpeg)](https://miro.medium.com/v2/resize:fill:55:55/1*vyvhK_h4zA05mg_Y-n2qBA.jpeg)
[![](https://miro.medium.com/v2/resize:fill:30:30/1*dHhREpDd71nTWkahuiiMJg.jpeg)](https://miro.medium.com/v2/resize:fill:30:30/1*dHhREpDd71nTWkahuiiMJg.jpeg)
[==Mehul Gupta==](https://medium.com/@mehulgupta_7991?source=post_page-----5b6005228e64--------------------------------)
==Published in==
[==Data Science in your pocket==](https://medium.com/data-science-in-your-pocket?source=post_page-----5b6005228e64--------------------------------)
==3 min read==
==May 14, 2024==
==If you’re in the field of AI and ML, you must be having a gala time right now as there are a lot of new & exciting things coming at a rapid pace.==
==My debut book: LangChain in your Pocket is out now!!==
[==LangChain in your Pocket: Beginner's Guide to Building Generative AI Applications using LLMs==](https://www.amazon.com/LangChain-your-Pocket-Generative-Applications-ebook/dp/B0CTHQHT25?source=post_page-----5b6005228e64--------------------------------)==  
  
==[==--------------------------------------------------------------------------------------------==](https://www.amazon.com/LangChain-your-Pocket-Generative-Applications-ebook/dp/B0CTHQHT25?source=post_page-----5b6005228e64--------------------------------)
### [==Amazon.com: LangChain in your Pocket: Beginner's Guide to Building Generative AI Applications using LLMs eBook : Gupta…==](https://www.amazon.com/LangChain-your-Pocket-Generative-Applications-ebook/dp/B0CTHQHT25?source=post_page-----5b6005228e64--------------------------------)
[==www.amazon.com==](https://www.amazon.com/LangChain-your-Pocket-Generative-Applications-ebook/dp/B0CTHQHT25?source=post_page-----5b6005228e64--------------------------------)
==I will be talking about the following topics in this post==
==Code tutorial==
==LangChain vs DSPy==
==How DSPy automates Prompt Engineering?==
==Important compoents of DSPy==
==I began the year with LangChain and a deep dive into the framework for building Generative AI applications. In this post, I will be talking about an important alternate for LangChain, especially if you’re a programmer i.e. DSPy.==
# ==Code Tutorial==
==Before starting off with any theory, I believe one must actually see how DSPy works to get a hang around the package by building a few dummy usecases. Checkout the below tutorial to understand the “Hello World” of DSPy==
[https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fwww.youtube.com%2Fembed%2FIiaXLP3JKr4%3Ffeature%3Doembed&display_name=YouTube&url=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DIiaXLP3JKr4&image=https%3A%2F%2Fi.ytimg.com%2Fvi%2FIiaXLP3JKr4%2Fhqdefault.jpg&key=a19fcc184b9711e1b4764040d3dc5c07&type=text%2Fhtml&schema=youtube](https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fwww.youtube.com%2Fembed%2FIiaXLP3JKr4%3Ffeature%3Doembed&display_name=YouTube&url=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DIiaXLP3JKr4&image=https%3A%2F%2Fi.ytimg.com%2Fvi%2FIiaXLP3JKr4%2Fhqdefault.jpg&key=a19fcc184b9711e1b4764040d3dc5c07&type=text%2Fhtml&schema=youtube)
==Code tutorial for DSPy==
==As you must have seen in the above tutorial, DSPy has a big edge over LangChain i.e.== ==_**it doesn’t require Prompt Engineering to be done manually by the user.**_== ==If you have developed some Generative AI applications or at least used ChatGPT, you must be knowing how important the prompt is and it may take you several hours to come to that one good prompt. Hence DSPy is a game changer.==
# ==How is it different from LangChain?==
==In many ways. The most important one being LangChain applications require the user to enter prompts manually in some way or the other but not DSPy. You can check other differences below:==
[https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fwww.youtube.com%2Fembed%2F3QbiUEWpO0E%3Ffeature%3Doembed&display_name=YouTube&url=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3D3QbiUEWpO0E&image=https%3A%2F%2Fi.ytimg.com%2Fvi%2F3QbiUEWpO0E%2Fhqdefault.jpg&key=a19fcc184b9711e1b4764040d3dc5c07&type=text%2Fhtml&schema=youtube](https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fwww.youtube.com%2Fembed%2F3QbiUEWpO0E%3Ffeature%3Doembed&display_name=YouTube&url=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3D3QbiUEWpO0E&image=https%3A%2F%2Fi.ytimg.com%2Fvi%2F3QbiUEWpO0E%2Fhqdefault.jpg&key=a19fcc184b9711e1b4764040d3dc5c07&type=text%2Fhtml&schema=youtube)
==But if you know, a prompt has to be passed to the LLM for generating any output. then==
# ==How DSPy automates prompt engineering?==
==Instead of handcrafting prompts, DSPy uses an “Optimizer” component to automatically generate and optimize prompts for the defined task logic==
1. ==**Bootstrapping:**== ==Starting with an initial seed prompt, DSPy iteratively refines it based on the LM’s outputs and user-provided examples/assertions==
2. ==**Prompt Chaining**====: Breaking down complex tasks into a sequence of simpler sub-prompts==
3. ==**Prompt Ensembling**====: Combining multiple prompt variations to improve performance==
==The optimization process treats prompt engineering as a machine learning problem, using metrics like accuracy on examples to guide the search for better prompts.==
# ==Important components of DSPy==
==Hope the code tutorial in the beginning gave you a hang around the package. We will now quickly discuss about some important components of DSPy package that one must know:==
## ==**Signatures**==
==Signatures are== ==**declarative specifications that define the input/output behavior**== ==of a DSPy module. They describe the task the language model should execute, rather than how to prompt it. A signature comprises:==
==A concise description of the sub-task==
==A description of one or more input fields (e.g., questions)==
==A description of one or more output fields expected (e.g., answers)==
==_**Example signatures:**_==
==Question Answering:== ==`"question -> answer"`==
==Sentiment Analysis:== ==`"sentence -> sentiment"`==
==Retrieval-Augmented QA:== ==`"context, question -> answer`==
## ==Modules==
==Modules abstract conventional== ==_**prompting techniques**_== ==like Chain-of-Thought or ReAct within an LLM pipeline.==
==Each built-in module handles a specific prompting technique and DSPy Signatures==
==Modules have learnable parameters like prompt components and LLM weights==
==Modules can be composed to create larger, complex modules.==
==Some of the build in Modules are predict, ReAct, ChainOfThought, Majority, etc.==
## ==Optimizers==
==Optimizers== ==_**adjust the settings of a DSPy program**_====, including prompts and language model weights, to enhance specified metrics like accuracy. Eventually optimizers play the most important role in automatic Prompt Engineering.==
==With this, I will be wrapping up this post. Trust me, if you’re into programming, you’re gonna love DSPy and can be your goto tool for production ready Generative AI applications. You can read my other blogs below==
[https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fmehulgupta2016154-resume-builder-streamlit-app-ajmqjx.streamlit.app%2FMy_blogs%3Fembed%3Dtrue&display_name=Streamlit&url=https%3A%2F%2Fmehulgupta2016154-resume-builder-streamlit-app-ajmqjx.streamlit.app%2FMy_blogs&image=https%3A%2F%2Fstorage.googleapis.com%2Fs4a-prod-share-preview%2Fdefault%2Fst_app_screenshot_image%2Fe5536a7f-91c2-4b74-a3b8-f3e8555d42fc%2FMy_blogs.png&key=a19fcc184b9711e1b4764040d3dc5c07&type=text%2Fhtml&schema=streamlit](https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fmehulgupta2016154-resume-builder-streamlit-app-ajmqjx.streamlit.app%2FMy_blogs%3Fembed%3Dtrue&display_name=Streamlit&url=https%3A%2F%2Fmehulgupta2016154-resume-builder-streamlit-app-ajmqjx.streamlit.app%2FMy_blogs&image=https%3A%2F%2Fstorage.googleapis.com%2Fs4a-prod-share-preview%2Fdefault%2Fst_app_screenshot_image%2Fe5536a7f-91c2-4b74-a3b8-f3e8555d42fc%2FMy_blogs.png&key=a19fcc184b9711e1b4764040d3dc5c07&type=text%2Fhtml&schema=streamlit)