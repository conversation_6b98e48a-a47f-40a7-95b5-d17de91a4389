---
Updated: 2024-08-28T21:41
tags:
  - AI->-<PERSON><PERSON><PERSON>
  - AI->-<PERSON><PERSON>e
  - <PERSON>->-Tools
Created: 2024-08-28T21:41
---
[==← Return to Tutorials==](https://tariksghiouri.github.io/tutorials.html)
[![](https://tariksghiouri.github.io/images/tutos/gguf_conversion.jpg)](https://tariksghiouri.github.io/images/tutos/gguf_conversion.jpg)
==This tutorial will guide you through the steps of converting a model from the safetensors format to the GGUF format and then uploading it to Hugging Face.==
## ==Step 1: Install Necessary Libraries and Tools==
==First, you need to install the required libraries and clone the== ==`llama.cpp`== ==repository:==
```plain
pip install transformers safetensors huggingface_hub
git clone https://github.com/ggerganov/llama.cpp.git
cd llama.cpp
mkdir build
cd build
cmake ..
cmake --build . --config Release
```
## ==Step 2: Download and Prepare the Model==
==Use the== ==`transformers`== ==library to download the model from Hugging Face in safetensors format and save it locally. Create a Python script== ==`download_model.py`== ==with the following content:==
```plain
from transformers import AutoModelForCausalLM, AutoTokenizer
model_name = "defog/llama-3-sqlcoder-8b"
save_path = "C:\\Users\\<USER>\\Desktop\\llama-3-sqlcoder-8b"
model = AutoModelForCausalLM.from_pretrained(model_name, use_safetensors=True)
tokenizer = AutoTokenizer.from_pretrained(model_name)
model.save_pretrained(save_path)
tokenizer.save_pretrained(save_path)
```
==Run the script to download the model:==
```plain
python download_model.py
```
## ==Step 3: Convert the Model to GGUF Format==
==Ensure the output directory exists by running the following script:==
```plain
import os
output_dir = "C:\\Users\\<USER>\\Desktop\\llama-3-sqlcoder-8b-gguf"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"Created directory: {output_dir}")
else:
    print(f"Directory already exists: {output_dir}")
```
==Run the script:==
```plain
python create_output_dir.py
```
==Now, convert the model to GGUF format:==
```plain
cd C:\\Users\\<USER>\\Desktop\\llama.cpp
python convert-hf-to-gguf.py "C:\\Users\\<USER>\\Desktop\\llama-3-sqlcoder-8b" --outtype f16 --outfile "C:\\Users\\<USER>\\Desktop\\llama-3-sqlcoder-8b-gguf\\ggml-model-f16.gguf"
```
## ==Step 4: Quantize the Model (Optional)==
==If you want to quantize the model, run the following command:==
```plain
cd build
./quantize "C:\\Users\\<USER>\\Desktop\\llama-3-sqlcoder-8b-gguf\\ggml-model-f16.gguf" "C:\\Users\\<USER>\\Desktop\\llama-3-sqlcoder-8b-gguf\\ggml-model-q4_0.gguf"
```
## ==Step 5: Upload to Hugging Face==
==Create a Python script== ==`upload_to_hf_http_with_progress.py`== ==to upload the converted model to Hugging Face:==
```plain
from huggingface_hub import HfApi, HfFolder
from pathlib import Path
import os
# Authenticate with Hugging Face
token = "your_huggingface_token"
HfFolder.save_token(token)
# Initialize the HfApi
api = HfApi()
# Define the repository name and local directory
repo_name = "MatrixIA/llama-3-sqlcoder-8b"
local_dir = "C:\\Users\\<USER>\\Desktop\\llama-3-sqlcoder-8b-gguf"
def upload_folder(api, folder_path, repo_id, token):
    folder_path = Path(folder_path)
    for path in folder_path.rglob("*"):
        if path.is_file():
            relative_path = path.relative_to(folder_path)
            print(f"Uploading {relative_path}...")
            api.upload_file(
                path_or_fileobj=str(path),
                path_in_repo=str(relative_path),
                repo_id=repo_id,
                repo_type="model",
                token=token
            )
# Upload files with progress
upload_folder(api, local_dir, repo_name, token)
```
==Replace== ==`"your_huggingface_token"`== ==with your actual Hugging Face token. Run the script to upload the files:==
```plain
python upload_to_hf_http_with_progress.py
```
## ==Conclusion==
==By following these steps, you can convert a model from safetensors format to GGUF format and upload it to Hugging Face. This tutorial covers installing necessary tools, downloading and preparing the model, converting the model, optionally quantizing it, and uploading it to Hugging Face.==