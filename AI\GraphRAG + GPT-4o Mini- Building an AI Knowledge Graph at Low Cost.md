---
Updated: 2024-08-02T10:38
tags:
  - AI->-Dataset
  - AI->-Graph
  - AI->-RAG
Created: 2024-08-02T10:38
---
[![](https://miro.medium.com/v2/resize:fit:1200/0*mXv2vIyLmh60Waax.jpg)](https://miro.medium.com/v2/resize:fit:1200/0*mXv2vIyLmh60Waax.jpg)
## ==Better results, lower prices — doesn’t that sound like a dream?==
[![](https://miro.medium.com/v2/resize:fill:88:88/1*xf1-UuzpNeh9dyDE626quw.jpeg)](https://miro.medium.com/v2/resize:fill:88:88/1*xf1-UuzpNeh9dyDE626quw.jpeg)
[![](https://miro.medium.com/v2/resize:fill:48:48/1*i6aZsChrqQVi4humMxqCCQ.png)](https://miro.medium.com/v2/resize:fill:48:48/1*i6aZsChrqQVi4humMxqCCQ.png)
[==Shuyi Wang==](https://wshuyi.medium.com/?source=post_page-----a4282440d92e--------------------------------)
==Published in==
[==Cubed==](https://blog.cubed.run/?source=post_page-----a4282440d92e--------------------------------)
==13 min read==
==Jul 21, 2024==
[![](https://miro.medium.com/v2/resize:fit:700/0*mXv2vIyLmh60Waax.jpg)](https://miro.medium.com/v2/resize:fit:700/0*mXv2vIyLmh60Waax.jpg)
# ==Limitations==
==First, let’s introduce a term: Retrieval-Augmented Generation (RAG).==
==In simple terms, RAG works by splitting large documents into multiple smaller paragraphs or segments. The main reason for this is that large language models have a limited context window length and cannot process information that exceeds this length all at once.==
==When a question is posed, RAG technology first retrieves relevant information from these segments, identifying several segments related to the question based on the similarity to the content of the inquiry, combining them into a context, and then inputting this information along with the question into the large language model, aiming to generate a more precise response.==
==However, we need to consider some potential limitations. For a sufficiently long document and a very complex question, relying solely on these few (merely suspected relevant) segments may not be enough. The real answer might not even be contained within them.==
==We have previously discussed many private knowledge bases, such as Quivr, Elephas, GPTs, Obsidian Copilot… With extended use, you’ll notice that responses from private knowledge bases often differ significantly from those obtained through fine-tuning models — fine-tuned models often can answer very complex questions based on private data, while the straightforward concatenation approach of RAG often yields less than ideal answers.==
# ==Knowledge Graph==
==These issues have led to the emergence of GraphRAG. GraphRAG is an innovative technology that combines knowledge graph structures with RAG methods, aiming to address the limitations of traditional RAG approaches.==
[![](https://miro.medium.com/v2/resize:fit:700/0*L6Dl1Mk35nP580N4.jpg)](https://miro.medium.com/v2/resize:fit:700/0*L6Dl1Mk35nP580N4.jpg)
==This is an innovative product developed by Microsoft’s research team, representing the latest advancements in RAG technology. Microsoft has also published related academic papers detailing the theoretical foundations and technical implementations of GraphRAG.==
[![](https://miro.medium.com/v2/resize:fit:700/0*YNplmm1AXEBrwpOx.jpg)](https://miro.medium.com/v2/resize:fit:700/0*YNplmm1AXEBrwpOx.jpg)
==So, what does “Graph” really mean here? On the homepage of GraphRAG, you will see a complex graph. This graph is not just a simple diagram; it represents a structured representation of knowledge. In this graph, each node may represent a concept or entity, while the edges connecting these nodes indicate the relationships between them.==
==Suppose there is a node in the graph labeled “Tiger” and another labeled “Rabbit,” with a line connecting them that reads “eats,” indicating that the relationship between the two is “Tiger eats Rabbit.” Of course, this is just a simplified example.==
==With such a graph, why combine it with the previously mentioned RAG? Because the traditional RAG method of “sifting through scattered fragments” actually performs poorly, we want to reveal the complex relationships between these concepts. During queries, it is no longer blindly searching for “potentially relevant” information fragments, but rather extracting a whole string of connected information based on the associations already established in the graph, allowing the large language model to process it all at once.==
[==Here is the GitHub URL for GraphRAG==](https://microsoft.github.io/graphrag/posts/get_started/)==. How popular is it on GitHub? It has already garnered over 11,000 stars.==
[![](https://miro.medium.com/v2/resize:fit:700/0*rwGMcRaRCN7gxnPx.jpg)](https://miro.medium.com/v2/resize:fit:700/0*rwGMcRaRCN7gxnPx.jpg)
==For a project, this is a very good achievement; I can only dream of my own project reaching such a level. It seems I still need to keep working hard.==
# ==Features==
==Let’s take a look at the features of GraphRAG. It integrates two major schools of artificial intelligence: one is deep learning, and the other is knowledge graphs.==
==Once, these two schools were in opposition. Later, it was discovered: why oppose each other?==
==If you now use deep learning to answer directly, the effect is poor, but if you combine it with a graph, the effect will be much stronger.==
==On the other hand, constructing a knowledge graph originally required manual extraction of entities and associations based on rules, which was a task that required throwing money and manpower at it. Later, it was found that using deep learning could effectively improve the efficiency of entity extraction. Especially with the advent of large language models, people found that extracting entities and associations became more accurate, simpler, and lower in cost. Therefore, the integration of the two is an inevitable development.==
==So what is GraphRAG good at after integration? It can== ==**connect the complex relationships and contexts between entities**====.==
==As we mentioned earlier, it can connect multiple information points for complex queries. This type of query cannot be completed simply by extracting a portion of information. The information fragments originally found based on similarity may not be sufficient to support answering the question. But now, obtaining relevant information based on actual associations produces much better results.==
==Additionally, GraphRAG has a comprehensive characterization of the dataset, allowing for a full expression of conceptual semantic information.==
==These two features working together have led to a significant improvement in the performance of GraphRAG. You’ll be able to observe this in the following examples.==
# ==Limitations==
==However, this technology is not perfect. The most significant issue it faces can be summed up in one word — expensive.==
==The official example mentions a book, which we will see shortly, with a physical length of about 200 pages. How much does it cost to turn it into a RAG graph?==
==11 dollars.==
==Some people find this too expensive; the cost of indexing an e-book is almost comparable to that of a physical book.==
==Do we have a solution then? We need to analyze.==
==Why is the implementation cost of GraphRAG so high? Because it uses the GPT-4 Turbo Preview model. The token cost of this model is relatively high, and since it needs to be called repeatedly during the graph construction process, the cost of GraphRAG remains high.==
==Since we’ve found the cause, can we use a more economical model to replace it?==
==This is a question that naturally comes to mind. Many people have already tried. For example, some have attempted to run small local models like Ollama, but the results were poor, and there were often issues during the construction process.==
==Some people have used Groq to do this, and they succeeded, but it requires quite a few complex configuration changes, which is very challenging for beginners.==
==The simplest method, of course, is to hope that OpenAI will release a more affordable model.==
==After much anticipation, good news has arrived.==
==OpenAI recently sent the users an email saying they have launched a new GPT-4o mini model.==
[![](https://miro.medium.com/v2/resize:fit:700/0*rCWUTNANxzEyow4v.jpg)](https://miro.medium.com/v2/resize:fit:700/0*rCWUTNANxzEyow4v.jpg)
==Don’t let the name mislead you — GPT-4o mini is actually positioned against the GPT-3.5 Turbo model, but it is even 60% cheaper than 3.5 Turbo.==
==I can’t help but admire OpenAI’s current naming approach. If you called it GPT 3.5 Turbo Plus, it might not have the same effect because people would think it’s just an improved version of 3.5, which, while cheaper, still wouldn’t satisfy them.==
==By naming it GPT-4o mini, people will imagine it as a good model — multimodal, fast, and cheap. Everyone will feel they are getting a bargain, being able to use a model that is comparable to GPT-4o, yet much cheaper than the original 3.5. This is, in any case, a sweet deal.==
==After I got access to GPT-4o mini, I immediately tested its Chinese writing capabilities. I shared the test results on Knowledge Planet (a platform for knowledge sharing).==
[![](https://miro.medium.com/v2/resize:fit:700/0*3qzM79EIuUKH12XI.jpg)](https://miro.medium.com/v2/resize:fit:700/0*3qzM79EIuUKH12XI.jpg)
==The translation goes as:==
==_It has been observed that GPT-40-mini performs remarkably well in handling Chinese writing. I have placed it at the final step of the audio transcription text polishing workflow, and the results feel quite “as I wish, according to my intentions,” as if I were equipped with a dedicated secretary._==
==However, don’t misunderstand me; GPT-4o mini is still a small model. You can expect its Chinese output capabilities, but don’t think its reasoning and logical thinking abilities can match those of GPT-4o or Claude 3.5 Sonnet. So I placed it in the appropriate position in my workflow to make the best use of it.==
==Suddenly, I thought that GPT-4o mini could not only be used for text output but could also be combined with GraphRAG in a “dual sword combination.”==
==The mere thought of this excites me, so let’s take a look at the results.==
# ==Installation==
==First, we need to install GraphRAG, which is very straightforward using== ==`pip install`====.==
==`pip install graphrag`====```==
==It will install a series of dependencies, including various libraries and tools required by GraphRAG. The installation process may take some time, depending on your internet speed and computer performance.==
[![](https://miro.medium.com/v2/resize:fit:700/0*_-iNtpOuL9ZRW5mc.jpg)](https://miro.medium.com/v2/resize:fit:700/0*_-iNtpOuL9ZRW5mc.jpg)
==Once the installation is complete, we find a directory, create a new directory, and then execute this command below.==
==`mkdir -p ./ragtest/input`==
==What is the input here? It is the place where we store the input text — like the 200-page book or article mentioned earlier.==
==Let me demonstrate this in Visual Studio Code, as it is more intuitive.==
==After executing this command, a new folder will appear in the sidebar.==
[![](https://miro.medium.com/v2/resize:fit:700/0*dTNO2Cs66gISXKOm.jpg)](https://miro.medium.com/v2/resize:fit:700/0*dTNO2Cs66gISXKOm.jpg)
==Next, we need to download the book materials. Here, the example from the GraphRAG official website uses Project Gutenberg, which has many free books. Project Gutenberg is a volunteer project dedicated to creating and distributing free eBooks, providing a large number of classic literary works whose copyrights have expired.==
[![](https://miro.medium.com/v2/resize:fit:700/0*j-mNiA-OoZ94CTcs.jpg)](https://miro.medium.com/v2/resize:fit:700/0*j-mNiA-OoZ94CTcs.jpg)
==The example given on the GraphRAG official website is “A Christmas Carol,” a famous novel by Charles Dickens that tells the wonderful story of a miser experiencing a transformation on Christmas Eve, ultimately changing his outlook on life.==
[![](https://miro.medium.com/v2/resize:fit:700/0*cqJyQjrx-xkdpNOB.jpg)](https://miro.medium.com/v2/resize:fit:700/0*cqJyQjrx-xkdpNOB.jpg)
==You can download it by executing the following command:==
==`curl https://www.gutenberg.org/cache/epub/24022/pg24022.txt > ./ragtest/input/book.txt`==
==I checked, and the downloaded file shows as 189KB locally.==
[![](https://miro.medium.com/v2/resize:fit:700/0*z93zWy9RmyCgsbuX.jpg)](https://miro.medium.com/v2/resize:fit:700/0*z93zWy9RmyCgsbuX.jpg)
==After the download is complete, we need to initialize.==
==`python -m graphrag.index --init --root ./ragtest`==
==This step is to set up the basic environment and configuration for GraphRAG, ensuring that subsequent operations can proceed smoothly.==
[![](https://miro.medium.com/v2/resize:fit:700/0*r-AtRZ5pdkSZ3Pdm.jpg)](https://miro.medium.com/v2/resize:fit:700/0*r-AtRZ5pdkSZ3Pdm.jpg)
==Let’s take a look; the execution is quick because no actual indexing operations are performed here, just a few files and folders are created.==
[![](https://miro.medium.com/v2/resize:fit:700/0*pf6IHB4UtB0fiyMS.jpg)](https://miro.medium.com/v2/resize:fit:700/0*pf6IHB4UtB0fiyMS.jpg)
==Earlier, there was an input folder you created yourself, and now GraphRAG has created an output folder, a prompts folder, and two configuration files.==
==We first set up this== ==`.env`== ==file, which needs to be filled with some configurations. These configurations typically include API keys, model selections, and other important parameters that are crucial for the normal operation of GraphRAG.==
[![](https://miro.medium.com/v2/resize:fit:700/0*9gLivx43ayktyihz.jpg)](https://miro.medium.com/v2/resize:fit:700/0*9gLivx43ayktyihz.jpg)
==You need to fill in the OpenAI provided API key into== ==`GRAPHRAG_API_KEY`====.==
==Additionally, the== ==`settings.yaml`== ==file also needs to be modified.==
[![](https://miro.medium.com/v2/resize:fit:700/0*uKLL_txp2vgcadDK.jpg)](https://miro.medium.com/v2/resize:fit:700/0*uKLL_txp2vgcadDK.jpg)
==There is one item in particular that needs attention. The default is set to GPT-4 Turbo preview, which must be changed to GPT-4o mini, as we want to try to reduce costs. Other settings do not need to be changed.==
[![](https://miro.medium.com/v2/resize:fit:700/0*n1LFiEW5Jr2eLA84.jpg)](https://miro.medium.com/v2/resize:fit:700/0*n1LFiEW5Jr2eLA84.jpg)
==Next, let’s create an index. Return to the terminal and execute the following command.==
==`python -m graphrag.index --root ./ragtest`==
==This command establishes a graph-based knowledge base. This process took a full five minutes, so we won’t show it in detail.==
[![](https://miro.medium.com/v2/resize:fit:700/0*ZNHJD4WvOa2GYnYY.gif)](https://miro.medium.com/v2/resize:fit:700/0*ZNHJD4WvOa2GYnYY.gif)
# ==Query==
==Finally, the graph has been built. Now let’s make a query.==
==`python -m graphrag.query \ --root ./ragtest \ --method global \ "What are the top themes in this story?"`==
==The command here, Global, represents that we are asking about the entire book. The question we are asking is: What are the main themes in this story?==
[![](https://miro.medium.com/v2/resize:fit:700/0*MGuVkO4h4gNgadH4.gif)](https://miro.medium.com/v2/resize:fit:700/0*MGuVkO4h4gNgadH4.gif)
==Let’s look at the results.==
[![](https://miro.medium.com/v2/resize:fit:700/0*WLoS4INO5HYvWDu3.jpg)](https://miro.medium.com/v2/resize:fit:700/0*WLoS4INO5HYvWDu3.jpg)
==The results show several themes, each answer followed by a series of content source numbers, which is very important. It emphasizes that the large language model does not hallucinate but indeed uses the materials you provide to give answers.==
# ==Verification==
==Should we trust this result? I think that although GraphRAG provided source fragment information in the response, it is still not enough.==
==Suppose you have never read Dickens’ novel; how would you verify the answer just given?==
==You can write a prompt:==
- ==You are an experienced English literature teacher, and now you have a question about Dickens’ novel “A Christmas Carol”: “What are the top themes in this story?” Below, I provide you with a document that is a student’s answer. Please verify it paragraph by paragraph based on your understanding of this novel, checking for factual errors and areas for improvement.==
==Then, submit this prompt along with the result just provided by GraphRAG (in English) to Claude 3.5 Sonnet.==
[![](https://miro.medium.com/v2/resize:fit:700/0*sp-D9PdOB8zh26tU.jpg)](https://miro.medium.com/v2/resize:fit:700/0*sp-D9PdOB8zh26tU.jpg)
==Then, here is the quality analysis result given by Claude 3.5 Sonnet. Let’s take a look at its evaluation.==
[![](https://miro.medium.com/v2/resize:fit:700/0*Nag5fIHKtIXwYQc4.jpg)](https://miro.medium.com/v2/resize:fit:700/0*Nag5fIHKtIXwYQc4.jpg)
==Claude 3.5 Sonnet gives an overall evaluation: This is an excellent analysis. This result proves that our method of using the knowledge graph for retrieval is very effective. So far, can we completely trust this answer?==
==Of course not.==
==What I just saw was the result derived from the large language model based on its memory of the data during training, which can still generate inaccurate information. Therefore, we need to connect AI to the internet for queries to verify the accuracy of the information.==
==In this regard,== [==an effective tool is Perplexity==](https://wshuyi.medium.com/how-effective-is-perplexity-pro-and-how-can-you-get-two-months-of-free-access-0b95799881e1)==. It can perform online queries to verify the accuracy of information.==
[![](https://miro.medium.com/v2/resize:fit:700/0*eaSNtSBD4-F-RgL7.jpg)](https://miro.medium.com/v2/resize:fit:700/0*eaSNtSBD4-F-RgL7.jpg)
==First, Perplexity searches for information related to the input content and lists multiple relevant sources of information. Then, Perplexity checks the accuracy of topic identification.==
[![](https://miro.medium.com/v2/resize:fit:700/0*DsgkPZCrqOfkBft9.jpg)](https://miro.medium.com/v2/resize:fit:700/0*DsgkPZCrqOfkBft9.jpg)
==In Perplexity’s analysis, you can see that it used these phrases to evaluate: accurately captures, accurately identifies, effectively captures, accurately points out, effectively summarizes. It also noted that there were no obvious factual errors, and the choice and analysis of the topics were very appropriate.==
==Through the cross-validation of these two methods, we have more confidence in the answers provided by the large language model based on our graph-based knowledge base.==
# ==Cost==
==What is the cost of using this method?==
==I opened the OpenAI console to check, and at first, I was shocked — had today’s bill skyrocketed?==
[![](https://miro.medium.com/v2/resize:fit:700/0*ejp-vWzkoCjpO6KK.jpg)](https://miro.medium.com/v2/resize:fit:700/0*ejp-vWzkoCjpO6KK.jpg)
==Fortunately, upon closer inspection, the actual cost was only $0.28. Here are the details.==
[![](https://miro.medium.com/v2/resize:fit:626/0*mJziqv6eUnSpmTOX.jpg)](https://miro.medium.com/v2/resize:fit:626/0*mJziqv6eUnSpmTOX.jpg)
==$0.05 (almost one-fifth) was used for speech recognition, which is unrelated to our current task.==
==In other words, what is the actual cost for summarizing this book, building a graph-based knowledge base, and conducting queries? It is only $0.23.==
==Considering that using the official example costs $11, you will find that the cost improvement brought by GPT-4o mini is astonishing.==
# ==Summary==
==Through the explanation in this article, you can see that GraphRAG technology can help us answer complex questions on a broader scale more accurately, which is crucial for many application scenarios.==
==Furthermore, combined with the GPT-4o mini model, we not only improved processing efficiency and speed but also effectively reduced costs. This is good news for individual users, researchers, and businesses.==
==Give it a try yourself, and feel free to share your test results in the comments section so we can discuss together.==
==Enjoy using your AI knowledge base powered by knowledge graphs!==
==If you find this article useful, please hit the== ==`Applaud`== ==button.==
==If you think this article might be helpful to your friends, please share it with them.==
==Feel free to== [==follow my column==](https://wshuyi.medium.com/) ==to receive timely updates.==
==Welcome to== [==subscribe to my Patreon column==](https://patreon.com/wshuyi) ==to access exclusive articles for paid users. I will update at least 3 articles per month. The current price is discounted.==
==To watch video content, please subscribe to== [==my Youtube channel==](https://www.youtube.com/@wshuyi)==.==
==My Twitter:== [==@wshuyi==](https://twitter.com/wshuyi)
# ==延伸阅读==
==Enjoy using the knowledge graph-based AI knowledge base!==
==If you find this article useful, please hit the== ==`Applaud`== ==button.==
==If you think this article might be helpful to your friends, please share it with them.==
==Feel free to== [==follow my column==](https://wshuyi.medium.com/) ==to receive timely updates.==
==Welcome to== [==subscribe to my Patreon column==](https://patreon.com/wshuyi) ==to access exclusive articles for paid users. I will update at least 3 articles per month. The current price is discounted.==
==To watch video content, please subscribe to== [==my Youtube channel==](https://www.youtube.com/@wshuyi)==.==
==My Twitter:== [==@wshuyi==](https://twitter.com/wshuyi)
# ==Related Articles==
- [==How can Retrieval-Augmented Generation’s GPT models help you use AI for coding more efficiently?==](https://mp.weixin.qq.com/s/UhGr7grp6byzu6GeOfpUEg)
- [==How can artificial intelligence help you read academic papers?==](https://mp.weixin.qq.com/s/sCI19ou5Ku5yoLbz10hWCg)
- [==What topics should students in the humanities explore when using machine learning for research papers?==](https://mp.weixin.qq.com/s/nHSeKa6NXsSt7pPXcmBE7w)
- [==How can artificial intelligence help you efficiently find research paper topics?==](https://mp.weixin.qq.com/s/UmS99UDh5c4O6FaG64dNYg)
- [==How can artificial intelligence help you find academic papers?==](https://mp.weixin.qq.com/s/Zn1N6L-DzKfnbzCTWW68jA)
# ==Cubed==
==_Thank you for being a part of the community! Before you go:_==
- ==Be sure to== ==**clap**== ==and== ==**follow**== ==the writer ️👏====**️️**==
- ==Follow us:== [==**X**==](https://twitter.com/inPlainEngHQ) ==|== [==**LinkedIn**==](https://www.linkedin.com/company/inplainenglish/) ==|== [==**YouTube**==](https://www.youtube.com/channel/UCtipWUghju290NWcn8jhyAw) ==|== [==**Discord**==](https://discord.gg/in-plain-english-709094664682340443) ==|== [==**Newsletter**==](https://newsletter.plainenglish.io/)
- ==Visit our platforms:== [==**CoFeed**==](https://cofeed.app/) ==|== [==**Differ**==](https://differ.blog/) ==|== [==**In Plain English**==](https://plainenglish.io/) ==|== [==**Venture**==](https://venturemagazine.net/) ==|== [==**Cubed**==](https://cubed.run/)