---
DocFlag:
  - Reference
Updated: 2023-05-13T13:58
tags:
  - AI->-Programming
Created: 2023-05-12T08:19
---
  
Image via Midjourney
A faster dataframe library than pandas is here and its name is polars!
Polars is a library written in Rust and uses Arrow as its foundation. This library is faster than pandas especially when it comes to working with large datasets.
Although Polar is written in Rust, you don’t need to know Rust to use it, but there’s a Python package that you can use to get started with it. In fact, if you already know pandas, learning polars should be easy.
But, first, let’s see why you should choose polars over other options.
_Don’t feel like reading? Watch my YouTube video instead!_
## Why use Polars?
Here are some reasons why you should choose polars.
- It uses all available cores on your computer.
- It optimizes queries to reduce unneeded work/memory allocations.
- It handles datasets larger than your available RAM.
- It has a strict schema (data types should be known before running the query).
But just don’t take my word for it. Let’s see some numbers.
Here’s a test performance shown in the Polars documentation. According to the image below, polars is way faster than other options.
Source: [Polars doc](https://pola-rs.github.io/polars-book/user-guide/introduction.html)
How can polars outperform pandas?
Unlike pandas, polars is lazy and semi-lazy. In lazy Polars, we can do query optimization on an entire query in order to improve performance and memory pressure. That said, you could do all your work eagerly with polars as you’d do with pandas.
Now let’s learn how to use polars!
## First Things First: Install The Library
To install Polars, we have to run the command below.
```Plain
# pip
pip install polars
# conda
conda install polars
```
Note that we need to have Python 3.7 or above.
## Read a dataset with Polars
Just like with pandas, we can read CSV files with polars. Let’s import polars and read a CSV file ([click here to download this CSV file](https://drive.google.com/drive/folders/1nTHDKzvR0WHsw1C76GbAcPERR0xRsFIR?usp=sharing))
```Plain
import polars as pl
df = pl.read_csv("StudentsPerformance.csv")
```
Here’s how the dataframe looks.
Did you notice anything strange about this dataframe?
The data type is specified in the column names and there is no index! If you’re a pandas user, you must be used to seeing indexes in a dataframe, but polars doesn’t have index.
Here’s [why](https://pola-rs.github.io/polars-book/user-guide/coming_from_pandas.html) (according to its docs):

> Polars aims to have predictable results and readable queries, as such we think an index does not help us reach that objective. We believe the semantics of a query should not change by the state of an index or a reset_index call.
What does that mean for pandas users?
Well, we won’t have to use the`.loc` or `iloc` methods anymore or get the`SettingWithCopyWarning` in `Polars`.
But our `df` dataframe is still similar to pandas. We can get the column attribute, just like we’d do with Pandas.
```Plain
>>> df.columns
['id',
 'gender',
 'race/ethnicity',
 'parental level of education',
 'lunch',
 'test preparation course',
 'math score',
 'reading score',
 'writing score']
```
Let’s explore what else we can do with polars and how it differs from pandas.
## How to select columns with Polars
Say we want to select the “gender” column from our dataframe. Here’s how we’d do it with polars.
```Plain
# Select 1 column
df.select(pl.col('gender'))
```
We can also select 2 columns by adding the `[]`.
```Plain
# Select 2+ columns
df.select(pl.col(['gender', 'math score']))
```
Or all the columns!
```Plain
# Select all columns
df.select(pl.col('*'))
```
## How to create columns with Polars
Let’s sum the ‘math score’ and ‘reading score’ columns and put the result in a new column named “sum.”
To create a column with Polars we have to use `.with_columns` . Here’s how to use it and how it differs from pandas.
```Plain
# polars: create "sum" column
df.with_columns(
    (pl.col('math score') + pl.col('reading score')).alias("sum")
)
# pandas: df['sum'] = df['math score'] + df['reading score']
```
As you can see, we also need to use `.alias` to name the column.
Now let’s create an “average” column. We’ll calculate the average of ‘math score,’ ‘reading score,’ and ‘writing score.’
```Plain
# polars: create "average" column
df.with_columns(
    pl.col(['math score', 'reading score', 'writing score']).mean().alias('average')
)
# pandas: df['average'] = df[['math score', 'reading score', 'writing score']].mean(axis=1)
```
## How to filter data with Polars
Say we want to filter only the female gender. We can filter data with polars using `.filter`.
```Plain
# polars: simple filtering
df.filter(pl.col('gender')=='female')
# pandas: df[df['gender'] == 'female']
```
We can also filter based on multiple conditions. Let’s filter only “female” from “group B.”
```Plain
# Multiple filtering
df.filter(
    (pl.col('gender')=='female') &
    (pl.col('race/ethnicity')=='group B')
)
# pandas: df[(df['gender'] == 'female') & (df['race/ethnicity'] == 'group B')]
```
## How to group by with Polars
Grouping with polars is very similar to pandas. We have to use `.groupby` and then indicate the aggregate function.
Let’s group by “race/ethnicity” and count the elements in each group.
```Plain
# Group by
df.groupby("race/ethnicity").count()
```
Just like pandas, isn’t it?
## Joining dataframes with Polars
To join dataframes with polars, we use `.join`. The syntax of this function is similar to the `.merge` function we have on pandas.
Before joining dataframes, [download the second CSV](https://drive.google.com/drive/folders/1nTHDKzvR0WHsw1C76GbAcPERR0xRsFIR?usp=sharing) named “LanguageScore.csv” and read it as `df2`.
```Plain
df2 = pl.read_csv("LanguageScore.csv")
```
Now, let’s join `df` and `df2`. They have a common column named `id` .
```Plain
# Join dataframes
df.join(df2, on='id')
```
Now we have the “language score” column in our `df` dataframe.
You can also add the `how` parameter to indicate the type of join you want.
```Plain
# Inner, left and outer join
df.join(df2, on='id', how='inner')
df.join(df2, on='id', how='left')
df.join(df2, on='id', how='outer')
```
## Concatenate dataframes with Polars
To concatenate dataframes with polars we use `.concat`, but, unlike pandas, to indicate whether we want a horizontal or vertical concatenation we simply have to add the `how` parameter and type either “horizontal” or “vertical.”
Note that vertical concatenation makes a dataframe longer, while horizontal concatenation makes a dataframe wider.
Let’s add the “language score” column from `df2` to `df` . To do so, we have to concatenate both dataframes horizontally. Here’s how.
```Plain
# Concatenate dataframes
pl.concat([df, df2], how="horizontal")
```
But here’s the catch, the dataframes to concatenate can’t have a single column in common.
Both our dataframes have the column “id”, so we have to drop one of them before concatenating them.
```Plain
# drop column "id" in df2
df2 = df2.drop("id")
# Concatenate dataframes
pl.concat([df, df2], how="horizontal")
```
Note that, unlike our previous inner join, now we get null values inside the “language score” column. This happens because `df` has more rows than `df2` resulting in null values in the concatenation.
Congratulations! You just learned how to use the polars library. For more, check the [official documentation](https://pola-rs.github.io/polars-book/user-guide/introduction.html).
If you enjoy reading stories like these and want to support me as a writer, consider signing up to become a Medium member. It’s $5 a month, giving you unlimited access to thousands of Python guides and Data science articles. If you sign up using [my link](https://frank-andrade.medium.com/membership), I’ll earn a small commission with no extra cost to you.