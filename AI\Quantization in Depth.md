---
Updated: 2024-05-08T11:06
tags:
  - AI->-Theory
Created: 2024-05-08T11:06
---
[![](https://wordpress.deeplearning.ai/wp-content/uploads/2024/05/V3_Banner-HF2-2-1.png)](https://wordpress.deeplearning.ai/wp-content/uploads/2024/05/V3_Banner-HF2-2-1.png)
[![](https://www.deeplearning.ai/_next/image/?url=https%3A%2F%2Fwordpress.deeplearning.ai%2Fwp-content%2Fuploads%2F2024%2F04%2FBG_DeepLearning_Hugging_Face_QF_C2_Banner_2070x1080.png&w=3840&q=90)](https://www.deeplearning.ai/_next/image/?url=https%3A%2F%2Fwordpress.deeplearning.ai%2Fwp-content%2Fuploads%2F2024%2F04%2FBG_DeepLearning_Hugging_Face_QF_C2_Banner_2070x1080.png&w=3840&q=90)
### ==Short Course==
==In Collaboration With==
[![](https://wordpress.deeplearning.ai/wp-content/uploads/2024/02/logo_DeepLearning_Hugging_Face_AI_Banner_1000x1000.png)](https://wordpress.deeplearning.ai/wp-content/uploads/2024/02/logo_DeepLearning_Hugging_Face_AI_Banner_1000x1000.png)
- ==Try out different variants of Linear Quantization, including symmetric vs. asymmetric mode, and different granularities like per tensor, per channel, and per group quantization.==
- ==Build a general-purpose quantizer in Pytorch that can quantize the dense layers of any open source model for up to 4x compression on dense layers.==
- ==Implement weights packing to pack four 2-bit weights into a single 8-bit integer.==
## ==What you’ll learn in this course==
==In== ==**Quantization in Depth**== ==you will build model quantization methods to shrink model weights to ¼ their original size, and apply methods to maintain the compressed model’s performance. Your ability to quantize your models can make them more accessible, and also faster at inference time.==
==Implement and customize linear quantization from scratch so that you can study the tradeoff between space and performance, and then build a general-purpose quantizer in PyTorch that can quantize any open source model. You’ll implement  techniques to compress model weights from 32 bits to 8 bits and even 2 bits.==
==Join this course to:==
- ==Build and customize linear quantization functions, choosing between two “modes”: asymmetric and symmetric; and three granularities: per-tensor, per-channel, and per-group quantization.==
- ==Measure the quantization error of each of these options as you balance the performance and space tradeoffs for each option.==
- ==Build your own quantizer in PyTorch, to quantize any open source model’s dense layers from 32 bits to 8 bits.==
- ==Go beyond 8 bits, and pack four 2-bit weights into one 8-bit integer.==
==**Quantization in Depth**== ==lets you build and customize your own linear quantizer from scratch, going beyond standard open source libraries such as PyTorch and Quanto, which are covered in the short course== [==**Quantization Fundamentals**==](https://www.deeplearning.ai/short-courses/quantization-fundamentals-with-hugging-face/)==, also by Hugging Face.==
==This course gives you the foundation to study more advanced quantization methods, some of which are recommended at the end of the course.==
## ==Who should join?==
==Building on the concepts introduced in== [==**Quantization Fundamentals with Hugging Face**==](https://www.deeplearning.ai/short-courses/quantization-fundamentals-with-hugging-face/)==, this course will help deepen your understanding of linear quantization methods. If you’re looking to go further into quantization, this course is the perfect next step.==
## ==Instructors==
### ==Marc Sun==
==Instructor==
==Machine Learning Engineer at Hugging Face==
### ==Younes Belkada==
==Instructor==
==Machine Learning Engineer at Hugging Face==
## ==Course access is free for a limited time during the DeepLearning.AI learning platform beta!==
## ==Want to learn more about Generative AI?==
==Keep learning with updates on curated AI news, courses, and events, as well as Andrew’s thoughts from DeepLearning.AI!==