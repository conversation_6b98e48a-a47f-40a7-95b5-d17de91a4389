---
DocFlag:
  - Reference
  - Tested
Updated: 2024-05-23T15:54
tags:
  - AI->-Competition
  - AI->-Model
  - AI->-Theory
URL: https://medium.com/data-science-in-your-pocket/deepfm-for-recommendation-systems-explained-with-codes-c200063990f7
Created: 2024-05-23T15:30
---
[![](https://miro.medium.com/v2/da:true/resize:fit:1200/0*-gMS-Xfoq38qGz0h)](https://miro.medium.com/v2/da:true/resize:fit:1200/0*-gMS-Xfoq38qGz0h)
## ==Understanding the maths behind DeepFM==
[![](https://miro.medium.com/v2/resize:fill:88:88/1*vyvhK_h4zA05mg_Y-n2qBA.jpeg)](https://miro.medium.com/v2/resize:fill:88:88/1*vyvhK_h4zA05mg_Y-n2qBA.jpeg)
[![](https://miro.medium.com/v2/resize:fill:48:48/1*dHhREpDd71nTWkahuiiMJg.jpeg)](https://miro.medium.com/v2/resize:fill:48:48/1*dHhREpDd71nTWkahuiiMJg.jpeg)
[==Mehul Gupta==](https://medium.com/@mehulgupta_7991?source=post_page-----c200063990f7--------------------------------)
==Published in==
[==Data Science in your pocket==](https://medium.com/data-science-in-your-pocket?source=post_page-----c200063990f7--------------------------------)
==4 min read==
==Sep 10, 2023==
[![](https://miro.medium.com/v2/resize:fit:875/0*-gMS-Xfoq38qGz0h)](https://miro.medium.com/v2/resize:fit:875/0*-gMS-Xfoq38qGz0h)
==Photo by== [==Eric Nopanen==](https://unsplash.com/@rexcuando?utm_source=medium&utm_medium=referral) ==on== [==Unsplash==](https://unsplash.com/?utm_source=medium&utm_medium=referral)
==Continuing my Recommendation System blog series, this time I will be covering the maths behind DeepFM (Deep Factorization Machine) and the codes to implement the same in Python. Do check out the URLs for the previous parts on the basics of Recommendation Systems, Matrix Factorization algorithms, NCF, and Factorization Machines below.==
[https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fmehulgupta2016154-resume-builder-streamlit-app-ajmqjx.streamlit.app%2FMy_blogs%3Fembed%3Dtrue&display_name=Streamlit&url=https%3A%2F%2Fmehulgupta2016154-resume-builder-streamlit-app-ajmqjx.streamlit.app%2FMy_blogs&image=https%3A%2F%2Fstorage.googleapis.com%2Fs4a-prod-share-preview%2Fdefault%2Fst_app_screenshot_image%2Fe5536a7f-91c2-4b74-a3b8-f3e8555d42fc%2FMy_blogs.png&key=a19fcc184b9711e1b4764040d3dc5c07&type=text%2Fhtml&schema=streamlit](https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fmehulgupta2016154-resume-builder-streamlit-app-ajmqjx.streamlit.app%2FMy_blogs%3Fembed%3Dtrue&display_name=Streamlit&url=https%3A%2F%2Fmehulgupta2016154-resume-builder-streamlit-app-ajmqjx.streamlit.app%2FMy_blogs&image=https%3A%2F%2Fstorage.googleapis.com%2Fs4a-prod-share-preview%2Fdefault%2Fst_app_screenshot_image%2Fe5536a7f-91c2-4b74-a3b8-f3e8555d42fc%2FMy_blogs.png&key=a19fcc184b9711e1b4764040d3dc5c07&type=text%2Fhtml&schema=streamlit)
==DeepFM can be considered as an advanced version of the Factorization Machines==
[https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fwww.youtube.com%2Fembed%2Fw_YSl0Gd5CA%3Ffeature%3Doembed&display_name=YouTube&url=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3Dw_YSl0Gd5CA&image=https%3A%2F%2Fi.ytimg.com%2Fvi%2Fw_YSl0Gd5CA%2Fhqdefault.jpg&key=a19fcc184b9711e1b4764040d3dc5c07&type=text%2Fhtml&schema=youtube](https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fwww.youtube.com%2Fembed%2Fw_YSl0Gd5CA%3Ffeature%3Doembed&display_name=YouTube&url=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3Dw_YSl0Gd5CA&image=https%3A%2F%2Fi.ytimg.com%2Fvi%2Fw_YSl0Gd5CA%2Fhqdefault.jpg&key=a19fcc184b9711e1b4764040d3dc5c07&type=text%2Fhtml&schema=youtube)
==My debut book “LangChain in your Pocket” is out now==
[==LangChain in your Pocket: Beginner's Guide to Building Generative AI Applications using LLMs==](https://www.amazon.in/dp/B0CTHQHT25?source=post_page-----c200063990f7--------------------------------)==  
  
==[==--------------------------------------------------------------------------------------------==](https://www.amazon.in/dp/B0CTHQHT25?source=post_page-----c200063990f7--------------------------------)
### [==LangChain in your Pocket: Beginner's Guide to Building Generative AI Applications using LLMs eBook : Gupta, Mehul…==](https://www.amazon.in/dp/B0CTHQHT25?source=post_page-----c200063990f7--------------------------------)
[==www.amazon.in==](https://www.amazon.in/dp/B0CTHQHT25?source=post_page-----c200063990f7--------------------------------)
# ==What are Factorization Machines?==
==FM can be considered as an extension for Linear Regression where apart from capturing linear relationships, higher-order relationships are also captured. Also,== ==**it not only uses User-Item interactions for training but also user & item features**====, which is a big advantage compared to NCF or Matrix Factorization, which uses just User-Item interactions.==
==Learn more about the Factorization Machine below==
[https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fwww.youtube.com%2Fembed%2FrtKtZpXQSZg%3Ffeature%3Doembed&display_name=YouTube&url=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DrtKtZpXQSZg&image=https%3A%2F%2Fi.ytimg.com%2Fvi%2FrtKtZpXQSZg%2Fhqdefault.jpg&key=a19fcc184b9711e1b4764040d3dc5c07&type=text%2Fhtml&schema=youtube](https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Fwww.youtube.com%2Fembed%2FrtKtZpXQSZg%3Ffeature%3Doembed&display_name=YouTube&url=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DrtKtZpXQSZg&image=https%3A%2F%2Fi.ytimg.com%2Fvi%2FrtKtZpXQSZg%2Fhqdefault.jpg&key=a19fcc184b9711e1b4764040d3dc5c07&type=text%2Fhtml&schema=youtube)
==So, as NCF was an extension for Matrix Factorization for capturing non-linear relationships better(I have already covered both these topics), very similar to, DeepFM, as the name suggests, is a deep learning-based extension for Factorization Machines capturing non-linear relationships==
# ==The architecture==
[![](https://miro.medium.com/v2/resize:fit:865/1*_d388oZVBckTzJHKr93L-A.png)](https://miro.medium.com/v2/resize:fit:865/1*_d388oZVBckTzJHKr93L-A.png)
## ==Input and Output==
==As conveyed DeepFM is an extension of Factorization Machines, even though the input is quite similar i.e. One-Hot Encoded features. The output is 0–1 giving the probability of whether the user will click or not as sigmoid activation is used in the last layer. However, this can be changed to ReLU or Linear activation for ratings/ranking purposes (explicit feedback).==
## ==Embedding layer (grey cells in the diagram)==
==This layer is trying to learn embeddings similar to the Latent vectors in the Factorization Machine. So, for each OHE feature, we will learn embeddings of the same size in this layer==
==From here on, these embeddings will be fed to two different branches:==
## ==Factorization Machine (left side of diagram)==
==This is a replica of the Factorization Machine where, using the latent vector embeddings we generated in the previous step, will use the below equation==
==_y = w₀ + ∑(wᵢ * xᵢ) + ∑ᵢ(∑ⱼ(<vᵢ . vⱼ> * xᵢ * xⱼ))_==
==Where==
==_y = Label_==
==_w₀ = Bias_==
==_wᵢ = weights_==
==_xᵢ = Features from One-Hot encoded feature-set_==
==_<vᵢ . vⱼ> = Dot product between latent vectors._==
==**Note:**== ==_Touch down on_== [==_this blog_==](https://medium.com/data-science-in-your-pocket/recommendation-systems-using-factorization-machines-with-examples-and-codes-48a8dcb9d5ea) ==_to understand the equation_==
==The other branch where the embeddings are fed is==
## ==DNN (right side of the diagram)==
==This is a general Neural Network (similar to NCF’s MLP) where all these latent vector embeddings are fed to a few hidden layers and forwarded to a final output layer.==
## ==Final Output==
==The outputs from the Factorization Machine and DNN segments are combined together (concatenated) and then the sigmoid function is applied for the final output. So, a DeepFM can be summarized as==
==Embedding Layer → Sigmoid(FM + DNN)==
## ==That’s it !!==
==Next, we will try to implement DeepFM over a dummy dataset using the LibRecommender library.==
==Note: This a great library if you wish to implement any SOTA recommendation systems==
==We will be implementing DeepFM over a sample of the MovieLens dataset that can be downloaded from== [==here==](https://github.com/massquantity/LibRecommender/blob/master/examples/sample_data/sample_movielens_merged.csv)==. Below is a snapshot==
[![](https://miro.medium.com/v2/resize:fit:875/1*0_LYeGoNC3qA_xHe0x2hUA.png)](https://miro.medium.com/v2/resize:fit:875/1*0_LYeGoNC3qA_xHe0x2hUA.png)
==As observed, the data has user & item features alongside user-item interaction information i.e. rating==
==Once done downloading, follow the below steps==
1. ==Import the required libraries and load the dataset==
==import numpy as np====  
  
====import pandas as pd====  
  
====from libreco.data import split_by_ratio_chrono, DatasetFeat====  
  
====from libreco.algorithms import DeepFM==
==data = pd.read_csv("data.csv", sep=",", header=0)====  
  
====data.fillna(value={'age':0,'genre1':'','genre2':'','genre3':'','occupation':'','sex':''},inplace=True)==
==train_data, test_data = split_by_ratio_chrono(data, test_size=0.2)==
==2. Before building our final training dataset compatible with LibRecommender, we need to mention==
==Sparse columns: Categorical columns==
==Dense columns: Numerical columns==
==User & Item columns==
==sparse_col = ["sex", "occupation", "genre1", "genre2", "genre3"]====  
  
====dense_col = ["age"]====  
  
====user_col = ["sex", "age", "occupation"]====  
  
====item_col = ["genre1", "genre2", "genre3"]==
==train_data, data_info = DatasetFeat.build_trainset(train_data, user_col, item_col, sparse_col, dense_col)====  
  
====test_data = DatasetFeat.build_testset(test_data)==
==Time to define our DeepFM model object and call model.fit()==
==model = DeepFM(====  
  
====task="ranking",====  
  
====data_info=data_info,====  
  
====embed_size=16,====  
  
====n_epochs=3,====  
  
====lr=1e-4,====  
  
====batch_size=512,====  
  
====use_bn=True,====  
  
====hidden_units=(128, 64, 32),====  
  
====)====  
  
====model.fit(====  
  
====train_data,====  
  
====neg_sampling=True,====  
  
====verbose=2,====  
  
====shuffle=True,====  
  
====eval_data=test_data,====  
  
====metrics=["loss"],====  
  
====)==
==A few points to note about the above code snippet==
- ==The task has two options rating or ranking==
- ==Hidden units are for the DNN part of DeepFM==
==The training may look something like this==
[![](https://miro.medium.com/v2/resize:fit:771/1*pJ25cFlFHqbhD0G7zf95oA.png)](https://miro.medium.com/v2/resize:fit:771/1*pJ25cFlFHqbhD0G7zf95oA.png)
==How to use the trained model?==
==# predict preference of user 2211 to item 110====  
  
====model.predict(user=2211, item=110)====  
  
====# recommend 7 items for user 2211====  
  
====model.recommend_user(user=2211, n_rec=7)==
==# cold-start prediction====  
  
====model.predict(user="ccc", item="not item", cold_start="average")====  
  
====# cold-start recommendation====  
  
====model.recommend_user(user="are we good?", n_rec=7, cold_start="popular")==
## ==T====o use LibRecommender, always maintain the sequence and name of 1st 4 columns as a user, item, label, and time followed by other features (if any)==
==We are done for the day. See you soon with some new Recommendation system blog !!==