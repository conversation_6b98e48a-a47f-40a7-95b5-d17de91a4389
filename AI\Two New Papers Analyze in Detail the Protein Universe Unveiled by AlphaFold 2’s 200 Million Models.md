---
Updated: 2023-09-25T22:23
tags:
  - AI->-Theory
Created: 2023-09-25T22:23
---
[![](https://miro.medium.com/v2/resize:fit:1200/1*_12F4l0Ate-wN114MyDmXg.png)](https://miro.medium.com/v2/resize:fit:1200/1*_12F4l0Ate-wN114MyDmXg.png)
---
# Two New Papers Analyze in Detail the Protein Universe Unveiled by AlphaFold 2’s 200 Million Models
## And they had to create new tools to handle such large volume of protein structural models
![[1_D6QHa8RBbslpMmy3jOGtw.jpeg]]
![[Notion/AI/Two New Papers Analyze in Detail the Protein Universe Unveiled by AlphaFold 2’s 200 Million Models/attachments/1CJe3891yB1A1mzMdqemkdg.jpeg|1CJe3891yB1A1mzMdqemkdg.jpeg]]
[LucianoSphere](https://lucianosphere.medium.com/?source=post_page-----bf5bd55e754a--------------------------------)
·
[Follow](https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fsubscribe%2Fuser%2Fd28939b5ab78&operation=register&redirect=https%3A%2F%2Ftowardsdatascience.com%2Ftwo-new-papers-analyze-in-detail-the-protein-universe-unveiled-by-alphafold-2s-200-million-models-bf5bd55e754a&user=LucianoSphere&userId=d28939b5ab78&source=post_page-d28939b5ab78----bf5bd55e754a---------------------post_header-----------)
Published in
[Towards Data Science](https://towardsdatascience.com/?source=post_page-----bf5bd55e754a--------------------------------)
·
7 min read
·
3 days ago
One of the resources presented in the discussed articles, [https://uniprot3d.org/](https://uniprot3d.org/), depicts proteins spread as in a modern view of the universe where brighter clusters contain more members. On zooming the user can inspect related proteins, until clicking on a specific node displays information (here only a structural model shown) about a protein family in the context of Uniprot. Picture composed by the author while browsing the website.
![[1_12F4l0Ate-wN114MyDmXg.png]]
**The recent release of over 200 million predicted protein structures by DeepMind’s AlphaFold 2, in collaboration with the European Institute of Bioinformatics, has ushered in a new era of protein research. Here I’m presenting a summary of findings from two groundbreaking papers published in** _**Nature**_ **this week, which delve into the depths of this protein universe. These papers employ innovative clustering algorithms, structural comparisons, and other adaptations of existing tools to work on large data volumes, to shed light on the structural diversity, evolutionary relationships, and functional potential of proteins at an unprecedented scale.**
Proteins are the workhorses of biology, governing a myriad of cellular processes, from energy generation to cell division. While the sequencing of proteins has burgeoned over the years, thanks to advances in genomics, the determination of their 3D structures has lagged behind due to the dearth of scalable experimental methods. However, with the advent of AlphaFold 2, a revolutionary AI system developed by DeepMind, the landscape of protein structure prediction has been transformed. The AlphaFold Protein Structure Database (AFDB) now houses an astounding 200 million predicted protein structures, marking a milestone in computational biology.
## [AlphaFold-based databases and fully-fledged, easy-to-use, online AlphaFold interfaces poised to…](https://towardsdatascience.com/alphafold-based-databases-and-fully-fledged-easy-to-use-alphafold-interfaces-poised-to-baf865c6d75e?source=post_page-----bf5bd55e754a--------------------------------)
### [Not only computational but also experimental biology. Thoughts on the future of data science niches in biology.](https://towardsdatascience.com/alphafold-based-databases-and-fully-fledged-easy-to-use-alphafold-interfaces-poised-to-baf865c6d75e?source=post_page-----bf5bd55e754a--------------------------------)
[towardsdatascience.com](https://towardsdatascience.com/alphafold-based-databases-and-fully-fledged-easy-to-use-alphafold-interfaces-poised-to-baf865c6d75e?source=post_page-----bf5bd55e754a--------------------------------)
Now, just this week actually, two groups of authors write in _Nature_ to report how to utilize AlphaFold 2’s protein models to unlock new insights into the protein universe. These studies leverage innovative versions of existing tools adapted to the huge volume of data in the AFDB; for example, modern versions of clustering algorithms and methods for structural comparisons. With these adapted tools the works explore the vast expanse of protein structures, their evolutionary origins, and their functional implications.