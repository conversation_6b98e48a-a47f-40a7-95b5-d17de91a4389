---
Updated: 2024-08-18T13:14
tags:
  - AI->-<PERSON><PERSON><PERSON><PERSON>
  - <PERSON>->-Programming
  - AI->-RAG
Created: 2024-08-18T13:14
---
[![](https://opengraph.githubassets.com/aaec5ff05b088e9d25aa4730daa5cdc7e8ac592907f022458c85b8ebc704cc19/NirDiamant/Controllable-RAG-Agent)](https://opengraph.githubassets.com/aaec5ff05b088e9d25aa4730daa5cdc7e8ac592907f022458c85b8ebc704cc19/NirDiamant/Controllable-RAG-Agent)
# ==Sophisticated Controllable Agent for Complex RAG Tasks 🧠📚==
[![](https://camo.githubusercontent.com/db9dfde8049c5d66ba62fde707d2cfb30e26f9f26ff274c3442c0aec1ec410a4/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4c6963656e73652d417061636865253230322e302d626c75652e737667)](https://camo.githubusercontent.com/db9dfde8049c5d66ba62fde707d2cfb30e26f9f26ff274c3442c0aec1ec410a4/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4c6963656e73652d417061636865253230322e302d626c75652e737667)
[![](https://camo.githubusercontent.com/678a3407e10f770d3aabd82db1fa7f373bf19425839540f85325e25072bc1e4a/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f707974686f6e2d332e382b2d626c75652e737667)](https://camo.githubusercontent.com/678a3407e10f770d3aabd82db1fa7f373bf19425839540f85325e25072bc1e4a/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f707974686f6e2d332e382b2d626c75652e737667)
[![](https://camo.githubusercontent.com/a5ceaa9e114c16d2c7cfd7ef62032b26b6eb47b61b1263ae4ebc5497fedd45b2/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f5052732d77656c636f6d652d627269676874677265656e2e7376673f7374796c653d666c61742d737175617265)](https://camo.githubusercontent.com/a5ceaa9e114c16d2c7cfd7ef62032b26b6eb47b61b1263ae4ebc5497fedd45b2/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f5052732d77656c636f6d652d627269676874677265656e2e7376673f7374796c653d666c61742d737175617265)
[![](https://camo.githubusercontent.com/9a75389a71b494d7598cd82432e6ed01c03ffa5cf03f6665db0454bb0488eafa/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4c696e6b6564496e2d436f6e6e6563742d626c7565)](https://camo.githubusercontent.com/9a75389a71b494d7598cd82432e6ed01c03ffa5cf03f6665db0454bb0488eafa/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4c696e6b6564496e2d436f6e6e6563742d626c7565)
[![](https://camo.githubusercontent.com/6acabdcb3c14145bdb51d9d44728f902a6acd364d5b7724f097dd9ad27f945f3/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f666f6c6c6f772f4e69724469616d616e7441493f6c6162656c3d466f6c6c6f77253230404e69724469616d616e744149267374796c653d736f6369616c)](https://camo.githubusercontent.com/6acabdcb3c14145bdb51d9d44728f902a6acd364d5b7724f097dd9ad27f945f3/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f666f6c6c6f772f4e69724469616d616e7441493f6c6162656c3d466f6c6c6f77253230404e69724469616d616e744149267374796c653d736f6369616c)
==An advanced Retrieval-Augmented Generation (RAG) solution designed to tackle complex questions that simple semantic similarity-based retrieval cannot solve. This project showcases a sophisticated deterministic graph acting as the "brain" of a highly controllable autonomous agent capable of answering non-trivial questions from your own data.==
==📚 Explore my== [==comprehensive guide on RAG techniques==](https://github.com/NirDiamant/RAG_Techniques) ==to complement this advanced agent implementation.==
==🔗 For discussions on GenAI, RAG, or custom agents, feel free to== [==connect on LinkedIn==](https://www.linkedin.com/in/nir-diamant-759323134/)==.==
[![](https://github.com/NirDiamant/Controllable-RAG-Agent/raw/main/graphs/demo.gif)](https://github.com/NirDiamant/Controllable-RAG-Agent/raw/main/graphs/demo.gif)
## ==🌟 Key Features==
- ==**Sophisticated Deterministic Graph**====: Acts as the "brain" of the agent, enabling complex reasoning.==
- ==**Controllable Autonomous Agent**====: Capable of answering non-trivial questions from custom datasets.==
- ==**Hallucination Prevention**====: Ensures answers are solely based on provided data, avoiding AI hallucinations.==
- ==**Multi-step Reasoning**====: Breaks down complex queries into manageable sub-tasks.==
- ==**Adaptive Planning**====: Continuously updates its plan based on new information.==
- ==**Performance Evaluation**====: Utilizes== ==`Ragas`== ==metrics for comprehensive quality assessment.==
## ==🧠 How It Works==
[![](https://github.com/NirDiamant/Controllable-RAG-Agent/raw/main/graphs/final_graph_schema.jpeg)](https://github.com/NirDiamant/Controllable-RAG-Agent/raw/main/graphs/final_graph_schema.jpeg)
1. ==**PDF Loading and Processing**====: Load PDF documents and split them into chapters.==
2. ==**Text Preprocessing**====: Clean and preprocess the text for better summarization and encoding.==
3. ==**Summarization**====: Generate extensive summaries of each chapter using large language models.==
4. ==**Book Quotes Database Creation**====: Create a database for specific questions that will need access to quotes from the book.==
5. ==**Vector Store Encoding**====: Encode the book content and chapter summaries into vector stores for efficient retrieval.==
6. ==**Question Processing**====:==
    - ==Anonymize the question by replacing named entities with variables.==
    - ==Generate a high-level plan to answer the anonymized question.==
    - ==De-anonymize the plan and break it down into retrievable or answerable tasks.==
7. ==**Task Execution**====:==
    - ==For each task, decide whether to retrieve information or answer based on context.==
    - ==If retrieving, fetch relevant information from vector stores and distill it.==
    - ==If answering, generate a response using chain-of-thought reasoning.==
8. ==**Verification and Re-planning**====:==
    - ==Verify that generated content is grounded in the original context.==
    - ==Re-plan remaining steps based on new information.==
9. ==**Final Answer Generation**====: Produce the final answer using accumulated context and chain-of-thought reasoning.==
## ==📊 Evaluation==
==The solution is evaluated using== ==`Ragas`== ==metrics:==
- ==Answer Correctness==
- ==Faithfulness==
- ==Answer Relevancy==
- ==Context Recall==
- ==Answer Similarity==
## ==🔍 Use Case: Harry Potter Book Analysis==
==The algorithm was tested using the first Harry Potter book, allowing for monitoring of the model's reliance on retrieved information versus pre-trained knowledge. This choice enables us to verify whether the model is using its pre-trained knowledge or strictly relying on the retrieved information from vector stores.==
### ==Example Question==
==**Q: How did the protagonist defeat the villain's assistant?**==
==To solve this question, the following steps are necessary:==
1. ==Identify the protagonist of the plot.==
2. ==Identify the villain.==
3. ==Identify the villain's assistant.==
4. ==Search for confrontations or interactions between the protagonist and the villain.==
5. ==Deduce the reason that led the protagonist to defeat the assistant.==
==The agent's ability to break down and solve such complex queries demonstrates its sophisticated reasoning capabilities.==
## ==🚀 Getting Started==
### ==Prerequisites==
- ==Python 3.8+==
- ==API key for your chosen LLM provider==
### ==Installation==
1. ==Clone the repository:==
    
    ==git clone https://github.com/NirDiamant/Controllable-RAG-Agent.git  
    cd Controllable-RAG-Agent  
    ==
    
2. ==Install required packages:==
    
    ==pip install -r requirements.txt==
    
3. ==Set up environment variables: Create a== ==`.env`== ==file in the root directory with your API key:==
    
    ```plain
    LLM_API_KEY=your_llm_api_key
    ```
    
### ==Usage==
1. ==Explore the step-by-step tutorial:== ==`sophisticated_rag_agent_harry_potter.ipynb`==
2. ==Run real-time agent visualization:==
    
    ==streamlit run simulate_agent.py==
    
## ==🛠️ Technologies Used==
- ==LangChain==
- ==FAISS Vector Store==
- ==Streamlit (for visualization)==
- ==Ragas (for evaluation)==
- ==Flexible integration with various LLMs (e.g., OpenAI GPT models, Groq, or others of your choice)==
## ==💡 Heuristics and Techniques==
1. ==Encoding both book content in chunks, chapter summaries generated by LLM, and quotes from the book.==
2. ==Anonymizing the question to create a general plan without biases or pre-trained knowledge of any LLM involved.==
3. ==Breaking down each task from the plan to be executed by custom functions with full control.==
4. ==Distilling retrieved content for better and accurate LLM generations, minimizing hallucinations.==
5. ==Answering a question based on context using a Chain of Thought, which includes both positive and negative examples, to arrive at a well-reasoned answer rather than just a straightforward response.==
6. ==Content verification and hallucination-free verification as suggested in "Self-RAG: Learning to Retrieve, Generate, and Critique through Self-Reflection" -== [==https://arxiv.org/abs/2310.11511==](https://arxiv.org/abs/2310.11511)==.==
7. ==Utilizing an ongoing updated plan made by an LLM to solve complicated questions. Some ideas are derived from "Plan-and-Solve Prompting" -== [==https://arxiv.org/abs/2305.04091==](https://arxiv.org/abs/2305.04091) ==and the "babyagi" project -== [==https://github.com/yoheinakajima/babyagi==](https://github.com/yoheinakajima/babyagi)==.==
8. ==Evaluating the model's performance using== ==`Ragas`== ==metrics like answer correctness, faithfulness, relevancy, recall, and similarity to ensure high-quality answers.==
## ==🤝 Contributing==
==Contributions are welcome! Please feel free to submit a pull request or open an issue for any suggestions or improvements.==
## ==📚 Learn More==
- [==Lecture Video==](https://www.youtube.com/watch?v=b4v7tjxQkvg&ab_channel=Machine%26DeepLearningIsrael)
- [==Medium Article==](https://medium.com/@nirdiamant21/controllable-agent-for-complex-rag-tasks-bf8cb652fbb3)
## ==🙏 Acknowledgements==
==Special thanks to Elad Levi for the valuable advice and ideas.==
## ==📄 License==
==This project is licensed under the Apache-2.0 License - see the== [==LICENSE==](https://github.com/NirDiamant/Controllable-RAG-Agent/blob/main/LICENSE) ==file for details.==
==⭐️ If you find this repository helpful, please consider giving it a star!==
==Keywords: RAG, Retrieval-Augmented Generation, Agent, Langgraph, NLP, AI, Machine Learning, Information Retrieval, Natural Language Processing, LLM, Embeddings, Semantic Search==