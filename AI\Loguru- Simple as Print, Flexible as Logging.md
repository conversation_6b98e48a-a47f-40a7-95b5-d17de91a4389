---
Updated: 2023-10-13T21:27
tags:
  - AI->-Programming
Created: 2023-10-13T21:27
---
[![](https://miro.medium.com/v2/resize:fit:1200/1*gVmXk1EUa8gaGjy3ghHzJg.png)](https://miro.medium.com/v2/resize:fit:1200/1*gVmXk1EUa8gaGjy3ghHzJg.png)
## ==Loguru: Simple as Print, Flexible as Logging====  
  
====Loguru：简单如打印，灵活如伐木==
## ==The simple logging solution for your data science project====  
  
====适用于数据科学项目的简单日志记录解决方案==
==[==
[![](https://miro.medium.com/v2/resize:fill:44:44/2*tiQVZEZxHMPcnVmEmN7UtA.jpeg)](https://miro.medium.com/v2/resize:fill:44:44/2*tiQVZEZxHMPcnVmEmN7UtA.jpeg)
==](https://towardsdatascience.com/@khuyentran1476?source=post_page-----c964467f64aa--------------------------------)[==
[![](https://miro.medium.com/v2/resize:fill:24:24/1*CJe3891yB1A1mzMdqemkdg.jpeg)](https://miro.medium.com/v2/resize:fill:24:24/1*CJe3891yB1A1mzMdqemkdg.jpeg)
==](https://medium.com/towards-data-science?source=post_page-----c964467f64aa--------------------------------)==
[![](https://miro.medium.com/v2/resize:fit:700/1*gVmXk1EUa8gaGjy3ghHzJg.png)](https://miro.medium.com/v2/resize:fit:700/1*gVmXk1EUa8gaGjy3ghHzJg.png)
==Image by Author 图片来源：作者==
==_Originally published at_== [==_https://mathdatasimplified.com_==](https://mathdatasimplified.com/2023/07/17/simplify-your-python-logging-with-loguru/) ==_on July 17, 2023._====  
  
====最初发表于 2023 年 7 月 17 日的 https://mathdatasimplified.com。==
## ==Why Use Logging in a Data Science Project?====  
  
====为什么要在数据科学项目中使用日志记录？==
==Data scientists often use the print function to debug their code. However, as the number of print statements increases, it becomes difficult to identify where the output is from due to the lack of line numbers or function names.====  
  
====数据科学家经常使用打印函数来调试他们的代码。但是，随着 print 语句数量的增加，由于缺少行号或函数名称，很难确定输出的来源。==
==def encode_data(data: list):====  
  
====print("Encode data")====  
  
====data_map = {'a': 1, 'b': 2, 'c': 3}====  
  
====print(f"Data map: {data_map}")====  
  
====return [data_map[num] for num in data]==
==def add_one(data: list):====  
  
====print("Add one")====  
  
====return [num + 1 for num in data]==
==def process_data(data: list):====  
  
====print("Process data")====  
  
====data = encode_data(data)====  
  
====print(f"Encoded data: {data}")====  
  
====data = add_one(data)====  
  
====print(f"Added one: {data}")==
==process_data(['a', 'a', 'c'])==
==Output: 输出：==
==Process data====  
  
====Encode data====  
  
====Data map: {'a': 1, 'b': 2, 'c': 3}====  
  
====Encoded data: [1, 1, 3]====  
  
====Add one====  
  
====Added one: [2, 2, 4]==
==When putting the code into production, manually going through and removing all the debugging lines can be a tedious and error-prone task.====  
  
====将代码投入生产时，手动检查和删除所有调试行可能是一项繁琐且容易出错的任务。==
==def encode_data(data: list):====  
  
====print("Encode data")====  
  
====data_map = {'a': 1, 'b': 2, 'c': 3}====  
  
====return [data_map[num] for num in data]==
==def add_one(data: list):====  
  
====print("Add one")====  
  
====return [num + 1 for num in data]==
==def process_data(data: list):====  
  
====print("Process data")====  
  
====data = encode_data(data)====  
  
====data = add_one(data)==
==process_data(['a', 'a', 'c'])==
==Logging provides the perfect solution for this problem by allowing data scientists to specify different levels (debug, info, warning, error) for their output.====  
  
====日志记录允许数据科学家为其输出指定不同的级别（调试、信息、警告、错误），从而为此问题提供了完美的解决方案。==
==def encode_data(data: list):====  
  
====logger.info("Encode data")====  
  
====data_map = {'a': 1, 'b': 2, 'c': 3}====  
  
====logger.debug(f"Data map: {data_map}")====  
  
====return [data_map[num] for num in data]==
==def add_one(data: list):====  
  
====logger.info("Add one")====  
  
====return [num + 1 for num in data]==
==def process_data(data: list):====  
  
====logger.info("Process data")====  
  
====data = encode_data(data)====  
  
====logger.debug(f"Encoded data: {data}")====  
  
====data = add_one(data)====  
  
====logger.debug(f"Added one: {data}")==
==process_data(['a', 'a', 'c'])==
==Output: 输出：==
==2023-07-24 09:45:15 | INFO | logging_example:process_data:22 - Process data====  
  
====2023-07-24 09:45:15 | INFO | logging_example:encode_data:12 - Encode data====  
  
====2023-07-24 09:45:15 | DEBUG | logging_example:encode_data:14 - Data map: {'a': 1, 'b': 2, 'c': 3}====  
  
====2023-07-24 09:45:15 | DEBUG | logging_example:process_data:24 - Encoded data: [1, 1, 3]====  
  
====2023-07-24 09:45:15 | INFO | logging_example:add_one:18 - Add one====  
  
====2023-07-24 09:45:15 | DEBUG | logging_example:process_data:26 - Added one: [2, 2, 4]==
==By setting appropriate log levels, data scientists can selectively enable or disable certain types of output based on their needs. In production environments, they can set the log level to “INFO” or higher to exclude debugging logs, keeping logs concise and relevant.====  
  
====通过设置适当的日志级别，数据科学家可以根据自己的需要有选择地启用或禁用某些类型的输出。在生产环境中，他们可以将日志级别设置为“INFO”或更高，以排除调试日志，从而使日志保持简洁且相关。==
==22023-07-24 09:40:05 | INFO | logging_example:process_data:22 - Process data====  
  
====2023-07-24 09:40:05 | INFO | logging_example:encode_data:12 - Encode data====  
  
====2023-07-24 09:40:05 | INFO | logging_example:add_one:18 - Add one==
==Since the logs contain additional information, such as timestamps, function names, and line numbers, data scientists can quickly pinpoint the origin of log messages.====  
  
====由于日志包含其他信息，例如时间戳、函数名称和行号，因此数据科学家可以快速查明日志消息的来源。==
==Data scientists can also direct log output to a file to review logs from previous runs.====  
  
====数据科学家还可以将日志输出定向到文件，以查看以前运行的日志。==
==# example.log==
==2023-07-16 09:50:24 | INFO | logging_example:main:17 - This is an info message====  
  
====2023-07-16 09:50:24 | WARNING | logging_example:main:18 - This is a warning message====  
  
====2023-07-16 09:50:24 | ERROR | logging_example:main:19 - This is an error message====  
  
====2023-07-16 09:55:37 | INFO | logging_example:main:17 - This is an info message====  
  
====2023-07-16 09:55:37 | WARNING | logging_example:main:18 - This is a warning message====  
  
====2023-07-16 09:55:37 | ERROR | logging_example:main:19 - This is an error message==
## ==Why Are Many Data Scientists Not Using Logging?====  
  
====为什么许多数据科学家不使用日志记录？==
==Many data scientists still prefer using print statements over logging because print is simpler and doesn’t require as much setup. For small scripts and one-off tasks, the overhead of setting up a logging framework seems unnecessary.====  
  
====许多数据科学家仍然更喜欢使用 print 语句而不是日志记录，因为打印更简单，不需要太多设置。对于小型脚本和一次性任务，设置日志记录框架的开销似乎没有必要。==
==import logging==
==logging.basicConfig(====  
  
====level=logging.DEBUG,====  
  
====format="%(asctime)s | %(levelname)s | %(module)s:%(funcName)s:%(lineno)d - %(message)s"====  
  
====datefmt="%Y-%m-%d %H:%M:%S",====  
  
====)==
==logger = logging.getLogger(__name__)==
==def main():====  
  
====logger.debug("This is a debug message")====  
  
====logger.info("This is an info message")====  
  
====logger.warning("This is a warning message")====  
  
====logger.error("This is an error message")==
==if __name__ == "__main__":====  
  
====main()==
==Wouldn’t it be nice if there was a library that allows you to leverage the power of logging while making the experience as simple as print?====  
  
====如果有一个库可以让您利用日志记录的力量，同时使体验像打印一样简单，那不是很好吗？==
==That is when Loguru, an open-source Python library, comes in handy. This article will show some Loguru features that make it a great alternative to the standard logging library.====  
  
====这时，开源Python库Loguru就派上用场了。本文将展示一些 Loguru 功能，这些功能使其成为标准日志记录库的绝佳替代品。==
==Feel free to play and fork the source code of this article here:====  
  
====随意在这里播放和分叉本文的源代码：==
## ==Elegant Out-of-the-Box Functionality====  
  
====优雅的开箱即用功能==
==By default, logging gives boring and not very useful logs:====  
  
====默认情况下，日志记录会提供无聊且不太有用的日志：==
==import logging==
==logger = logging.getLogger(__name__)==
==def main():====  
  
====logger.debug("This is a debug message")====  
  
====logger.info("This is an info message")====  
  
====logger.warning("This is a warning message")====  
  
====logger.error("This is an error message")==
==if __name__ == "__main__":====  
  
====main()==
==Output: 输出：==
==WARNING:root:This is a warning message====  
  
====ERROR:root:This is an error message==
==In contrast, Loguru generates informative and vibrant logs by default.====  
  
====相比之下，默认情况下，Loguru 会生成信息丰富且充满活力的日志。==
==from loguru import logger==
==def main():====  
  
====logger.debug("This is a debug message")====  
  
====logger.info("This is an info message")====  
  
====logger.warning("This is a warning message")====  
  
====logger.error("This is an error message")==
==if __name__ == "__main__":====  
  
====main()==
## ==Format Logs 格式化日志==
==Formatting logs allows you to add useful information to logs such as timestamps, log levels, module names, function names, and line numbers.====  
  
====格式化日志允许您向日志添加有用的信息，例如时间戳、日志级别、模块名称、函数名称和行号。==
==The traditional logging approach uses the % formatting, which is not intuitive to use :====  
  
====传统的日志记录方法使用 % 格式，使用起来并不直观：==
==iimport logging==
==logging.basicConfig(====  
  
====level=logging.INFO,====  
  
====format="%(asctime)s | %(levelname)s | %(module)s:%(funcName)s:%(lineno)d - %(message)s",====  
  
====datefmt="%Y-%m-%d %H:%M:%S",====  
  
====)==
==logger = logging.getLogger(__name__)==
==def main():====  
  
====logger.debug("This is a debug message")====  
  
====logger.info("This is an info message")====  
  
====logger.warning("This is a warning message")====  
  
====logger.error("This is an error message")==
==Output: 输出：==
==2023-07-16 14:48:17 | INFO | logging_example:main:13 - This is an info message====  
  
====2023-07-16 14:48:17 | WARNING | logging_example:main:14 - This is a warning message====  
  
====2023-07-16 14:48:17 | ERROR | logging_example:main:15 - This is an error message==
==In contrast, Loguru uses the== ==`{}`== ==formatting, which is much more readable and easy to use:====  
  
====相比之下，Loguru 使用的== ==`{}`== ==格式更具可读性和易用性：==
==from loguru import logger==
==logger.add(====  
  
====sys.stdout,====  
  
====level="INFO",====  
  
====format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {module}:{function}:{line} - {message}",====  
  
====)==
## ==Save Logs to Files 将日志保存到文件==
==Saving logs to files and printing them to the terminal using the traditional logging module requires two extra classes== ==`FileHandler`== ==and== ==`StreamHandler`====.====  
  
====使用传统的日志记录模块将日志保存到文件并将它们打印到终端需要两个额外的类== ==`FileHandler`== ==和== ==`StreamHandler`== ==.==
==import logging==
==logging.basicConfig(====  
  
====level=logging.DEBUG,====  
  
====format="%(asctime)s | %(levelname)s | %(module)s:%(funcName)s:%(lineno)d - %(message)s",====  
  
====datefmt="%Y-%m-%d %H:%M:%S",====  
  
====handlers=[====  
  
====logging.FileHandler(filename="info.log", level=logging.INFO),====  
  
====logging.StreamHandler(level=logging.DEBUG),====  
  
====],====  
  
====)==
==logger = logging.getLogger(__name__)==
==def main():====  
  
====logging.debug("This is a debug message")====  
  
====logging.info("This is an info message")====  
  
====logging.warning("This is a warning message")====  
  
====logging.error("This is an error message")==
==if __name__ == "__main__":====  
  
====main()==
==However, with Loguru, you can attain the same functionality with just the== ==`add`== ==method.====  
  
====但是，使用Loguru，您只需使用== ==`add`== ==该方法即可获得相同的功能。==
==from loguru import logger==
==logger.add(====  
  
===='info.log',====  
  
====format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {module}:{function}:{line} - {message}",====  
  
====level="INFO",====  
  
====)==
==def main():====  
  
====logger.debug("This is a debug message")====  
  
====logger.info("This is an info message")====  
  
====logger.warning("This is a warning message")====  
  
====logger.error("This is an error message")==
==if __name__ == "__main__":====  
  
====main()==
## ==Rotate Logs 轮换日志==
==Rotating logs prevents the size of log files from getting too large by periodically creating new log files and archiving or removing older ones.====  
  
====轮换日志通过定期创建新的日志文件并存档或删除旧日志文件来防止日志文件的大小变得太大。==
==In the logging library, rotating logs requires an additional class called== ==`TimedRotatingFileHandler`====. The following code switches to a new log file every week (== ==`when="WO", interval=1`====) and retains up to 4 weeks' worth of log files (== ==`backupCount=4`====).====  
  
====在日志记录库中，轮换日志需要一个名为== ==`TimedRotatingFileHandler`== ==的附加类。以下代码每周切换到一个新的日志文件 （== ==`when="WO", interval=1`== ==），并保留最多 4 周的日志文件 （== ==`backupCount=4`== ==）。==
==import logging====  
  
====from logging.handlers import TimedRotatingFileHandler==
==logger = logging.getLogger(__name__)====  
  
====logger.setLevel(logging.DEBUG)==
==formatter = logging.Formatter(====  
  
===="%(asctime)s | %(levelname)-8s | %(module)s:%(funcName)s:%(lineno)d - %(message)s",====  
  
====datefmt="%Y-%m-%d %H:%M:%S",====  
  
====)==
==file_handler = TimedRotatingFileHandler(====  
  
====filename="debug2.log", when="WO", interval=1, backupCount=4====  
  
====)====  
  
====file_handler.setLevel(logging.INFO)====  
  
====file_handler.setFormatter(formatter)====  
  
====logger.addHandler(file_handler)==
==def main():====  
  
====logger.debug("This is a debug message")====  
  
====logger.info("This is an info message")====  
  
====logger.warning("This is a warning message")====  
  
====logger.error("This is an error message")==
==if __name__ == "__main__":====  
  
====main()==
==In Loguru, you can replicate this behavior by adding the== ==`rotation`== ==and== ==`retention`== ==arguments to the== ==`add`== ==method. The syntax for specifying these arguments is readable and easy to use.====  
  
====在 Loguru 中，可以通过向== ==`add`== ==方法添加== ==`rotation`== ==and== ==`retention`== ==参数来复制此行为。用于指定这些参数的语法可读且易于使用。==
==from loguru import logger==
==logger.add("debug.log", level="INFO", rotation="1 week", retention="4 weeks")==
==def main():====  
  
====logger.debug("This is a debug message")====  
  
====logger.info("This is an info message")====  
  
====logger.warning("This is a warning message")====  
  
====logger.error("This is an error message")==
==if __name__==
==== "__main__":====  
  
====main()==
## ==Filter 滤波器==
==Filtering in logging allows you to selectively control which log records should be output based on specific criteria.====  
  
====日志记录中的筛选允许您根据特定条件有选择地控制应输出哪些日志记录。==
==In the logging library, filtering logs requires creating a custom logging filter class.====  
  
====在日志记录库中，筛选日志需要创建自定义日志记录筛选器类。==
==import logging==
==logging.basicConfig(====  
  
====filename="hello.log",====  
  
====format="%(asctime)s | %(levelname)-8s | %(module)s:%(funcName)s:%(lineno)d - %(message)s",====  
  
====level=logging.INFO,====  
  
====)==
==class CustomFilter(logging.Filter):====  
  
====def filter(self, record):====  
  
====return "Hello" in record.msg==
==custom_filter = CustomFilter()==
==logger = logging.getLogger()====  
  
====logger.addFilter(custom_filter)==
==def main():====  
  
====logger.info("Hello World")====  
  
====logger.info("Bye World")==
==if __name__ == "__main__":====  
  
====main()==
==In Loguru, you can simply use a lambda function to filter logs.====  
  
====在 Loguru 中，您可以简单地使用 lambda 函数来过滤日志。==
==from loguru import logger==
==logger.add("hello.log", filter=lambda x: "Hello" in x["message"], level="INFO")==
==def main():====  
  
====logger.info("Hello World")====  
  
====logger.info("Bye World")==
==if __name__ == "__main__":====  
  
====main()==
## ==Catch Exceptions 捕获异常==
==Conventional logs for exceptions can be ambiguous and challenging to debug:====  
  
====异常的传统日志可能不明确且难以调试：==
==import logging==
==logging.basicConfig(====  
  
====level=logging.DEBUG,====  
  
====format="%(asctime)s | %(levelname)s | %(module)s:%(funcName)s:%(lineno)d - %(message)s",====  
  
====datefmt="%Y-%m-%d %H:%M:%S",====  
  
====)==
==def division(a, b):====  
  
====return a / b==
==def nested(c):====  
  
====try:====  
  
====division(1, c)====  
  
====except ZeroDivisionError:====  
  
====logging.exception("ZeroDivisionError")==
==if __name__ == "__main__":====  
  
====nested(0)==
==Traceback (most recent call last):====  
  
====File "/Users/<USER>/Data-science/productive_tools/logging_tools/catch_exceptions/logging_example.py", line 16, in nested====  
  
====division(1, c)====  
  
====File "/Users/<USER>/Data-science/productive_tools/logging_tools/catch_exceptions/logging_example.py", line 11, in division====  
  
====return a / b====  
  
====ZeroDivisionError: division by zero==
==The exceptions displayed above are not very helpful as they don’t provide information about the values of== ==`c`== ==that triggered the exceptions.====  
  
====上面显示的异常不是很有帮助，因为它们不提供有关触发异常== ==`c`== ==的值的信息。==
==Loguru enhances error identification by displaying the entire stack trace, including the values of variables:====  
  
====Loguru 通过显示整个堆栈跟踪（包括变量的值）来增强错误识别：==
==from loguru import logger==
==def division(a, b):====  
  
====return a / b==
==def nested(c):====  
  
====try:====  
  
====division(1, c)====  
  
====except ZeroDivisionError:====  
  
====logger.exception("ZeroDivisionError")==
==if __name__ == "__main__":====  
  
====nested(0)==
==Loguru’s== ==`catch`== ==decorator allows you to catch any errors within a function. This decorator also identifies the thread on which the error occurs.====  
  
====Loguru 的== ==`catch`== ==装饰器允许您捕获函数中的任何错误。此修饰器还标识发生错误的线程。==
==from loguru import logger==
==def division(a, b):====  
  
====return a / b==
==@logger.catch====  
  
====def nested(c):====  
  
====division(1, c)==
==if __name__ == "__main__":====  
  
====nested(0)==
## ==But I don’t want to add more dependencies to my Python project====  
  
====但我不想向我的 Python 项目添加更多依赖项==
==Although incorporating Loguru into your project requires installing an additional library, it is remarkably lightweight and occupies minimal disk space.====  
  
====虽然将 Loguru 合并到您的项目中需要安装额外的库，但它非常轻巧，占用的磁盘空间最小。==
==Moreover, it helps in reducing boilerplate code, significantly reducing the friction associated with using logging and making your project easier to maintain in the long run.====  
  
====此外，它有助于减少样板代码，显着减少与使用日志记录相关的摩擦，并使项目更易于长期维护。==