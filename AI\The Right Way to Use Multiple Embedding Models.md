---
Updated: 2024-07-14T15:32
tags:
  - AI->-Theory
Created: 2024-07-14T15:32
---
[![](https://substackcdn.com/image/fetch/w_1200,h_600,c_fill,f_jpg,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb4afba9c-dc5f-485f-acb5-67848876c98f_3000x2224.png)](https://substackcdn.com/image/fetch/w_1200,h_600,c_fill,f_jpg,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb4afba9c-dc5f-485f-acb5-67848876c98f_3000x2224.png)
[==_**Partner with us**_==](https://scorecard.dailydoseofds.com/sponsorship-assessment) ==_**|**_== [==_**Deep Dives**_==](https://www.dailydoseofds.com/membership/)==  
  
====_**与我们合作 | 深度探索**_==
==Imagine you have two different models (or sub-networks) in your whole ML pipeline. Both generate a representation/embedding of the input in the same dimensions (say, 200).====  
  
====假设您在整个 ML 管道中有两个不同的模型(或子网络)。它们都生成了相同维度(例如 200)的输入表示/嵌入。==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Face496b0-36ba-438d-a118-4463604f9d06_2343x1008.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Face496b0-36ba-438d-a118-4463604f9d06_2343x1008.png)
==Two networks generate embeddings of the same dimensions====  
  
====两个网络生成相同维度的嵌入==
==_These could also be pre-trained models used to generate embeddings — BERT, XLNet, etc., or even through any embedding network for that matter._====  
  
====_这些也可能是用于生成嵌入的预训练模型 — BERT、XLNet 等，或者任何嵌入网络。_==
==Here, many folks get tempted to make them interact. They would:====  
  
====这里,很多人会被诱惑去让它们互动。他们会:==
- ==compare these representations====  
      
    ====比较这些表示==
- ==compute their Euclidean distance====  
      
    ====计算它们的欧几里得距离==
- ==compute their cosine similarity, and more.====  
      
    ====计算它们的余弦相似度，以及更多。==
==The rationale is that== ==**as the representations have the same dimensions, they can seamlessly interact.**====  
  
====理由是,由于这些表示具有相同的维度,它们可以无缝地进行交互。==
==**However, that is NOT true, and you should NEVER do that.**====  
  
====**然而,那不是真的,你绝对不应该那么做。**==
==Why? 为什么?==
==This is because even though these embeddings have the same length (or dimensions), they are not in the same space, i.e.,== ==**they are out of space.**====  
  
====这是因为即使这些嵌入具有相同的长度(或维度),但它们不在同一个空间中,即它们是不在空间之内的。==
==Out of space means that their axes are not aligned.====  
  
====空间不足意味着它们的轴线未对齐。==
==To simplify, imagine both embeddings were in a 3D space.====  
  
====简单来说,假设两个嵌入都在 3D 空间中。==
==Now, assume that their z-axes are aligned, but the x and y axes of the first is at an angle to the x and y axes of the second:====  
  
====现在,假设它们的 z 轴是对齐的,但第一个的 x 和 y 轴与第二个的 x 和 y 轴成角度:==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F1fa6ed52-ba08-40f8-9776-04b57c791dbb_2405x760.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F1fa6ed52-ba08-40f8-9776-04b57c791dbb_2405x760.png)
==Now, of course, both embeddings have the same dimensions — 3.====  
  
====现在,当然,这两个嵌入都有相同的维度 — 3。==
==But can you compare them?====  
  
====但是你能比较它们吗？==
==No, right? 不是,对吧?==
==Similarly, comparing the embeddings from the two networks above would inherently assume that all axes are perfectly aligned.====  
  
====同样地,比较上述两个网络的嵌入会默认所有轴完美对齐。==
==But this is highly unlikely because there are infinitely many ways axes may orient relative to each other.====  
  
====但这种可能性极小,因为轴可以相对于彼此以无数种方式定向。==
==Thus, the representations can NEVER be compared,== ==**unless generated by the same model.**====  
  
====因此,除非由同一个模型生成,否则表征是无法比较的。==
==I vividly remember making this mistake once, and it caused serious trouble in my ML pipeline.====  
  
====我生动地记得我曾经犯过这个错误,这在我的机器学习管道中造成了严重的麻烦。==
==And I think if you are not aware of this, then it is something that can easily go unnoticed.====  
  
====如果你没有意识到这一点,这很容易被忽视。==
==Instead, I have always found that concatenation is a much better way to leverage multiple embeddings.====  
  
====相反,我一直发现连接是一种更好的利用多个嵌入的方法。==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fcb8005a7-8f4e-483e-995e-ab9f47c8cb84_2820x1020.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fcb8005a7-8f4e-483e-995e-ab9f47c8cb84_2820x1020.png)
==The good thing is that concatenation works even if they have unequal dimensions.====  
  
====好的是,即使它们的维度不相等,拼接也能正常工作。==
==👉 Over to you: How do you typically handle embeddings from multiple models?====  
  
====👉 你说: 你通常如何处理来自多个模型的嵌入?==
==Thanks for reading Daily Dose of Data Science! Subscribe for free to learn something new and insightful about Python and Data Science every day. Also, get a Free Data Science PDF (550+ pages) with 320+ tips.==
==Every week, I publish no-fluff deep dives on topics that truly matter to your skills for ML/DS roles.====  
  
====每周,我都会发布无废话的深度分析,涉及对于机器学习/数据科学角色技能非常重要的话题。==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fa463bde0-2a7a-4758-8557-b6122ab4a4d9_2065x826.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fa463bde0-2a7a-4758-8557-b6122ab4a4d9_2065x826.png)
[==I want to read super-detailed articles==](https://www.dailydoseofds.com/membership)==  
  
==[==我想阅读超详细的文章==](https://www.dailydoseofds.com/membership)
==For instance: 例如:==
- [==A Crash Course on Causality – Part 1==](https://www.dailydoseofds.com/a-crash-course-on-causality-part-1/)==  
      
    ==[==因果关系概论 – 第一部分==](https://www.dailydoseofds.com/a-crash-course-on-causality-part-1/)
- [==A Crash Course on Causality – Part 2==](https://www.dailydoseofds.com/a-crash-course-on-causality-part-2/)==  
      
    ==[==因果论速成课程 – 第二部分==](https://www.dailydoseofds.com/a-crash-course-on-causality-part-2/)
- [==A Beginner-friendly Introduction to Kolmogorov Arnold Networks (KANs)==](https://www.dailydoseofds.com/a-beginner-friendly-introduction-to-kolmogorov-arnold-networks-kan/)==  
      
    ==[==适合初学者的科尔莫戈洛夫-阿诺德网络(KAN)简介==](https://www.dailydoseofds.com/a-beginner-friendly-introduction-to-kolmogorov-arnold-networks-kan/)
- [==Implementing KANs From Scratch Using PyTorch==](https://www.dailydoseofds.com/implementing-kans-from-scratch-using-pytorch/)==  
      
    ==[==利用 PyTorch 从头实现 KANs==](https://www.dailydoseofds.com/implementing-kans-from-scratch-using-pytorch/)
- [==A Practical Guide to Scaling ML Model Training==](https://www.dailydoseofds.com/how-to-scale-model-training/)==  
      
    ==[==机器学习模型训练的实用指南==](https://www.dailydoseofds.com/how-to-scale-model-training/)
- [==5 Must-Know Ways to Test ML Models in Production (Implementation Included)==](https://www.dailydoseofds.com/5-must-know-ways-to-test-ml-models-in-production-implementation-included/)==  
      
    ==[==5 个必须了解的在生产环境中测试机器学习模型的方法(含实施)==](https://www.dailydoseofds.com/5-must-know-ways-to-test-ml-models-in-production-implementation-included/)
- [==A Beginner-Friendly Guide to Multi-GPU Model Training==](https://www.dailydoseofds.com/a-beginner-friendly-guide-to-multi-gpu-model-training/)==  
      
    ==[==面向初学者的多 GPU 模型训练指南==](https://www.dailydoseofds.com/a-beginner-friendly-guide-to-multi-gpu-model-training/)
- [==Understanding LoRA-derived Techniques for Optimal LLM Fine-tuning==](https://www.dailydoseofds.com/understanding-lora-derived-techniques-for-optimal-llm-fine-tuning/)==  
      
    ==[==理解 LoRA 衍生的技术以实现最佳LLM微调==](https://www.dailydoseofds.com/understanding-lora-derived-techniques-for-optimal-llm-fine-tuning/)
- [==8 Fatal (Yet Non-obvious) Pitfalls and Cautionary Measures in Data Science==](https://www.dailydoseofds.com/8-fatal-yet-non-obvious-pitfalls-and-cautionary-measures-in-data-science/)==  
      
    ==[==数据科学中 8 个致命的(但不太明显)陷阱及防范措施==](https://www.dailydoseofds.com/8-fatal-yet-non-obvious-pitfalls-and-cautionary-measures-in-data-science/)
- [==Implementing Parallelized CUDA Programs From Scratch Using CUDA Programming==](https://www.dailydoseofds.com/implementing-massively-parallelized-cuda-programs-from-scratch-using-cuda-programming/)==  
      
    ==[==从头开始使用 CUDA 编程实现并行化 CUDA 程序==](https://www.dailydoseofds.com/implementing-massively-parallelized-cuda-programs-from-scratch-using-cuda-programming/)
- [==You Are Probably Building Inconsistent Classification Models Without Even Realizing==](https://www.dailydoseofds.com/you-are-probably-building-inconsistent-classification-models-without-even-realizing/)==  
      
    ==[==你可能在不知不觉中构建了不一致的分类模型==](https://www.dailydoseofds.com/you-are-probably-building-inconsistent-classification-models-without-even-realizing/)
- ==And many many more. 以及更多。==
==Join below to unlock all full articles:====  
  
====点击下方链接解锁所有完整文章:==
[==I want to read super-detailed articles==](https://www.dailydoseofds.com/membership)==  
  
==[==我想阅读超详细的文章==](https://www.dailydoseofds.com/membership)
==Get your product in front of 82,000 data scientists and other tech professionals.====  
  
====让您的产品展现在 82,000 位数据科学家和其他科技专业人士面前。==
==Our newsletter puts your products and services directly in front of an audience that matters — thousands of leaders, senior data scientists, machine learning engineers, data analysts, etc., who have influence over significant tech decisions and big purchases.====  
  
====我们的通讯将您的产品和服务直接展示给一个重要的受众群体——数以千计的领导者、资深数据科学家、机器学习工程师和数据分析师等,他们能够影响重要的技术决策和大型采购。==
==To ensure your product reaches this influential audience, reserve your space== [==**here**==](https://forms.gle/F32h8etrPVh3pvrTA) ==or reply to this email to ensure your product reaches this influential audience.====  
  
====为确保您的产品能够触及这一有影响力的受众群体,请在此预定您的展示空间,或回复此电子邮件以确保您的产品能够触及这一有影响力的受众群体。==