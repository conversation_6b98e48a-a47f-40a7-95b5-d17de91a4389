---
Updated: 2023-03-02T22:41
tags:
  - AI->-Programming
  - AI->-Theory
Created: 2023-03-01T19:48
---
# Colossal-AI
Colossal-AI: Making large AI models cheaper, faster and more accessible
![[colossal-ai_logo_vertical.png]]
### [Paper](https://arxiv.org/abs/2110.14883) | [Documentation](https://www.colossalai.org/) | [Examples](https://github.com/hpcaitech/ColossalAI/tree/main/examples) | [Forum](https://github.com/hpcaitech/ColossalAI/discussions) | [Blog](https://medium.com/@hpcaitech)
![[badge.svg]]
![[68747470733a2f2f72656164746865646f63732e6f72672f70726f6a656374732f636f6c6f7373616c61692f62616467652f3f76657273696f6e3d6c6174657374]]
![[68747470733a2f2f7777772e636f6465666163746f722e696f2f7265706f7369746f72792f6769746875622f6870636169746563682f636f6c6f7373616c61692f6261646765]]
![[68747470733a2f2f696d672e736869656c64732e696f2f62616467652f25463025394625413425393748756767696e67466163652d4a6f696e2d79656c6c6f77]]
![[68747470733a2f2f696d672e736869656c64732e696f2f62616467652f536c61636b2d6a6f696e2d626c756576696f6c65743f6c6f676f3d736c61636b26616d70]]
![[68747470733a2f2f696d672e736869656c64732e696f2f62616467652f2545352542452541452545342542462541312d2545352538412541302545352538352541352d677265656e3f6c6f676f3d77656368617426616d70]]
| [English](https://github.com/hpcaitech/ColossalAI/blob/main/README.md) | [中文](https://github.com/hpcaitech/ColossalAI/blob/main/README-zh-Hans.md) |
## Latest News
- [2023/02] [Open source solution replicates ChatGPT training process! Ready to go with only 1.6GB GPU memory](https://www.hpc-ai.tech/blog/colossal-ai-chatgpt)
- [2023/01] [Hardware Savings Up to 46 Times for AIGC and Automatic Parallelism](https://www.hpc-ai.tech/blog/colossal-ai-0-2-0)
- [2022/11] [Diffusion Pretraining and Hardware Fine-Tuning Can Be Almost 7X Cheaper](https://www.hpc-ai.tech/blog/diffusion-pretraining-and-hardware-fine-tuning-can-be-almost-7x-cheaper)
- [2022/10] [Use a Laptop to Analyze 90% of Proteins, With a Single-GPU Inference Sequence Exceeding 10,000](https://www.hpc-ai.tech/blog/use-a-laptop-to-analyze-90-of-proteins-with-a-single-gpu-inference-sequence-exceeding)
- [2022/09] [HPC-AI Tech Completes $6 Million Seed and Angel Round Fundraising](https://www.hpc-ai.tech/blog/hpc-ai-tech-completes-6-million-seed-and-angel-round-fundraising-led-by-bluerun-ventures-in-the)
## Table of Contents
- [Why Colossal-AI](https://github.com/hpcaitech/ColossalAI#Why-Colossal-AI)
- [Features](https://github.com/hpcaitech/ColossalAI#Features)
- [Parallel Training Demo](https://github.com/hpcaitech/ColossalAI#Parallel-Training-Demo)
    - [GPT-3](https://github.com/hpcaitech/ColossalAI#GPT-3)
    - [GPT-2](https://github.com/hpcaitech/ColossalAI#GPT-2)
    - [BERT](https://github.com/hpcaitech/ColossalAI#BERT)
    - [PaLM](https://github.com/hpcaitech/ColossalAI#PaLM)
    - [OPT](https://github.com/hpcaitech/ColossalAI#OPT)
    - [ViT](https://github.com/hpcaitech/ColossalAI#ViT)
    - [Recommendation System Models](https://github.com/hpcaitech/ColossalAI#Recommendation-System-Models)
- [Single GPU Training Demo](https://github.com/hpcaitech/ColossalAI#Single-GPU-Training-Demo)
    - [GPT-2](https://github.com/hpcaitech/ColossalAI#GPT-2-Single)
    - [PaLM](https://github.com/hpcaitech/ColossalAI#PaLM-Single)
- [Inference (Energon-AI) Demo](https://github.com/hpcaitech/ColossalAI#Inference-Energon-AI-Demo)
    - [GPT-3](https://github.com/hpcaitech/ColossalAI#GPT-3-Inference)
    - [OPT-175B Online Serving for Text Generation](https://github.com/hpcaitech/ColossalAI#OPT-Serving)
    - [176B BLOOM](https://github.com/hpcaitech/ColossalAI#BLOOM-Inference)
- [Colossal-AI for Real World Applications](https://github.com/hpcaitech/ColossalAI#Colossal-AI-in-the-Real-World)
    - [ChatGPT: Low-cost ChatGPT Equivalent Implementation Process](https://github.com/hpcaitech/ColossalAI#ChatGPT)
    - [AIGC: Acceleration of Stable Diffusion](https://github.com/hpcaitech/ColossalAI#AIGC)
    - [Biomedicine: Acceleration of AlphaFold Protein Structure](https://github.com/hpcaitech/ColossalAI#Biomedicine)
- [Installation](https://github.com/hpcaitech/ColossalAI#Installation)
    - [PyPI](https://github.com/hpcaitech/ColossalAI#PyPI)
    - [Install From Source](https://github.com/hpcaitech/ColossalAI#Install-From-Source)
- [Use Docker](https://github.com/hpcaitech/ColossalAI#Use-Docker)
- [Community](https://github.com/hpcaitech/ColossalAI#Community)
- [Contributing](https://github.com/hpcaitech/ColossalAI#contributing)
- [Cite Us](https://github.com/hpcaitech/ColossalAI#Cite-Us)
## Why Colossal-AI
Prof. James Demmel (UC Berkeley): Colossal-AI makes training AI models efficient, easy, and scalable.
![[JamesDemmel_Colossal-AI.png]]
([back to top](https://github.com/hpcaitech/ColossalAI#top))
## Features
Colossal-AI provides a collection of parallel components for you. We aim to support you to write your distributed deep learning models just like how you write your model on your laptop. We provide user-friendly tools to kickstart distributed training and inference in a few lines.
- Parallelism strategies
    
    - Data Parallelism
    - Pipeline Parallelism
    - 1D, [2D](https://arxiv.org/abs/2104.05343), [2.5D](https://arxiv.org/abs/2105.14500), [3D](https://arxiv.org/abs/2105.14450) Tensor Parallelism
    - [Sequence Parallelism](https://arxiv.org/abs/2105.13120)
    - [Zero Redundancy Optimizer (ZeRO)](https://arxiv.org/abs/1910.02054)
    - [Auto-Parallelism](https://arxiv.org/abs/2302.02599)
- Heterogeneous Memory Management
    
    - [PatrickStar](https://arxiv.org/abs/2108.05818)
- Friendly Usage
    
    - Parallelism based on configuration file
- Inference
    
    - [Energon-AI](https://github.com/hpcaitech/EnergonAI)
([back to top](https://github.com/hpcaitech/ColossalAI#top))
## Parallel Training Demo
### GPT-3
![[GPT3-v5.png]]
- Save 50% GPU resources, and 10.7% acceleration
### GPT-2
![[GPT2.png]]
- 11x lower GPU memory consumption, and superlinear scaling efficiency with Tensor Parallelism
![[(updated)GPT-2.png]]
- 24x larger model size on the same hardware
- over 3x acceleration
### BERT
![[BERT.png]]
- 2x faster training, or 50% longer sequence length
### PaLM
- [PaLM-colossalai](https://github.com/hpcaitech/PaLM-colossalai): Scalable implementation of Google's Pathways Language Model ([[pa]]).
### OPT
![[OPT_update.png]]
- [Open Pretrained Transformer (OPT)](https://github.com/facebookresearch/metaseq), a 175-Billion parameter AI language model released by Meta, which stimulates AI programmers to perform various downstream tasks and application deployments because public pretrained model weights.
- 45% speedup fine-tuning OPT at low cost in lines. [[Example]](https://github.com/hpcaitech/ColossalAI/tree/main/examples/language/opt) [[Online Serving]](https://colossalai.org/docs/advanced_tutorials/opt_service)
Please visit our [documentation](https://www.colossalai.org/) and [examples](https://github.com/hpcaitech/ColossalAI/tree/main/examples) for more details.
### ViT
![[ViT.png]]
- 14x larger batch size, and 5x faster training for Tensor Parallelism = 64
### Recommendation System Models
- [Cached Embedding](https://github.com/hpcaitech/CachedEmbedding), utilize software cache to train larger embedding tables with a smaller GPU memory budget.
([back to top](https://github.com/hpcaitech/ColossalAI#top))
## Single GPU Training Demo
### GPT-2
![[GPT2-GPU1.png]]
- 20x larger model size on the same hardware
![[GPT2-NVME.png]]
- 120x larger model size on the same hardware (RTX 3080)
### PaLM
![[PaLM-GPU1.png]]
- 34x larger model size on the same hardware
([back to top](https://github.com/hpcaitech/ColossalAI#top))
## Inference (Energon-AI) Demo
![[inference_GPT-3.jpg]]
- [Energon-AI](https://github.com/hpcaitech/EnergonAI): 50% inference acceleration on the same hardware
    
- [OPT Serving](https://colossalai.org/docs/advanced_tutorials/opt_service): Try 175-billion-parameter OPT online services
    
![[BLOOM20Inference.png]]
- [BLOOM](https://github.com/hpcaitech/EnergonAI/tree/main/examples/bloom): Reduce hardware deployment costs of 176-billion-parameter BLOOM by more than 10 times.
([back to top](https://github.com/hpcaitech/ColossalAI#top))
## Colossal-AI in the Real World
### ChatGPT
A low-cost [ChatGPT](https://openai.com/blog/chatgpt/) equivalent implementation process. [[code]](https://github.com/hpcaitech/ColossalAI/tree/main/applications/ChatGPT) [[blog]](https://www.hpc-ai.tech/blog/colossal-ai-chatgpt)
Up to 7.73 times faster for single server training and 1.42 times faster for single-GPU inference
![[ChatGPT20scaling.png]]
![[ChatGPT-1GPU.jpg]]
- Up to 10.3x growth in model capacity on one GPU
- A mini demo training process requires only 1.62GB of GPU memory (any consumer-grade GPU)
![[LoRA20data.jpg]]
- Increase the capacity of the fine-tuning model by up to 3.7 times on a single GPU
- Keep in a sufficiently high running speed
([back to top](https://github.com/hpcaitech/ColossalAI#top))
### AIGC
Acceleration of AIGC (AI-Generated Content) models such as [Stable Diffusion v1](https://github.com/CompVis/stable-diffusion) and [Stable Diffusion v2](https://github.com/Stability-AI/stablediffusion).
![[Stable20Diffusion20v2.png]]
- [Training](https://github.com/hpcaitech/ColossalAI/tree/main/examples/images/diffusion): Reduce Stable Diffusion memory consumption by up to 5.6x and hardware cost by up to 46x (from A100 to RTX3060).
![[DreamBooth.png]]
- [DreamBooth Fine-tuning](https://github.com/hpcaitech/ColossalAI/tree/main/examples/images/dreambooth): Personalize your model using just 3-5 images of the desired subject.
![[Stable20Diffusion20Inference.jpg]]
- [Inference](https://github.com/hpcaitech/ColossalAI/tree/main/examples/images/diffusion): Reduce inference GPU memory consumption by 2.5x.
([back to top](https://github.com/hpcaitech/ColossalAI#top))
### Biomedicine
Acceleration of [AlphaFold Protein Structure](https://alphafold.ebi.ac.uk/)
![[FastFold.jpg]]
- [FastFold](https://github.com/hpcaitech/FastFold): accelerating training and inference on GPU Clusters, faster data processing, inference sequence containing more than 10000 residues.
![[xTrimoMultimer_Table.jpg]]
- [xTrimoMultimer](https://github.com/biomap-research/xTrimoMultimer): accelerating structure prediction of protein monomers and multimer by 11x.
([back to top](https://github.com/hpcaitech/ColossalAI#top))
## Installation

> Colossal-AI currently only supports the Linux operating system and has not been tested on other OS such as Windows and macOS.
> 
> Environment Requirement: PyTorch 1.10 ~ 1.12 (WIP higher version), Python >= 3.7, CUDA >= 11.0. If you encounter any problem about installation, you may want to raise an [issue](https://github.com/hpcaitech/ColossalAI/issues/new/choose) in this repository.
### Install from PyPI
You can easily install Colossal-AI with the following command. **By default, we do not build PyTorch extensions during installation.**
```Plain
pip install colossalai
```
However, if you want to build the PyTorch extensions during installation, you can set `CUDA_EXT=1`.
```Plain
CUDA_EXT=1 pip install colossalai
```
**Otherwise, CUDA kernels will be built during runtime when you actually need it.**
We also keep release the nightly version to PyPI on a weekly basis. This allows you to access the unreleased features and bug fixes in the main branch. Installation can be made via
```Plain
pip install colossalai-nightly
```
### Download From Source

> The version of Colossal-AI will be in line with the main branch of the repository. Feel free to raise an issue if you encounter any problem. :)
```Plain
git clone https://github.com/hpcaitech/ColossalAI.git
cd ColossalAI
# install colossalai
pip install .
```
By default, we do not compile CUDA/C++ kernels. ColossalAI will build them during runtime. If you want to install and enable CUDA kernel fusion (compulsory installation when using fused optimizer):
```Plain
CUDA_EXT=1 pip install .
```
([back to top](https://github.com/hpcaitech/ColossalAI#top))
## Use Docker
### Pull from DockerHub
You can directly pull the docker image from our [DockerHub page](https://hub.docker.com/r/hpcaitech/colossalai). The image is automatically uploaded upon release.
### Build On Your Own
Run the following command to build a docker image from Dockerfile provided.

> Building Colossal-AI from scratch requires GPU support, you need to use Nvidia Docker Runtime as the default when doing `docker build`. More details can be found [here](https://stackoverflow.com/questions/59691207/docker-build-with-nvidia-runtime). We recommend you install Colossal-AI from our [project page](https://www.colossalai.org/) directly.
```Plain
cd ColossalAI
docker build -t colossalai ./docker
```
Run the following command to start the docker container in interactive mode.
```Plain
docker run -ti --gpus all --rm --ipc=host colossalai bash
```
([back to top](https://github.com/hpcaitech/ColossalAI#top))
## Community
Join the Colossal-AI community on [Forum](https://github.com/hpcaitech/ColossalAI/discussions), [Slack](https://join.slack.com/t/colossalaiworkspace/shared_invite/zt-z7b26eeb-CBp7jouvu~r0~lcFzX832w), and [WeChat(微信)](https://raw.githubusercontent.com/hpcaitech/public_assets/main/colossalai/img/WeChat.png) to share your suggestions, feedback, and questions with our engineering team.
## Contributing
If you wish to contribute to this project, please follow the guideline in [Contributing](https://github.com/hpcaitech/ColossalAI/blob/main/CONTRIBUTING.md).
Thanks so much to all of our amazing contributors!
![[contributor_avatar.png]]
_The order of contributor avatars is randomly shuffled._
([back to top](https://github.com/hpcaitech/ColossalAI#top))
## CI/CD
We leverage the power of [GitHub Actions](https://github.com/features/actions) to automate our development, release and deployment workflows. Please check out this [documentation](https://github.com/hpcaitech/ColossalAI/blob/main/.github/workflows/README.md) on how the automated workflows are operated.
## Cite Us
```Plain
@article{bian2021colossal,
 title={Colossal-AI: A Unified Deep Learning System For Large-Scale Parallel Training},
 author={Bian, Zhengda and Liu, Hongxin and Wang, Boxiang and Huang, Haichen and Li, Yongbin and Wang, Chuanrui and Cui, Fan and You, Yang},
 journal={arXiv preprint arXiv:2110.14883},
 year={2021}
}
```
Colossal-AI has been accepted as official tutorials by top conference [SC](https://sc22.supercomputing.org/), [AAAI](https://aaai.org/Conferences/AAAI-23/), [PPoPP](https://ppopp23.sigplan.org/), [CVPR](https://cvpr2023.thecvf.com/), etc.
([back to top](https://github.com/hpcaitech/ColossalAI#top))