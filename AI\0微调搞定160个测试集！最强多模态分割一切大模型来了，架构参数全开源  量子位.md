---
Updated: 2023-12-12T13:16
tags:
  - AI->-CV
  - AI->-Image
  - AI->-Model
URL: https://www.qbitai.com/2023/12/105244.html
Created: 2023-12-11T22:33
---
大模型感知能力upup

> Brady 投稿
> 
> 量子位 | 公众号 QbitAI
用**多模态大模型**来做语义分割，效果有多好？
一张图+文字输入想分割的物体，大模型几秒钟就能识别并搞定！
只需输入想分割的对象如“擎天柱”，单个目标立刻就能被精准识别、快速切割：
![[2f591999ec3dcd6dea03d67b5b258b68.png]]
**多**个物体也是手到擒来，像是指定天空、水、树、草、女孩、龙猫（Chinchilla），同样能火速分割：
![[43a8222b0002056ce7daabcbfb294e98.png]]
表情包也没问题：
![[15f931691c611e065b308e1c2c999067.png]]
这是来自厦门大学等机构的最新多模态基础感知大模型，一上来就在**160个测试集**上达成了SOTA或持平近似的结果，没有针对任何下游任务进行微调。
![[b0863d5367a4878da136970835b2b9cb.png]]
目前，模型架构和参数已经全部开源，一起来看看这是怎么实现的。
# 多模态大模型APE长啥样？
此前，视觉基础模型（Vision Foundation Models）方向一直在探索建立一个通用的视觉感知系统。
![[e88d5f26ced28c41d852a9668f2b33c7.png]]
已有的方法可以**分为三类**，但都有一些不足之处：
**第一类**采用自监督的训练方式，例如DINO和CLIP等，这类方法在做下游感知类任务的时候需要再训练单独的Head或者Adapter；
**第二类**方法是开集检测，对齐图像的局部区域和文本表达，例如GLIP、UNINEXT和GroundingDINO等，但这类方法在每次推理时只能检测非常有限数量的类别（例如lvis的1023类单词要拆分成30多次推理）或者单个句子，检测类别或者句子长度的提升会给计算量和性能带来巨大的挑战；
**第三类**方法是开集语义分割，例如SAM、Xdecoder和SEEM等，但这类方法在加上语义信息时面临着前景可数物体和背景不可数物体的粒度差异问题，导致性能下降，这类方法往往需要设计特定的网络结构或者训练策略来缓解前背景冲突。
针对以上问题，来自厦门大学等机构的研究人员提出了一种新的模型，名叫**APE**（Aligning and Prompting Everything All at Once for Universal Visual Perception）。
![[1a7c1a11d8f3986f53849322cd84eecb.png]]
APE的框架如下所示：
![[f4197f6e3e17b8c5f728ba8afddaecb0.png]]
它有以下6点值得关注的设计：
# 1、Independent Prompt
给定多个目标类别，例如Girl和Sky等，以往的方法通常直接联结这些类别名组成一个单独的Prompt：“Girl. Sky. …”，这种构造方式是期望可以建模不同类别名之间的相互关系。
但论文发现，这种类别名之间的相互关系不是必须的，每种类别名独立建模就可以学习不同的实例。
为此，论文对每种类别名或者描述的短语都进行独立建模：[“Girl”, “Sky”, “Little child with hat on branch”, “The big chinchilla”, . . . ]，并获得其独立的prompt embedding。
# 2、Sentence-Level Embeddings
为了减少计算复杂度和内存消耗，论文进一步将Word-Level Embeddings压缩成Sentence-Level Embeddings，也就是将一句话中所有Word Embeddings求平均。
实验中发现，这种Sentence-Level Embeddings足够表达语义信息。
# 3、Gated Cross-modality Interaction
GLIP等方法直接融合文本和视觉特征，在类别名很多的情况下融合的代价逐步提升。
论文则针对不同的Prompt类型来进行特征融合，对于纯类别名（Vocabulary Prompt）的文本特征，采用一种“zero”文本token来作为它们的替代。
过往经验表明，直接将Vocabulary Prompt和视觉特征融合容易导致过拟合现象，使得在新类别上的表现欠佳。
语言描述（Sentence Prompt）的文本特征则被融合到视觉特征中以实现语义级的理解。
# 4、Region-sentence Alignment
论文直接通过矩阵乘法计算Object Embeddings和Prompt Embeddings之间的相似度，从而实现一次性检测&分割万物。
值得注意的是论文维持了一个Embedding Bank用于构建负样本。
# 5、Thing-stuff-equalizing Alignment
分割任务的前景和背景的粒度是不同的，比如背景天空即使由好几块组成，但仍然被认为是同一个标签，而前景比如人则是单独的标签。
这种粒度差异会给模型训练带来挑战，为此论文提出统一前景和背景的粒度，对于不同块的背景论文将其视为独立的标签，如上图中的“天空”。
这使得模型可以采用统一的架构训练前景和背景数据，也可以方便地融入SA-1B这类大规模的Class-Agnostic数据。
# 6、数据配比
论文使用了10中常见的开源数据集进行训练，包括：
通用检测分割数据（COCO、Objects365），长尾检测分割数据（LVIS），联邦标注的数据（OpenImages），指向性检测分割数据（VG、RefCOCO/+/g、GQA、Phrascut、Flickr30k），不带语义的分割数据（SA-1B）。
论文提出一系列原则精心设计数据配比和损失权重。
# 160个测试集SOTA或近似打平
作者们进行了大规模的实验，以验证论文方法的有效性。
他们一共训练了四组大模型：
- APE (A)：基础版，基于DETA构建，并只在通常的检测和分割数据集上训练，包括COCO, LVIS, Objects365, OpenImages, and Visual Genome。
- APE (B)：APE (A)的基础上加入Visual Genome和COCO的指向性检测和分割数据进行训练。
- APE (C)：进一步加入大规模SA-1B数据集进行训练。
- APE (D)：除了上面的数据，进一步加入GQA, PhraseCut, Flickr30k数据集，并且修改了部分训练策略。
其中，APE-ABCD分别对应不同的训练数据。
实验表明，这种方法在**160种测试集**上普遍取得了当前SOTA或具有竞争性的结果。
值得注意的是，论文只采用了一个模型架构和一套参数，**没有针对下游任务进行微调**。
# 性能对比总览
整体上看，APE方法在各个**检测**、**分割**和**指向性检测**数据集上都比之前的方法好，特别是在D3数据集上。
![[64eb53ff5432a025f8f31cc10544c8ce.png]]
# 开集检测
在检测上，论文主要比较了LVIS、OpenImages，Objects365和COCO这四个常见数据集。
APE的效果优势非常明显。
此前不少方法都在Objects365上预训练过，例如GLIP、OWL和UNINEXT，但是它们在这些训练过的数据集上效果也并不是很好。
另外，实验还比较了RoboFlow100和ODinW评测基准。RoboFlow100和ODinW分别由100和35个独立的小数据集组成，专门用于开集评测，APE在这两个数据集上取得了新SOTA。
![[d7edc5dea7954895672611eed02213fc.png]]
# 开集分割
在开集分割评测基准上，当类别数量较多时候，例如PC-459、ADE20K和SegInW分别有459、150和85个类，APE的效果比其他方法好不少。
其中，SegInW由25个差异很大的小数据集组成，专门用于开集分割评测。而在类别数量较少的数据集上，APE的效果相对差些。
![[a340a015677f63b29ffb7ecef374605c.png]]
# 视觉定位
在视觉定位D3评测基准上，APE取得的效果提升最明显。特别在inter-scenario的设定下，大部分方法的指标都低于6，而APE可以取得21.0。
因为在inter-scenario设定下，每张图都要用422个句子去查询，之前模型总是会每句话预测一个物体，因此效果不理想，而APE会拒绝不相关的查询。
在intra-scenario设定下，APE在各项指标上也取得了15+个点的提升。
![[405529ec001387850adc89bff7cf7cce.png]]
参考链接：
[1]论文：https://arxiv.org/pdf/2312.02153.pdf
[2]开源项目：https://github.com/shenyunhang/APE
[3]Demo：https://huggingface.co/spaces/shenyunhang/APE_demo
_版权所有，未经授权不得以任何形式转载及使用，违者必究。_