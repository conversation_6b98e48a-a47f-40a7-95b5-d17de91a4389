---
Updated: 2024-03-25T11:03
tags:
  - AI->-Programming
  - AI->-TimeSeries
Created: 2024-03-25T11:03
---
[![](https://miro.medium.com/v2/da:true/resize:fit:1200/0*4bEzImG20k3iBFP5)](https://miro.medium.com/v2/da:true/resize:fit:1200/0*4bEzImG20k3iBFP5)
## ==Explore the architecture of TiDE and apply it in a forecasting project using Python====  
  
====探索 TiDE 的架构，并在使用 Python 进行预测项目中应用它==
==[==
[![](https://miro.medium.com/v2/resize:fill:44:44/1*0HKbgLGe-BeQE3o240uafw.jpeg)](https://miro.medium.com/v2/resize:fill:44:44/1*0HKbgLGe-BeQE3o240uafw.jpeg)
==](https://medium.com/@marcopeixeiro?source=post_page-----b043acc60f79--------------------------------)[==
[![](https://miro.medium.com/v2/resize:fill:24:24/1*CJe3891yB1A1mzMdqemkdg.jpeg)](https://miro.medium.com/v2/resize:fill:24:24/1*CJe3891yB1A1mzMdqemkdg.jpeg)
==](https://towardsdatascience.com/?source=post_page-----b043acc60f79--------------------------------)==
[![](https://miro.medium.com/v2/resize:fit:875/0*4bEzImG20k3iBFP5)](https://miro.medium.com/v2/resize:fit:875/0*4bEzImG20k3iBFP5)
==Photo by== [==Boris Smokrovic==](https://unsplash.com/@borisworkshop?utm_source=medium&utm_medium=referral) ==on== [==Unsplash==](https://unsplash.com/?utm_source=medium&utm_medium=referral)==  
  
====在 Unsplash 上的照片，由 Boris Smokrovic 拍摄==
==In our exploration of the latest advances in the field of time series forecasting, we discovered== [==N-HiTS==](https://medium.com/towards-data-science/all-about-n-hits-the-latest-breakthrough-in-time-series-forecasting-a8ddcb27b0d5)==,== [==PatchTST==](https://medium.com/towards-data-science/patchtst-a-breakthrough-in-time-series-forecasting-e02d48869ccc)==,== [==TimeGPT==](https://medium.com/towards-data-science/timegpt-the-first-foundation-model-for-time-series-forecasting-bf0a75e63b3a) ==and also== [==TSMixer==](https://medium.com/towards-data-science/tsmixer-the-latest-forecasting-model-by-google-2fd1e29a8ccb)==.====  
  
====在我们对时间序列预测领域的最新进展进行探索时，我们发现了 N-HiTS、PatchTST、TimeGPT 和 TSMixer。==
==While many efforts have been deployed to apply the Transformer architecture for forecasting, it turns out that it achieves a mediocre performance considering the computation requirements.====  
  
====尽管已经采取了许多努力将 Transformer 架构应用于预测，但考虑到计算需求，结果表明其性能一般。==
==In fact, simple linear models have been shown to outperform the complex Transformer-based models on many benchmark datasets (see== [==Zheng et al., 2022==](https://arxiv.org/pdf/2205.13504.pdf)==).====  
  
====事实上，简单的线性模型在许多基准数据集上表现出比复杂的基于 Transformer 的模型更好的性能（参见郑等人，2022 年）。==
==Motivated by that, in April 2023, researchers at Google proposed TiDE: a long-term forecasting model with an encoder-decoder architecture built with Multilayer Perceptrons (MLPs).====  
  
====受此激励，2023 年 4 月，谷歌的研究人员提出了 TiDE：一个使用多层感知器（MLPs）构建的编码器-解码器架构的长期预测模型。==
==In their paper== [==Long-term Forecasting with TiDE: Time-series Dense Encoder==](https://arxiv.org/pdf/2304.08424.pdf)==, the authors demonstrate that the model achieves state-of-the-art results on numerous datasets when compared to other Transformer-based and MLP-based models, like PatchTST and N-HiTS respectively.====  
  
====在他们的论文《使用 TiDE 进行长期预测：时间序列密集编码器》中，作者们展示了该模型在与其他基于 Transformer 和 MLP 的模型（如 PatchTST 和 N-HiTS）进行比较时，在许多数据集上取得了最先进的结果。==
==In this article, we first explore the architecture and inner workings of TiDE. Then, we apply the model in Python and use it in our own small forecasting experiment.====  
  
====在本文中，我们首先探讨了 TiDE 的架构和内部工作原理。然后，我们在 Python 中应用该模型，并在我们自己的小型预测实验中使用它。==
==For more details on TiDE, make sure to read the== [==original paper==](https://arxiv.org/pdf/2304.08424.pdf)==.====  
  
====有关 TiDE 的更多详细信息，请务必阅读原始论文。==
==_**Learn the latest time series analysis techniques with my**_== [==_**free time series cheat sheet**_==](https://www.datasciencewithmarco.com/pl/2147608294) ==_**in Python! Get the implementation of statistical and deep learning techniques, all in Python and TensorFlow!**_====  
  
====使用我的免费时间序列 Python 速查表，学习最新的时间序列分析技术！获取统计和深度学习技术的 Python 和 TensorFlow 实现！==
==Let’s get started! 让我们开始吧！==
## ==Explore TiDE 探索 TiDE==
==TiDE stands for== ==**Ti**====me-series== ==**D**====ense== ==**E**====ncoder. At its base, this model implements the encoder-decoder concept without the attention mechanism in Transformer-based models.====  
  
====TiDE 代表时间序列密集编码器。在其基础上，该模型实现了编码器-解码器的概念，但没有使用 Transformer 模型中的注意力机制。==
==Instead, it relies on MLPs to achieve faster training and inference times, while achieving good performance.====  
  
====相反，它依靠多层感知器（MLPs）来实现更快的训练和推理时间，同时达到良好的性能。==
==During training, the model will encode historical data along with covariates. Then, it will decode the learned representation along with known future covariates to make predictions.====  
  
====在训练过程中，模型将对历史数据和协变量进行编码。然后，它将解码学习到的表示，并结合已知的未来协变量进行预测。==
## ==Architecture of TiDE TiDE 的架构==
==The architecture of TiDE is presented in the figure below.====  
  
====TiDE 的架构如下图所示。==
[![](https://miro.medium.com/v2/resize:fit:875/1*ndsyqZxHG5HdKysvPEotOg.png)](https://miro.medium.com/v2/resize:fit:875/1*ndsyqZxHG5HdKysvPEotOg.png)
==Architecture of TiDE. Image by A. Das, W. Kong, A. Leach, S. Mathur, R. Sen, R. Yu from== [==Long-term Forecasting with TiDE: Time-series Dense Encoder==](https://arxiv.org/pdf/2304.08424.pdf)==.====  
  
====TiDE 的架构。图片由 A. Das，W. Kong，A. Leach，S. Mathur，R. Sen，R. Yu 创作，摘自《使用 TiDE 进行长期预测：时间序列密集编码器》。==
==From the figure above, we can see that the model treats each series as an independent channel, meaning that one series along with its covariates are passed at a time.====  
  
====从上图可以看出，该模型将每个系列视为独立的通道，意味着每次只传递一个系列及其协变量。==
==We also see that there are three main components to the model: an encoder, a decoder and a temporal decoder, and all rely on the residual block architecture.====  
  
====我们还看到模型有三个主要组件：编码器、解码器和时间解码器，它们都依赖于残差块架构。==
==There is a lot of information in this figure, so let’s explore each component in more detail.====  
  
====这个图中有很多信息，所以让我们更详细地探索每个组成部分。==
## ==Explore the residual block====  
  
====探索残差块==
==As mentioned, the residual block serves as the base layer in TiDE’s architecture.====  
  
====如前所述，残差块在 TiDE 的架构中作为基础层。==
[![](https://miro.medium.com/v2/resize:fit:798/1*Sgb72KeEpP7GDy9F-EiDOg.png)](https://miro.medium.com/v2/resize:fit:798/1*Sgb72KeEpP7GDy9F-EiDOg.png)
==Components of the residual block. Image by A. Das, W. Kong, A. Leach, S. Mathur, R. Sen, R. Yu from== [==Long-term Forecasting with TiDE: Time-series Dense Encoder==](https://arxiv.org/pdf/2304.08424.pdf)==.====  
  
====残差块的组成部分。图片由 A. Das、W. Kong、A. Leach、S. Mathur、R. Sen、R. Yu 创作，摘自《使用 TiDE 进行长期预测：时间序列密集编码器》。==
==From the figure above, we see that it is an MLP with one hidden layer and a ReLU activation. This is followed by a dropout layer, a skip connection, and a final layer normalization step.====  
  
====从上图中，我们可以看到这是一个具有一个隐藏层和 ReLU 激活函数的多层感知机。接下来是一个 dropout 层，一个跳跃连接和最后的层归一化步骤。==
==Then, this component is reused across the network to encode, decode and make predictions.====  
  
====然后，这个组件在网络中被重复使用来进行编码、解码和预测。==
## ==Understand the encoder 了解编码器==
==During this step, the model maps the past and the covariates of a time series to a dense representation.====  
  
====在这一步骤中，模型将时间序列的过去和协变量映射到一个密集的表示。==
==The first step if to carry out feature projection. This is where the residual block is used to map dynamic covariates (exogenous variables varying in time) into a lower dimensional projection.====  
  
====第一步是进行特征投影。这是使用残差块将动态协变量（随时间变化的外生变量）映射到较低维度投影的过程。==
==Keep in mind that when performing multivariate forecasting, we need the future values of our features. Therefore, the model must treat a look-back window and a horizon sequence.====  
  
====请记住，在进行多元预测时，我们需要特征的未来值。因此，模型必须处理一个回顾窗口和一个预测序列。==
==These sequences can get very long, so by projecting to a lower dimensional space, we keep the lengths manageable and allow the model to treat longer sequences, both in terms of historical window and horizon of forecast.====  
  
====这些序列可能会变得非常长，因此通过投影到较低维度的空间，我们可以保持长度可控，并允许模型处理更长的序列，无论是历史窗口还是预测的时间范围。==
==The second step, is then to concatenate the past of the series with its attributes and projection of past and future covariates. This is then sent to the encoder, which is simply a stack of residual blocks.====  
  
====第二步是将序列的过去部分与其属性以及过去和未来协变量的投影连接起来。然后将其发送到编码器，它只是一个残差块的堆叠。==
==The encoder is thus responsible for learning a representation of the inputs. This can be seen as a learned embedding.====  
  
====编码器因此负责学习输入的表示。这可以看作是一个学习到的嵌入。==
==Once done, the embedding is sent to the decoder.====  
  
====一旦完成，嵌入将被发送到解码器。==
## ==Understand the decoder 了解解码器==
==Here, the decoder is responsible for taking the learned representation of the encoder and generating predictions.====  
  
====在这里，解码器负责将编码器学习到的表示转化为预测。==
==The first step is the dense decoder, which is also composed of a stack of residual blocks. This takes the encoded information and outputs a matrix to be fed to the temporal decoder.====  
  
====第一步是密集解码器，它也由一堆残差块组成。它将编码信息输入，并输出一个矩阵供时间解码器使用。==
==The decoded output is stacked with projected features to capture direct effects of future covariates. For example, holidays are punctual events that can have important impacts on certain time series. With this residual connection, the model can capture and leverage that information.====  
  
====解码后的输出与投影特征叠加在一起，以捕捉未来协变量的直接影响。例如，假期是可能对某些时间序列产生重要影响的 punctual 事件。通过这个残差连接，模型可以捕捉并利用这些信息。==
==The second step is then the temporal decoder, where predictions are generated. Here, it is simply a residual block with an output size of 1, such that we get the predictions for a given time series.====  
  
====第二步是时间解码器，用于生成预测。在这里，它只是一个输出大小为 1 的残差块，以便我们得到给定时间序列的预测。==
==Now that we understand each critical component of TiDE, let’s apply it in a small forecasting project using Python.====  
  
====现在我们了解了 TiDE 的每个关键组成部分，让我们在使用 Python 进行一个小的预测项目中应用它。==
## ==Forecast using TiDE 使用 TiDE 进行预测==
==Now, let’s apply TiDE in a small forecasting project and compare its performance to TSMixer.====  
  
====现在，让我们在一个小的预测项目中应用 TiDE，并将其性能与 TSMixer 进行比较。==
==Interestingly, TSMixer is also an MLP-based architecture for multivariate forecasting developed by Google researchers, but it was published a month before TiDE. So I think it is interesting to compare both models in a small experiment.====  
  
====有趣的是，TSMixer 也是由 Google 研究人员开发的基于 MLP 的多变量预测架构，但它在 TiDE 之前一个月就已经发布。因此，我认为在一个小实验中比较这两个模型是很有趣的。==
==For more details on TSMixer, make sure to read== [==my article==](https://medium.com/towards-data-science/tsmixer-the-latest-forecasting-model-by-google-2fd1e29a8ccb) ==entirely dedicated to it.====  
  
====有关 TSMixer 的更多详细信息，请确保完整阅读我专门为此撰写的文章。==
==In this project, we use the== [==Etth1 dataset==](https://github.com/zhouhaoyi/ETDataset) ==released under the Creative Commons Attribution license.====  
  
====在这个项目中，我们使用了根据创作共用署名许可发布的 Etth1 数据集。==
==This is a popular benchmark for time series forecasting widely used in literature. It tracks the hourly oil temperature of an electricity transformer along with other covariates, making it a great scenario for multivariate forecasting.====  
  
====这是一种在文献中广泛使用的时间序列预测的流行基准。它跟踪电力变压器的每小时油温以及其他协变量，使其成为多变量预测的一个很好的场景。==
==The full source code of this experiment is available on== [==GitHub==](https://github.com/marcopeix/time-series-analysis/blob/master/TiDE.ipynb)==.====  
  
====这个实验的完整源代码可以在 GitHub 上找到。==
## ==Import libraries and read the data====  
  
====导入库并读取数据==
==The natural first step is to import the required libraries for this project and read the data.====  
  
====自然的第一步是导入此项目所需的库并读取数据。==
==While the source code of the original paper of TiDE is publicly available on== [==GitHub==](https://github.com/google-research/google-research/tree/master/tide)==, I instead opted to use the implementation available in Darts.====  
  
====尽管 TiDE 的原始论文源代码在 GitHub 上是公开可用的，但我选择使用 Darts 中提供的实现。==
==It will provide us with greater flexibility and it comes with hyperparameter optimization capabilities that are not available in the original repository.====  
  
====它将为我们提供更大的灵活性，并具有原始存储库中不可用的超参数优化功能。==
==So, let’s import== ==`darts`== ==as well as other standard packages.====  
  
====所以，让我们导入== ==`darts`== ==以及其他标准包。==
==import pandas as pd====  
  
====import numpy as np====  
  
====import matplotlib.pyplot as plt==
==from darts import TimeSeries====  
  
====from darts.datasets import ETTh1Dataset==
==Then, we can read our data. Luckily for us, Darts comes with standard datasets used across academia, like the Etth1 dataset.====  
  
====然后，我们可以读取我们的数据。幸运的是，Darts 提供了学术界常用的标准数据集，例如 Etth1 数据集。==
==series = ETTh1Dataset().load()==
==Finally, let’s split our data and reserve the last 96 time steps for the test set.====  
  
====最后，让我们将数据分割，并保留最后的 96 个时间步作为测试集。==
==train, test = series[:-96], series[-96:]==
==We are now ready to train our TiDE model.====  
  
====我们现在准备训练我们的 TiDE 模型。==
## ==Train TiDE 火车潮==
==To access TiDE, we simply import it from the== ==`darts`== ==library. We also need to manually scale our data before training. This ensures a faster and more stable training procedure.====  
  
====要访问 TiDE，我们只需从== ==`darts`== ==库中导入它。在训练之前，我们还需要手动对数据进行缩放。这样可以确保训练过程更快、更稳定。==
==from darts.models.forecasting.tide_model import TiDEModel====  
  
====from darts.dataprocessing.transformers import Scaler==
==train_scaler = Scaler()====  
  
====scaled_train = train_scaler.fit_transform(train)==
==Then, we initialize the model and specify its parameters. Here, I am using the same optimized parameters as presented in the paper for this particular dataset.====  
  
====然后，我们初始化模型并指定其参数。在这里，我使用的是与论文中针对这个特定数据集所提出的优化参数相同的参数。==
==tide = TiDEModel(====  
  
====input_chunk_length=720,====  
  
====output_chunk_length=96,====  
  
====num_encoder_layers=2,====  
  
====num_decoder_layers=2,====  
  
====decoder_output_dim=32,====  
  
====hidden_size=512,====  
  
====temporal_decoder_hidden=16,====  
  
====use_layer_norm=True,====  
  
====dropout=0.5,====  
  
====random_state=42)==
==Then, we can simply fit the model. Note that I only train it on 30 epochs, so feel free to increase that number if you have more time and computing resources available.====  
  
====然后，我们可以简单地拟合模型。请注意，我只训练了 30 个周期，如果你有更多时间和计算资源可用，可以随意增加这个数字。==
==tide.fit(====  
  
====scaled_train,====  
  
====epochs=30====  
  
====)==
==Once the model is done training, we can access its predictions. Note that because we scaled the training data, the model also outputs scaled predictions. Therefore, we must reverse the transformation.====  
  
====一旦模型训练完成，我们就可以访问它的预测结果。请注意，由于我们对训练数据进行了缩放，模型也会输出缩放后的预测结果。因此，我们需要将其还原。==
==scaled_pred_tide = tide.predict(n=96)==
==pred_tide = train_scaler.inverse_transform(scaled_pred_tide)==
==Perfect! We can then evaluate the performance of TiDE.====  
  
====完美！我们可以评估 TiDE 的性能。==
## ==Evaluate the performance 评估表现==
==To evaluate our model’s performance, let’s store the predictions and the actual values in a DataFrame.====  
  
====为了评估我们模型的性能，让我们将预测值和实际值存储在一个数据框中。==
==preds_df = pred_tide.pd_dataframe()====  
  
====test_df = test.pd_dataframe()==
==Optionally, we can visualize the predictions. For simplicity, I am only plotting four columns.====  
  
====可选的，我们可以将预测结果可视化。为简单起见，我只绘制了四列。==
==cols_to_plot = ['OT', 'HULL', 'MUFL', 'MULL']==
==fig, axes = plt.subplots(nrows=2, ncols=2, figsize=(12,8))==
==for i, ax in enumerate(axes.flatten()):====  
  
====col = cols_to_plot[i]==
```plain
        ax.plot(test\_df\[col\], label='Actual', ls='-', color='blue')  
ax.plot(preds\_df\[col\], label='TiDE', ls='--', color='green')
    ax.legend(loc='best')  
ax.set\_xlabel('Date')  
ax.set\_title(col)
plt.tight\_layout()  
```
==fig.autofmt_xdate()==
[![](https://miro.medium.com/v2/resize:fit:875/1*ihiQsqcDkOk3lsJWwkWQxQ.png)](https://miro.medium.com/v2/resize:fit:875/1*ihiQsqcDkOk3lsJWwkWQxQ.png)
==Visualizing the predictions from TiDE. Image by the author.====  
  
====TiDE 的预测可视化。图片由作者提供。==
==From the figure above, we can see that TiDE does a pretty good job at forecasting each series.====  
  
====从上图可以看出，TiDE 在预测每个系列方面做得相当不错。==
==Of course, the best way to evaluate the performance is to calculate error metrics, so let’s compute the mean absolute error (MAE) and mean squared error (MSE).====  
  
====当然，评估性能的最佳方法是计算错误指标，因此让我们计算平均绝对误差（MAE）和均方误差（MSE）。==
==from darts.metrics import mae, mse==
==tide_mae = mae(test, pred_tide)====  
  
====tide_mse = mse(test, pred_tide)==
==print(tide_mae, tide_mse)==
==This gives us a MAE of 1.19 and a MSE of 3.58.====  
  
====这给我们带来了 1.19 的平均绝对误差和 3.58 的均方误差。==
==Now, to keep this article at reasonable length, I will not cover the implementation of TSMixer.====  
  
====现在，为了保持这篇文章的合理长度，我不会涉及 TSMixer 的实施。==
==At this moment, there is no ready-to-use implementation of it, so we have to many steps manually. Again, for more details, you can read== [==my article==](https://medium.com/towards-data-science/tsmixer-the-latest-forecasting-model-by-google-2fd1e29a8ccb) ==covering TSMixer.====  
  
====此刻，还没有现成的实现，所以我们必须手动完成许多步骤。再次提醒，如需更多详细信息，请阅读我关于 TSMixer 的文章。==
==For now, let’s just report the performance of TSMixer for multivariate forecasting on the Etth1 dataset on a horizon of 96 time steps.====  
  
====目前，让我们只报告 TSMixer 在 Etth1 数据集上进行多元预测的性能，时间步长为 96。==
[![](https://miro.medium.com/v2/resize:fit:551/1*iddvXyopjqZTQdtHOiGX_g.png)](https://miro.medium.com/v2/resize:fit:551/1*iddvXyopjqZTQdtHOiGX_g.png)
==Performance metrics of TiDE and TSMixer for multivariate forecasting on the Etth1 dataset on a horizon of 96 time steps. We can see that TiDE achieves the best performance. Image by the author.====  
  
====TiDE 和 TSMixer 在 Etth1 数据集上进行多元预测的性能指标，预测时间范围为 96 个时间步。我们可以看到 TiDE 取得了最佳性能。图片由作者提供。==
==From the figure above, we can see that TiDE achieves the lowest MAE and MSE, meaning that it outperforms TSMixer.====  
  
====从上图可以看出，TiDE 实现了最低的 MAE 和 MSE，这意味着它优于 TSMixer。==
==Of course, this is not an extensive experiment, so it does not mean that TiDE is always better than TSMixer.====  
  
====当然，这并不是一项全面的实验，所以并不意味着 TiDE 总是比 TSMixer 更好。==
==Although both models have an MLP-based architecture, it may be that TiDE represents an incremental improvement over TSMixer. Nevertheless, you should always test different models for your particular dataset and find which works best.====  
  
====虽然两个模型都基于 MLP 架构，但 TiDE 可能比 TSMixer 有所改进。然而，您应该始终测试适用于您特定数据集的不同模型，并找到其中哪个效果最佳。==
## ==Conclusion 结论==
==TiDE stands for Time-series Dense Encoder, and it is an MLP-based model designed for long-horizon multivariate forecasting.====  
  
====TiDE 代表时间序列密集编码器，它是一种基于多层感知器的模型，专为长期多变量预测而设计。==
==It relies on the residual block unit to first encode covariates and historical data. Then, it decodes the learned representation and generates forecast.====  
  
====它依赖于残差块单元来首先对协变量和历史数据进行编码。然后，它解码学习到的表示并生成预测。==
==Since its only MLPs, it benefits from faster training times and it was demonstrated to still achieve high performances on long-term forecasting tasks.====  
  
====由于只有多层感知器（MLPs），它在训练时间上受益于更快的速度，并且已经证明在长期预测任务上仍能取得高性能。==
==Of course, each problem is unique, so make sure to test TiDE as well as other models.====  
  
====当然，每个问题都是独特的，所以请确保测试 TiDE 以及其他模型。==
==Thanks for reading! I hope that you enjoyed it and that you learned something new!====  
  
====谢谢阅读！希望你喜欢并学到了新的东西！==
==Looking to master time series forecasting? The check out my course== [==Applied Time Series Forecasting in Python==](https://www.datasciencewithmarco.com/offers/zTAs2hi6/checkout?coupon_code=ATSFP10)==. This is the only course that uses Python to implement statistical, deep learning and state-of-the-art models in 16 guided hands-on projects.====  
  
====想要掌握时间序列预测吗？那就来看看我的课程《Python 应用时间序列预测》吧。这是唯一一个使用 Python 实现统计学、深度学习和最先进模型的课程，包含 16 个实践项目。==
==Cheers 🍻 干杯 🍻==
## ==Support me 支持我==
==Enjoying my work? Show your support with== [==Buy me a coffee==](http://buymeacoffee.com/dswm)==, a simple way for you to encourage me, and I get to enjoy a cup of coffee! If you feel like it, just click the button below 👇====  
  
====喜欢我的工作吗？用“给我买杯咖啡”来支持我吧，这是一个简单的方式让你鼓励我，而我可以享受一杯咖啡！如果你愿意的话，就点击下面的按钮👇==
[![](https://miro.medium.com/v2/resize:fit:381/0*4KNoi3GUHUnfajdA.png)](https://miro.medium.com/v2/resize:fit:381/0*4KNoi3GUHUnfajdA.png)
## ==References 参考资料==
==Abhimanyu Das, Weihao Kong, Andrew Leach, Shaan Mathur, Rajat Sen, Rose Yu —== [==Long-term Forecasting with TiDE: Time-series Dense Encoder==](https://arxiv.org/abs/2304.08424)==  
  
====阿比曼尼·达斯，魏浩孔，安德鲁·利奇，沙恩·马图尔，拉贾特·森，罗斯·于——使用 TiDE 进行长期预测：时间序列密集编码==
==Original implementation of TiDE by researchers —== [==GitHub==](https://github.com/google-research/google-research/tree/master/tide)==  
  
====TiDE 的原始实现由研究人员完成 - GitHub==