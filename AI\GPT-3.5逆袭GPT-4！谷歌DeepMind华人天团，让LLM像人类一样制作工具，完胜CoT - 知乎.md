---
Updated: 2023-06-08T10:50
tags:
  - AI->-Theory
Created: 2023-06-01T23:47
---
编辑：好困 Aeneas
## 【新智元导读】最近，来自Google DeepMind，普林斯顿和斯坦福的顶尖华人团队提出了一种全新的框架。现在，LLM可以像人类一样制作自己的工具了！
ChatGPT等大语言模型诞生以来，凭着强大的语言理解能力、生成能力、逻辑推理能力等，已经被人类玩出了花。
而OpenAI公开GPT-4后，最大的惊喜之一，莫过于插件模式的引入了。
插件使得GPT-4可以执行代码、搜索引擎、集成各种APP的功能。这无疑是对于LLM后续应用模式的一大突破。
![[v2-610ffd584a98abab139dfad961a40f61_b.jpg]]
显然，通过工具的使用，我们可以显著地提升LLM的生产力，
但是，如果没有合适的工具怎么办？
——那就让LLM自己造！
最近，来自Google DeepMind，普林斯顿和斯坦福的华人团队提出了一种闭环框架，让LLM可以通过程序的形式，制作并使用全新的工具（能重复使用的那种）。
简单来说，就是让一个LLM作为工具制造者制作新工具，另一个LLM作为工具使用者，使用工具来解决新问题。
论文地址：[arxiv.org/abs/2305.1712](https://link.zhihu.com/?target=https%3A//arxiv.org/abs/2305.17126)
项目地址：[github.com/ctlllll/LLM-](https://link.zhihu.com/?target=https%3A//github.com/ctlllll/LLM-ToolMaker)
基于这个框架，LLM就变得像人类的祖先一样，能够自我开发，并且学会使用工具。
同时，这种工具制造者和使用者之间的分工，在不降低生成工具和解决方案质量的情况下，就能实现成本效益。
结果显示，当使用GPT-4作为工具制造者，GPT-3.5 Turbo作为工具使用者时，可以在成本低、速度快的GPT-3.5 Turbo上实现与GPT-4相当的性能。
**让LLM自己造工具**
虽然LLM很好用，但如果问题太多的话，直接丢给GPT-4这种性能很强的模型，成本会非常高。
另一方面，轻量级模型在成本效益上很高，但通常在处理复杂任务时遇到困难。
LATM通过利用强大的模型作为工具制造者，为请求中观察到的任务生成可重复使用的工具（以Python函数实现），并将工具传递给一个成本效益高的工具使用者模型，用于解决后续请求中的类似实例。
这种方法使得轻量级模型在保持更高的成本效率的同时，能够达到与强大模型相当的性能。
LATM的闭环框架
LATM可以分为两个阶段：
**1. 制造工具：**一个强大但更昂贵的模型作为工具制造者，从一些示例中生成通用且可重复使用的工具；
**2. 使用工具：**一个轻量且更便宜的模型作为工具使用者，使用工具来解决任务的各种实例。
制造工具阶段可以进一步划分为三个子阶段：
**（1）工具提议：**工具制造者试图从一些训练示例中生成工具（Python函数），如果工具无法执行，报告错误并生成新的（修复函数中的问题）；
**（2）工具验证：**工具制造者在验证样本上运行单元测试，如果工具没有通过测试，报告错误并生成新的测试（修复单元测试中函数调用的问题）；
**（3）工具封装：**封装函数代码和如何从单元测试中将问题转换为函数调用的示例，为工具使用者准备可用的工具。
LATM的流程
在现实世界的场景中，任务实例通常都是按顺序来的。为了适应这种数据流，团队引入了第三个LLM——分派器。
具体来说，当收到一个新的任务实例时，分派器首先确定是否有适合当前任务的工具。
**· 如果存在合适的工具，**分派器将实例及其相应的工具传递给工具使用者进行任务解决。
**· 如果没有找到合适的工具，**分派器将该实例识别为新任务，并使用强大的模型甚至调用人类标注者来解决该实例。
鉴于分派任务的简单性，分派器可以是一个配备了适当提示的轻量级模型，这只会为整个流程增加很小的成本。
结果显示，分派器可以有效地识别现有的工具，而且对于没有见过的任务也不会有显著的性能下降。
分派器的工作流程
在下面这个任务示例中，需要根据给定的几个条件确定五个对象的顺序。
在工具提议阶段，工具制造者（如GPT-4）会写一个通用的Python函数，能够解决任务中提供的k个示例（实验中k=3）。
然后，工具制造者会生成一个搜索算法，枚举所有可能的排序，并根据给定的条件进行验证
在工具使用阶段，工具使用者将每个自然语言问题转化为一系列条件，生成函数调用，利用工具解决每个任务实例。
工具提议和工具使用阶段（逻辑推理任务）
结果显示，不管是由人类编写还是由GPT-4生成，CoT在任务完成的准确率上，都会被LATM吊打。
使用GPT-4生成的CoT的准确性
如表1所示，当被用作工具制造者时，GPT-4会搜索所有排列组合，并选出满足给定约束条件的，最终成功解决任务。
而且，工具制造者即便是接收到了错误的信息，也依然有能力进行纠正。
工具制造者为解决任务而生成的函数
在表2中，团队将思维链提示方法和LATM的性能进行了比较。
结果显示，有了工具的帮助，像GPT-3.5 Turbo这样的轻量级模型可以实现与GPT-4相当的性能，并显著优于CoT提示。
而且，能够利用工具的GPT-3.5 Turbo在平均成本上，也比直接用GPT-4低得多。
有趣的是，对于Dyck语言任务，作为工具使用者的GPT-3.5 Turbo，甚至甚至超过了同样作为使用者的GPT-4。
在研究错误的案例时，团队发现，在将问题转化为函数调用时，GPT-4有时会自发地去「解决」问题。然而，这是完全不必要的，而且还会导致函数输出错误。
LATM和思维链（CoT）的性能比较
此外，在所有的模型中，GPT-3.5 Turbo也有着最佳的性价比。
各种工具使用者模型的性能比较
不过，虽然GPT-3.5 Turbo在作为使用者时表现出色，但它并不能很好地扮演工具制造者的角色。
结果显示，对于像逻辑推理这样比较困难的任务，GPT-3.5 Turbo一次都没成功。
也就是说，使用一个强大的模型作为工具的制造者，是很有必要的。
GPT-4和GPT-3.5 Turbo在生成新工具时的成功率
**LATM Prompt**
工具制造
工具验证
工具封装
分派器
**封装工具**
逻辑推理
查找随机对象
Dyck语言
单词排序
余数定理
安排会议
**作者介绍**
论文一作Tianle Cai（蔡天乐），是普林斯顿大学的一年级博士生。
在此之前，他就读于北京大学，主修应用数学，同时兼修计算机科学。
他的兴趣非常广泛，横跨机器学习的很堵领域。比如，优化，表征学习，架构设计（Transformer，图神经网络等）。宗旨就是，让机器学习更通用，更高效，更可靠。
Xuezhi Wang是谷歌大脑的研究科学家。
在此之前，她于2016年在卡耐基梅隆大学计算机科学系获得博士学位，于2011年在清华大学取得计算机科学学士学位。
她的主要兴趣是NLP模型的鲁棒性和公平性，以及在语言模型中实现系统化的泛化。
Tengyu Ma（马腾宇）是斯坦福大学计算机科学和统计学的助理教授。
他的研究兴趣广泛，包括机器学习、算法及其理论方面的课题，如深度学习、（深度）强化学习、预训练/基础模型、鲁棒性、非凸优化、分布式优化和高维统计。
Xinyun Chen（陈昕昀）是谷歌大脑的高级研究科学家。
在此之前，她于2022年在加州大学伯克利分校获得计算机科学博士学位，并在上海交通大学ACM班获得计算机科学学士学位。
她的研究兴趣是神经程序合成和对抗性机器学习。
Denny Zhou是谷歌大脑和DeepMind推理团队的创始人和负责人。
研究方向是通过建立和教授大型语言模型（LLMs）来解决人工通用智能（AGI）问题，实现人类水平的推理。
研究突破包括：指令调整（FLAN2）、思维链提示、自洽性解码、最小到最大提示，以及LLMs的涌现特性。
值得一提的是，谷歌首席执行官Sundar Pichai在2022年谷歌I/O大会上介绍了他们的工作。
参考资料：
[![](https://www.zhihu.com/api/v3/account/api/login/qrcode/NDE0NzMwOGUtMzM0/image)](https://www.zhihu.com/api/v3/account/api/login/qrcode/NDE0NzMwOGUtMzM0/image)