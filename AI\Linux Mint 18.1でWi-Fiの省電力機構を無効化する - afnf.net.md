---
Updated: 2024-06-07T09:32
tags:
  - AI->-<PERSON><PERSON>
Created: 2024-06-07T09:32
---
[==Linux==](https://blog.afnf.net/blog/t/Linux) [==Mint==](https://blog.afnf.net/blog/t/Mint) [==Wi-Fi==](https://blog.afnf.net/blog/t/Wi-Fi) ==2017/04/20 23:49==
## ==現象==
==Linux Mint 18.1で、ある程度アイドル時間が続くとWi-Fiが不通となってしまう現象が発生しました。==
## ==環境==
- ==Let's note R5==
- ==Intel Pro Wireless 3945ABG (iwl3945)==
- ==Linux Mint 18.1 MATE 32bit==
    - ==おそらくUbuntu 16.04でも同様==
## ==切り分け==
==有線LANは問題なし。Wi-Fiが不通になった後で、有線LAN経由のVNCでデスクトップを表示すると、Wi-Fiが復活します。==
## ==原因==
==切り分け結果から、パワーマネージメント関連が原因であろうと推測しました。iwconfigの結果は、====`Power Management:on`====となっていて疑わしいです。==
```plain
$ iwconfig
lo        no wireless extensions.
enp4s1    no wireless extensions.
wlp3s0    IEEE 802.11abg  ESSID:"xxxxxxxxxxx"
          Mode:Managed  Frequency:2.422 GHz  Access Point: xx:xx:xx:xx:xx:xx
          Bit Rate=54 Mb/s   Tx-Power=15 dBm
          Retry short limit:7   RTS thr:off   Fragment thr:off
          Power Management:on
          Link Quality=53/70  Signal level=-57 dBm
          Rx invalid nwid:0  Rx invalid crypt:0  Rx invalid frag:0
          Tx excessive retries:0  Invalid misc:1710   Missed beacon:0
```
==一時的な対処としては、====`iwconfig デバイス名 power off`== ==で対応できるようなのですが、ログアウト(?)や時間経過で戻ってしまうようなので、恒久的な対処が必要です。==
## ==上手くいかなかった対処方法2==
==ググって出てきた以下の方法では、正しく適用されませんでした。==
```plain
# does not work
$ sudo bash -c "echo \"/sbin/iwconfig wlp3s0 power off\" > /etc/pm/power.d/wireless"
$ sudo chmod 740 /etc/pm/power.d/wireless
$ sudo reboot
```
## ==上手くいかなかった対処方法3==
==よくよく調べると、正にこの目的のための設定ファイル== ==`default-wifi-powersave-on.conf`== ==が見つかりました。以下は変更前の内容です。==
```plain
# /etc/NetworkManager/conf.d/default-wifi-powersave-on.conf
[connection]
wifi.powersave = 3
```
[==ここの情報==](https://people.freedesktop.org/~lkundrak/nm-docs/nm-settings.html#id-1.2.6.4.31)==によれば、変更前はパワーマネージメント有効ということになります。==
==論理名==
==値==
==意味==
==NM_SETTING_WIRELESS_POWERSAVE_DEFAULT==
==0==
==use the globally configured value==
==NM_SETTING_WIRELESS_POWERSAVE_IGNORE==
==1==
==don't touch currently configure setting==
==NM_SETTING_WIRELESS_POWERSAVE_DISABLE==
==2==
==disable Wi-Fi power saving==
==NM_SETTING_WIRELESS_POWERSAVE_ENABLE==
==3==
==enable Wi-Fi power saving==
==ということで、====`2:無効`====に変更し再起動すると、Wi-Fiの省電力機構が常に無効になりました。==
```plain
$ sudo vim /etc/NetworkManager/conf.d/default-wifi-powersave-on.conf
# wifi.powersave = 3
wifi.powersave = 2
```
==ところがこの対応の後も、現象は解決しませんでした。==
## ==最終的な対処方法==
==なんとなく以下のコマンドを実行したところ、それ以降発生しなくなってしまいました。==
```plain
sudo pm-powersave false
```
==初回起動時に毎回しかけているわけで無く、ただ一度実行しただけ。==
==良く分かりませんでした、という残念な結論。==
[==Linux==](https://blog.afnf.net/blog/t/Linux) [==Mint==](https://blog.afnf.net/blog/t/Mint) [==Wi-Fi==](https://blog.afnf.net/blog/t/Wi-Fi) ==2017/04/20 23:49==