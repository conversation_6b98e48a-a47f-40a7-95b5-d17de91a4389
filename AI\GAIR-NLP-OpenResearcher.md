---
Updated: 2024-08-20T18:04
tags:
  - AI->-<PERSON>on
  - AI->-SearchEngine
  - AI->-Theory
  - AI->Automation
URL: https://github.com/gair-nlp/openresearcher
Created: 2024-08-19T17:22
---
Memo
OpenResearcher: Unleashing AI for Accelerated Scientific Research
📋 Table of Contents
📝 Introduction
🏆 Performance
🚀 Get Started
🛠️ Setup
Install necessary packages:
Install Qdrant vector search engine:
🤖 Supported models
Using API:
Using Opensource LLMs:
Enable Web search:
📊 Process Data to embeddings
Indexing and Saving in Qdrant
📘 Usage
Run the RAG application
📚 Citation
  
# Memo
```JavaScript
It includes
1. how to install and start elasticsearch with docker
2. how to fix GLIBXXC libary not found issue
3. how to install pytorch in Jetson AGX oron
```
```JavaScript
git clone https://github.com/GAIR-NLP/OpenResearcher.git 
conda create -n openresearcher python=3.10 
conda activate openresearcher
cd OpenResearcher
pip install -r requirements.txt
sudo docker pull qdrant/qdrant
sudo docker run -p 6333:6333 -p 6334:6334 \
    -v $(pwd)/qdrant_storage:/qdrant/storage:z \
    qdrant/qdrant
mkdir -p html/base/dir
git clone https://huggingface.co/Alibaba-NLP/gte-large-en-v1.5
git clone https://huggingface.co/BAAI/bge-reranker-v2-m3
git clone https://huggingface.co/naver/efficient-splade-VI-BT-large-doc
\#modify config.py
\#startup this system
conda activate openresearcher
1. start docker
sudo docker run -p 6333:6333 -p 6334:6334 \
    -v $(pwd)/qdrant_storage:/qdrant/storage:z \
    qdrant/qdrant
    
https://www.elastic.co/guide/en/elasticsearch/reference/current/docker.html
a, install cosign <= it is option
https://docs.sigstore.dev/system_config/installation/
sudo go install github.com/sigstore/cosign/v2/cmd/cosign@latest
then copy cosign under root home direct to /usr/bin
b. install elasticsearch as docker
https://www.elastic.co/guide/en/elasticsearch/reference/current/docker.html
docker network create elastic
docker pull docker.elastic.co/elasticsearch/elasticsearch:8.15.0
c.start elasticsearch docker
sudo sysctl -w vm.max_map_count=262144 && echo "vm.max_map_count=262144" | sudo tee -a /etc/sysctl.conf
sudo docker run --name es01 --net elastic -p 9200:9200 -it -m 1GB docker.elastic.co/elasticsearch/elasticsearch:8.15.0
-------------------------------
✅ Elasticsearch security features have been automatically configured!
✅ Authentication is enabled and cluster connections are encrypted.
ℹ️  Password for the elastic user (reset with `bin/elasticsearch-reset-password -u elastic`):
  jKFflI4Peg7yXp=Mbv6s
ℹ️  HTTP CA certificate SHA-256 fingerprint:
  fe9b0e16322e22116fe8b51b7d94d3cd5f38a94c31e6e472b85509b64293fbc9
ℹ️  Configure Kibana to use this cluster:
• Run Kibana and click the configuration link in the terminal when Kibana starts.
• Copy the following enrollment token and paste it into Kibana in your browser (valid for the next 30 minutes):
  eyJ2ZXIiOiI4LjE0LjAiLCJhZHIiOlsiMTcyLjE5LjAuMjo5MjAwIl0sImZnciI6ImZlOWIwZTE2MzIyZTIyMTE2ZmU4YjUxYjdkOTRkM2NkNWYzOGE5NGMzMWU2ZTQ3MmI4NTUwOWI2NDI5M2ZiYz
kiLCJrZXkiOiJxNldkYlpFQmRmeDdFbjlqNUtIWDpHX3NVaDdYZVRiR256UU9aUkk2c0NBIn0=
ℹ️ Configure other nodes to join this cluster:
• Copy the following enrollment token and start new Elasticsearch nodes with `bin/elasticsearch --enrollment-token <token>` (valid for the next 30 minut
es):
  eyJ2ZXIiOiI4LjE0LjAiLCJhZHIiOlsiMTcyLjE5LjAuMjo5MjAwIl0sImZnciI6ImZlOWIwZTE2MzIyZTIyMTE2ZmU4YjUxYjdkOTRkM2NkNWYzOGE5NGMzMWU2ZTQ3MmI4NTUwOWI2NDI5M2ZiYz
kiLCJrZXkiOiJyS1dkYlpFQmRmeDdFbjlqNUtIWDpNM0VYcDRwOVRXaTNobkN6cmc5eFdRIn0=
  If you're running in Docker, copy the enrollment token and run:
  `docker run -e "ENROLLMENT_TOKEN=<token>" docker.elastic.co/elasticsearch/elasticsearch:8.15.0`
export ELASTIC_PASSWORD="jKFflI4Peg7yXp=Mbv6s"
-------------------------------
(openresearcher) ray@jethome:~$ sudo chmod a+r http_ca.crt
(openresearcher) ray@jethome:~$ curl --cacert ./http_ca.crt -u elastic:$ELASTIC_PASSWORD https://localhost:9200
{
  "name" : "11f913b0db8f",
  "cluster_name" : "docker-cluster",
  "cluster_uuid" : "jCA0cslnSZiULCpwSKIrjw",
  "version" : {
    "number" : "8.15.0",
    "build_flavor" : "default",
    "build_type" : "docker",
    "build_hash" : "1a77947f34deddb41af25e6f0ddb8e830159c179",
    "build_date" : "2024-08-05T10:05:34.233336849Z",
    "build_snapshot" : false,
    "lucene_version" : "9.11.1",
    "minimum_wire_compatibility_version" : "7.17.0",
    "minimum_index_compatibility_version" : "7.0.0"
  },
  "tagline" : "You Know, for Search"
}
# As default it need password login, Let's remove it
sudo docker rm es01
sudo docker run -d --name es01 --net elastic -p 9200:9200 -e "discovery.type=single-node" -e "xpack.security.enabled=false" docker.elastic.co/elasticsearch/elasticsearch:8.15.0
curl http://localhost:9200
{
  "name" : "dd41306a2c98",
  "cluster_name" : "docker-cluster",
  "cluster_uuid" : "HALn07mcRwCRvFKT_pSLkQ",
  "version" : {
    "number" : "8.15.0",
    "build_flavor" : "default",
    "build_type" : "docker",
    "build_hash" : "1a77947f34deddb41af25e6f0ddb8e830159c179",
    "build_date" : "2024-08-05T10:05:34.233336849Z",
    "build_snapshot" : false,
    "lucene_version" : "9.11.1",
    "minimum_wire_compatibility_version" : "7.17.0",
    "minimum_index_compatibility_version" : "7.0.0"
  },
  "tagline" : "You Know, for Search"
}

\#remove all Exited docker process
sudo docker rm $(sudo docker ps -a -f status=exited -q)
    
2. run the Qdrant retriever server:
pip install fastapi
pip install accelerate
pip install torch-2.3.0-cp310-cp310-linux_aarch64.whl torchaudio-2.3.0+952ea74-cp310-cp310-linux_aarch64.whl torchvision-0.18.0a0+6043bc2-cp310-cp310-linux_aarc
pip install uvicorn
pip install git+https://github.com/FlagOpen/FlagEmbedding.git
pip install peft
pip install llama-index-llms-openllm

history|grep 'pip install'
python -um utils.async_qdrant_retriever.
3.run the Elastic Search retriever server
python -um utils.async_elasticsearch_retriever
4. Start OpenResearcher UI
 CUDA_VISIBLE_DEVICES=0 streamlit run ui_app.py
```
  
Issues  
```JavaScript
 ImportError: /databank/workspace/miniconda3/envs/openresearcher/bin/../lib/libstdc++.so.6: version GLIBCXX_3.4.30' not found (required by /opt/workspace/miniconda3/envs/openresearcher/lib/python3.10/site-packages/torch/lib/libtorch_python.so)
 strings /databank/workspace/miniconda3/envs/openresearcher/lib/libstdc++.so.6 | grep GLIBCXX
@ we found os the library include it.
dpkg -L libstdc++6
(openresearcher) ray@jethome:/usr/lib$ strings /usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.30|grep GLIBCXX_3.4.30
GLIBCXX_3.4.30
 so we enter
 (openresearcher) ray@jethome:/databank/workspace/miniconda3/envs/openresearcher/lib$ ls -lrt|grep libstdc
-rwxrwxr-x 10 <USER> <GROUP>  3934728 Jun  1  2022 libstdc++.so.6.0.29
lrwxrwxrwx  1 ray ray       19 Aug 20 09:36 libstdc++.so.6 -> libstdc++.so.6.0.29
lrwxrwxrwx  1 ray ray       19 Aug 20 09:36 libstdc++.so -> libstdc++.so.6.0.29
and
(openresearcher) ray@jethome:/databank/workspace/miniconda3/envs/openresearcher/lib$ rm -f libstdc++.so.6
(openresearcher) ray@jethome:/databank/workspace/miniconda3/envs/openresearcher/lib$ ln -sf /usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.30 libstdc++.so.6
(openresearcher) ray@jethome:/databank/workspace/miniconda3/envs/openresearcher/lib$ ls -lrt|grep libstdc  -rwxrwxr-x 10 <USER> <GROUP>  3934728 Jun  1  2022 libstdc++.so.6.0.29
lrwxrwxrwx  1 ray ray       19 Aug 20 09:36 libstdc++.so -> libstdc++.so.6.0.29
lrwxrwxrwx  1 ray ray       46 Aug 20 12:19 libstdc++.so.6 -> /usr/lib/aarch64-linux-gnu/libstdc++.so.6.0.3
```
  
[![](https://opengraph.githubassets.com/a5e18b2e42e06f3a73ba3c5d6ff32f4117d75aab4d31d7525f4c375eb46daffc/GAIR-NLP/OpenResearcher)](https://opengraph.githubassets.com/a5e18b2e42e06f3a73ba3c5d6ff32f4117d75aab4d31d7525f4c375eb46daffc/GAIR-NLP/OpenResearcher)
# ==OpenResearcher: Unleashing AI for Accelerated Scientific Research==
==This is the official repository for OpenResearcher.==
==**Note: This repository is actively maintained and regularly updated to provide the latest features and improvements.**==
## ==📋 Table of Contents==
- [==Introduction==](https://github.com/GAIR-NLP/OpenResearcher#-introduction)
- [==Performance==](https://github.com/GAIR-NLP/OpenResearcher#-performance)
- [==Get started==](https://github.com/GAIR-NLP/OpenResearcher#-get-started)
    - [==Setup==](https://github.com/GAIR-NLP/OpenResearcher#-setup)
    - [==Supported models==](https://github.com/GAIR-NLP/OpenResearcher#-supported-models)
        - [==Using Api==](https://github.com/GAIR-NLP/OpenResearcher#-using-api)
        - [==Using Opensource LLMs==](https://github.com/GAIR-NLP/OpenResearcher#-using-opensource-llms)
    - [==Process Data to embeddings==](https://github.com/GAIR-NLP/OpenResearcher#-process-data-to-embeddings)
    - [==Usage==](https://github.com/GAIR-NLP/OpenResearcher#-usage)
- [==Citation==](https://github.com/GAIR-NLP/OpenResearcher#-citation)
## ==📝 Introduction==
[![](https://github.com/GAIR-NLP/OpenResearcher/raw/main/images/logo.jpg)](https://github.com/GAIR-NLP/OpenResearcher/raw/main/images/logo.jpg)
==Welcome to OpenResearcher, an advanced Scientific Research Assistant designed to provide a helpful answer to a research query.==
==With access to the arXiv corpus, OpenResearcher can provide the latest scientific insights.==
==Explore the frontiers of science with OpenResearcher—where answers await.==
## ==🏆 Performance==
==We release the benchmarking results on various RAG-related systems as a leaderboard.==
==Models==
==Correctness==
==Richness==
==Relevance==
==(Compared to== [==Perplexity==](https://perplexity.ai/)==)==
==Win==
==Tie==
==Lose==
==Win==
==Tie==
==Lose==
==Win==
==Tie==
==Lose==
[==iAsk.Ai==](https://iask.ai/)
==2==
==16==
==12==
==12==
==6==
==12==
==2==
==8==
==20==
[==You.com==](https://you.com/)
==3==
==21==
==6==
==9==
==5==
==16==
==4==
==13==
==13==
[==Phind==](https://www.phind.com/)
==2==
==26==
==2==
==15==
==7==
==8==
==5==
==13==
==12==
==Naive RAG==
==1==
==22==
==7==
==14==
==8==
==8==
==5==
==16==
==9==
==OpenResearcher==
==**10**==
==13==
==7==
==**25**==
==4==
==1==
==**15**==
==13==
==2==
==We used human experts to evaluate the responses from various RAG systems. If one answer was significantly better than another, it was judged as a win for the former and a lose for the latter. If the two answers were similar, it was considered a tie.==
==Models==
==Richness==
==Relevance==
==(Compared to== [==Perplexity==](https://perplexity.ai/)==)==
==Win==
==Tie==
==Lose==
==Win==
==Tie==
==Lose==
[==iAsk.Ai==](https://iask.ai/)
==42==
==0==
==67==
==38==
==0==
==71==
[==You.com==](https://you.com/)
==15==
==0==
==94==
==16==
==0==
==93==
[==Phind==](https://www.phind.com/)
==52==
==1==
==56==
==54==
==0==
==55==
==Naive RAG==
==41==
==1==
==67==
==57==
==0==
==52==
==OpenResearcher==
==**62**==
==2==
==45==
==**74**==
==0==
==35==
==GPT-4 Preference Results compared with Perplexity AI outcome.==
## ==🚀 Get Started==
### ==🛠️ Setup==
### ==Install necessary packages:==
==To begin using OpenResearcher, you need to install the required dependencies. You can do this by running the following command:==
==git clone https://github.com/GAIR-NLP/OpenResearcher.git  
conda create -n openresearcher python=3.10  
conda activate openresearcher  
cd OpenResearcher  
pip install -r requirements.txt  
==
### ==Install Qdrant vector search engine:==
==First, download the latest Qdrant image from Dockerhub:==
==docker pull qdrant/qdrant==
==Then, run the service:==
==docker run -p 6333:6333 -p 6334:6334 \  
-v $(pwd)/qdrant_storage:/qdrant/storage:z \  
qdrant/qdrant  
==
==For more Qdrant installation details, you can follow this== [==link==](https://qdrant.tech/documentation/quickstart/)==.==
### ==🤖 Supported models==
==OpenResearcher currently supports API models from== [==OpenAI==](https://openai.com/)==,== [==Deepseek==](https://www.deepseek.com/)==, and== [==Aliyun==](https://www.aliyun.com/)==, as well as most== [==huggingface==](https://huggingface.co/) ==models supported by vllm.==
### ==Using API:==
==Modify the API and base URL values in the config.py file located in the root directory to use large language model service platforms that support the OpenAI interface==
==For example, if you use== [==Deepseek==](https://www.deepseek.com/) ==as an API provider, and then modify the following value in== ==`config.py`====::==
==...  
openai_api_base_url = "https://api.deepseek.com/v1"  
openai_api_key = "api key here"  
...  
==
### ==Using Opensource LLMs:==
==Please use== [==vllm==](https://github.com/vllm-project/vllm) ==to set up the API server for open-source LLMs. For example, use the following command to deploy a Llama 3 70B hosted on HuggingFace:==
==python -m vllm.entrypoints.openai.api_server \  
--model meta-llama/Meta-Llama-3-70B-Instruct \  
--tensor-parallel-size 8 \  
--dtype auto \  
--api-key sk-dummy \  
--gpu-memory-utilization 0.9 \  
--port 5000  
==
==Then we can initialize the chat-llm with== ==`config.py`====:==
==...  
openai_api_base_url = "http://localhost:5000/v1"  
openai_api_key = "sk-dummy"  
...  
==
### ==Enable Web search:==
==We currently support== [==Bing Search==](https://www.microsoft.com/en-us/bing/apis) ==in OpenResearcher. Modify the following value in== ==`config.py`====:==
==...  
bing_search_key = "api key here"  
bing_search_end_point = "https://api.bing.microsoft.com/"  
...  
==
### ==📊 Process Data to embeddings==
### ==Indexing and Saving in Qdrant==
==**1. Download arXiv data (html file) and metadata into the /data**==
==​ arXiv data refers to== [==https://info.arxiv.org/help/bulk_data/index.html==](https://info.arxiv.org/help/bulk_data/index.html)
==​ Metadata refers to== [==https://www.kaggle.com/datasets/Cornell-University/arxiv==](https://www.kaggle.com/datasets/Cornell-University/arxiv)
==The directory of== ==`data`====is formatted as follows:==
```plain
   - data/
     - 2401/  # pub date   
       - 2401.00001/  # paper id    
         - doc.html   # paper content 
       - 2401.00002/
         - doc.html
     - 2402/
    ...
     -arxiv-metadata-oai-snapshot.jsonl   # metadata        
```
==**2. Parse the html data**==
==CUDA_VISIBLE_DEVICES=0 python -um connector.html_parsing --target_dir /path/to/target/directory --start_index 0 --end_index -1 \  
--meta_data_path /path/to/metadata/file  
==
==**Parameter explanation:**==
==​== ==**target_dir:**== ==process the 'target_dir' papers==
==​== ==**start_index,end_index:**== ==papers in directory from 'start_index' to 'end_index' will be processed==
==​== ==**meta_data_path:**== ==metadata saved path==
### ==📘 Usage==
### ==Run the RAG application==
==First, run the Qdrant retriever server:==
==python -um utils.async_qdrant_retriever==
==Then run the Elastic Search retriever server:==
==python -um utils.async_elasticsearch_retriever==
==Then you can run the OpenResearcher system by following the command:==
==CUDA_VISIBLE_DEVICES=0 streamlit run ui_app.py==
## ==📚 Citation==
==If this work is helpful, please kindly cite as:==
```plain
@misc{zheng2024openresearcherunleashingaiaccelerated,
      title={OpenResearcher: Unleashing AI for Accelerated Scientific Research}, 
      author={Yuxiang Zheng and Shichao Sun and Lin Qiu and Dongyu Ru and Cheng Jiayang and Xuefeng Li and Jifan Lin and Binjie Wang and Yun Luo and Renjie Pan and Yang Xu and Qingkai Min and Zizhao Zhang and Yiwen Wang and Wenjie Li and Pengfei Liu},
      year={2024},
      eprint={2408.06941},
      archivePrefix={arXiv},
      primaryClass={cs.IR},
      url={https://arxiv.org/abs/2408.06941}, 
}
```