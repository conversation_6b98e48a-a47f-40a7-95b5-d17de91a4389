---
DocFlag:
  - ToBeTested
Updated: 2024-08-09T19:08
tags:
  - AI-->-WebDev
  - AI->-DevPlatform
  - AI->-Infra
  - AI->-Model
  - AI->-Tools
URL: https://github.blog/news-insights/product-news/introducing-github-models/
Created: 2024-08-02T08:05
---
# 实验笔记
```JavaScript
# get github token
https://github.com/settings/tokens
generate one token: ****************************************
You do not need to give any permissions to the token. Note that the token will be sent to a Microsoft service.
\#install library
pip install azure-ai-inference
\#sample code
import os
from azure.ai.inference import Chat<PERSON><PERSON>ple<PERSON>Client
from azure.ai.inference.models import SystemMessage, UserMessage
from azure.core.credentials import AzureKeyCredential
token = os.getenv("GITHUB_TOKEN", "****************************************")
endpoint = "https://models.inference.ai.azure.com"
model_name = "meta-llama-3-8b-instruct"
client = ChatCompletionsClient(
    endpoint=endpoint,
    credential=AzureKeyCredential(token),
)
response = client.complete(
    stream=True,
    messages=[
        SystemMessage(content="You are a helpful assistant."),
        UserMessage(content="Give me 5 good reasons why I should exercise every day."),
    ],
    model=model_name,
)
for update in response:
    if update.choices:
        print(update.choices[0].delta.content or "", end="")
client.close()
```
```JavaScript
import os
import json
from openai import OpenAI
import inspect
def execute_tool_call(tool_call, tools_map):
    name = tool_call.function.name
    args = json.loads(tool_call.function.arguments)
    
    print(color("Assistant:", "yellow"), color(f"{name}({args})", "magenta"))
    
    # call corresponding function with provided arguments
    return tools_map[name](**args)
           
           
def color(text, color_name):
    color_codes = {
        "blue": "\033[94m",
        "yellow": "\033[93m",
        "magenta": "\033[95m",
        # Add more colors as needed
    }
    reset_code = "\033[0m"
    
    if color_name in color_codes:
        return f"{color_codes[color_name]}{text}{reset_code}"
    else:
        return text  # Return uncolored text if color not found
    
def function_to_schema(func) -> dict:
    type_map = {
        str: "string",
        int: "integer",
        float: "number",
        bool: "boolean",
        list: "array",
        dict: "object",
        type(None): "null",
    }
    try:
        signature = inspect.signature(func)
    except ValueError as e:
        raise ValueError(
            f"Failed to get signature for function {func.__name__}: {str(e)}"
        )
    parameters = {}
    for param in signature.parameters.values():
        try:
            param_type = type_map.get(param.annotation, "string")
        except KeyError as e:
            raise KeyError(
                f"Unknown type annotation {param.annotation} for parameter {param.name}: {str(e)}"
            )
        parameters[param.name] = {"type": param_type}
    required = [
        param.name
        for param in signature.parameters.values()
        if param.default == inspect._empty
    ]
    
    return {
        "type": "function",
        "function": {
            "name": func.__name__,
            "description": func.__doc__ or "",
            "parameters": {
                "type": "object",
                "properties": parameters,
                "required": required,
            },
        },
    }
def run_full_turn(system_message, tools, messages):
    num_init_messages = len(messages)
    messages = messages.copy()
    
    while True:
        
        # turn python functions into tools and save a reverse map
        tool_schemas = [function_to_schema(tool) for tool in tools]
        tools_map = {tool.__name__: tool for tool in tools}
        response = client.chat.completions.create(
            model=model_name,
            messages=[{"role": "system", "content": system_message}] + messages,
            tools=tool_schemas or None,
        )
        message = response.choices[0].message
        messages.append(message)
        if message.content:
            print(color("Assistant:", "yellow"), message.content)
        if not message.tool_calls:
            break
        
        # === 2. handle tool calls ===
        for tool_call in message.tool_calls:
            result = execute_tool_call(tool_call, tools_map)
            
            result_message = {
                "role": "tool",
                "tool_call_id": tool_call.id,
                "content": result,
            }
            messages.append(result_message)
            
    # === 3. return new messages ===
    return messages[num_init_messages:]
token = os.getenv("GITHUB_TOKEN", "****************************************")
endpoint = "https://models.inference.ai.azure.com"
model_name = "gpt-4o"
# Define a function that returns flight information between two cities (mock implementation)
def get_flight_info(origin_city: str, destination_city: str):
    if origin_city == "Seattle" and destination_city == "Miami":
        return json.dumps({
            "airline": "Delta",
            "flight_number": "DL123",
            "flight_date": "May 7th, 2024",
            "flight_time": "10:00AM"})
    return json.dumps({"error": "No flights found between the cities"})
tools = [get_flight_info]
client = OpenAI(
    base_url=endpoint,
    api_key=token,
)
# messages=[
#     {"role": "system", "content": "You an assistant that helps users find flight information."},
#     {"role": "user", "content": "I'm interested in going to Miami. What is the next flight there from Seattle?"},
# ]
messages = []
system_message = ("You an assistant that helps users find flight information.")
user_input = "I'm interested in going to Miami. What is the next flight there from Seattle?"
messages.append({"role": "user", "content": user_input})
new_messages = run_full_turn(system_message, tools, messages)
messages.extend(new_messages)
# Print messages with better formatting
print("\nConversation:")
for message in messages:
    if isinstance(message, dict):
        role = message["role"].capitalize()
        content = message["content"]
    else:  # Assuming it's a ChatCompletionMessage object
        role = message.role.capitalize()
        content = message.content
    
    if role == "Tool":
        print(f"\n{role} Response:")
        try:
            # Try to parse and pretty-print JSON content
            parsed_content = json.loads(content)
            print(json.dumps(parsed_content, indent=2))
        except json.JSONDecodeError:
            # If not JSON, print as is
            print(content)
    else:
        print(f"\n{role}: {content}")
print("\n")
```
  
  
[![](https://github.blog/wp-content/uploads/2024/07/github-models-header.png?w=1600)](https://github.blog/wp-content/uploads/2024/07/github-models-header.png?w=1600)
We believe every developer can be an AI engineer with the right tools and training. From playground to coding with the model in Codespaces to production deployment via Azure, GitHub Models shows you how simple it can be. Sign up for the limited public beta
[HERE](https://gh.io/models)
.
---
From the early days of the home computer, the dominant mode of creation for developers has long been building, customizing, and deploying software with code. Today, in the age of AI, a secondary and equally important mode of creation is rapidly emerging: the ability to leverage machine learning models. Increasingly, developers are building generative AI applications where the full stack contains backend and frontend code plus one or more models. But a vast segment of developers still lack easy access to open and closed models. This changes today.
**We are launching GitHub Models, enabling our more than 100 million developers to become AI engineers and build with industry-leading AI models.**
From Llama 3.1, to GPT-4o and GPT-4o mini, to Phi 3 or Mistral Large 2, you can access each model via a built-in playground that lets you test different prompts and model parameters, for free, right in GitHub. And if you like what you’re seeing on the playground, we’ve created a glide path to bring the models to your developer environment in Codespaces and VS Code. And once you are ready to go to production, Azure AI offers built-in responsible AI, enterprise-grade security & data privacy, and global availability, with provisioned throughput and availability in over 25 Azure regions for some models. It’s never been easier to develop and run your AI application.
## The joy begins in the model playground on GitHub
For most of us, learning to be a developer didn’t happen on a linear path in the classroom. It took practicing, playing around, and learning through experimentation. The same is true today for AI models. In the new interactive model playground, students, hobbyists, startups, and more can explore the most popular private _and_ open models from Meta, Mistral, Azure OpenAI Service, Microsoft, and others with just a few clicks and keystrokes. Experiment, compare, test, and deploy AI applications right where you manage your source code.
In alignment with GitHub and Microsoft’s continued commitment to privacy and security, no prompts or outputs in GitHub Models will be shared with model providers, nor used to train or improve the models.
[![](https://github.blog/wp-content/uploads/2024/07/models-marketplace.jpg?w=1600)](https://github.blog/wp-content/uploads/2024/07/models-marketplace.jpg?w=1600)
Professor David J. Malan will be putting GitHub Models to the test in Harvard’s CS50 this fall, to enable students to experiment with AI all the more easily.
---
## Test and compare different models
Every piece of software is unique. And likewise, every model is unique in its capabilities, performance, and cost. Mistral offers low latency, while GPT-4o is excellent at building multimodal applications that might demand audio, vision, and text in real time. Some advanced scenarios might require the integration of different modes, such as an embeddings model for [Retrieval Augmented Generation (RAG).](https://github.blog/2024-04-04-what-is-retrieval-augmented-generation-and-what-does-it-do-for-generative-ai/)
With the suite of models, developers will have all the options they need to stay in the flow, experiment more, and learn faster than ever before. And this is just the first wave. In the months ahead, as we approach the general availability of GitHub Models, we will continue to add more language, vision, and other models to our platform.
[![](https://github.blog/wp-content/uploads/2024/07/six-models.png?w=1600)](https://github.blog/wp-content/uploads/2024/07/six-models.png?w=1600)
## Spin up Codespaces to bring your ideas to life
The fun doesn’t just stay in the playground. With the power of Codespaces, we’ve created a zero-friction path for you to experiment with the model inference code before dropping it into your own project. With sample code for a variety of languages and frameworks of all types ready to go, you can try out various scenarios without ever hitting “works on my machine” problems.
Then, once you’re ready, it’s a breeze to get things running in your project. Use the knowledge you’ve gained from the playground and Codespaces to set up a prototype or proof-of-concept within your own applications. Run prompt evals in GitHub Actions with a series of JSON files that you just pipe in the GitHub Models command within the [GitHub CLI](https://cli.github.com/). Or you can leverage GitHub Models to build a [GitHub Copilot Extension](https://github.blog/2024-05-21-introducing-github-copilot-extensions/), extending GitHub’s platform ecosystem for every stage of software development. And finally, go to production with Azure AI by replacing your GitHub personal access token with an Azure subscription and credential.
## The creator network for the age of AI
From the creation of AI through open source collaboration, to the creation of software with the power of AI, to enabling the rise of the AI engineer with GitHub Models – GitHub is the creator network for the age of AI.
The path to artificial general intelligence (AGI) will not be built without the source code and collaboration of the interconnected community on GitHub. Just in the last year, more than 100K generative AI projects were created on GitHub.
GitHub Copilot is foundationally changing the speed of software production, already writing nearly 50% of code in files where it’s enabled. With [GitHub Copilot Workspace](https://github.blog/news-insights/product-news/github-copilot-workspace/), we envision a world where millions of novice, hobbyist, and professional developers alike can code with entirely human language.
And now with GitHub Models, more than 100 million developers can access and experiment with new AI models where they already manage their source code, issues, pull requests, workflows, and repositories – directly on GitHub.
In the years ahead, we will continue to democratize access to AI technologies to generate a groundswell of one billion developers. By doing so, we will enable 10% of the world’s population to build and advance breakthroughs that will accelerate human progress for all.