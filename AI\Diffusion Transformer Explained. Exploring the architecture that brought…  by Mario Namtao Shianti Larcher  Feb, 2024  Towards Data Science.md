---
Updated: 2024-03-02T10:17
tags:
  - AI->-Theory
URL: https://towardsdatascience.com/diffusion-transformer-explained-e603c4770f7e
Created: 2024-03-02T08:03
---
# Diffusion Transformer Explained
## Exploring the architecture that brought transformers into image generation
[![](https://miro.medium.com/v2/resize:fill:88:88/1*<EMAIL>)](https://miro.medium.com/v2/resize:fill:88:88/1*<EMAIL>)
[![](https://miro.medium.com/v2/resize:fill:48:48/1*CJe3891yB1A1mzMdqemkdg.jpeg)](https://miro.medium.com/v2/resize:fill:48:48/1*CJe3891yB1A1mzMdqemkdg.jpeg)
[<PERSON>](https://mnslarcher.medium.com/?source=post_page-----e603c4770f7e--------------------------------)
·
[Follow](https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fsubscribe%2Fuser%2Fcd2b72f39ad4&operation=register&redirect=https%3A%2F%2Ftowardsdatascience.com%2Fdiffusion-transformer-explained-e603c4770f7e&user=Mario+Namtao+Shianti+Larcher&userId=cd2b72f39ad4&source=post_page-cd2b72f39ad4----e603c4770f7e---------------------post_header-----------)
Published in
[Towards Data Science](https://towardsdatascience.com/?source=post_page-----e603c4770f7e--------------------------------)
·
12 min read
·
2 days ago
Image generated with DALL·E.
[![](https://miro.medium.com/v2/resize:fit:700/1*hWXJ--CkWDFeF8owOW15kg.png)](https://miro.medium.com/v2/resize:fit:700/1*hWXJ--CkWDFeF8owOW15kg.png)
# Introduction
After shaking up NLP and moving into computer vision with the Vision Transformer (ViT) and its successors, transformers are now entering the field of image generation. They are gradually becoming an alternative to the U-Net, the convolutional architecture upon which all the early diffusion models were built. This article looks into the **Diffusion Transformer** (**DiT**), introduced by William Peebles and Saining Xie in their paper “**Scalable Diffusion Models with Transformers**.”
## [Scalable Diffusion Models with Transformers](https://arxiv.org/abs/2212.09748?source=post_page-----e603c4770f7e--------------------------------)
### [We explore a new class of diffusion models based on the transformer architecture. We train latent diffusion models of…](https://arxiv.org/abs/2212.09748?source=post_page-----e603c4770f7e--------------------------------)
[arxiv.org](https://arxiv.org/abs/2212.09748?source=post_page-----e603c4770f7e--------------------------------)
DiT has influenced the development of other transformer-based diffusion models like [PIXART-α](https://pixart-alpha.github.io/), [Sora](https://openai.com/sora) (OpenAI’s astonishing text-to-video model), and, as I write this article, [Stable Diffusion 3](https://stability.ai/news/stable-diffusion-3). Let’s start exploring this emerging class of architectures that are contributing to the evolution of diffusion models.
# Preliminaries
Given that this is an advanced topic, I’ll have to assume a certain familiarity with recurring concepts in AI and, in particular, in image generation. If you’re already familiar with this field, this section will help refresh these concepts, providing you with further references for a deeper understanding.
If you want an extensive overview of this world before reading this article, I recommend reading my previous article below, where I cover many diffusion models and related techniques, some of which we’ll revisit here.
## [Comparing and Explaining Diffusion Models in HuggingFace Diffusers](https://towardsdatascience.com/comparing-and-explaining-diffusion-models-in-huggingface-diffusers-a83d64348d90?source=post_page-----e603c4770f7e--------------------------------)
### [DDPM, Stable Diffusion, DALL·E-2, Imagen, Kandinsky 2, SDEdit, ControlNet, InstructPix2Pix, and more](https://towardsdatascience.com/comparing-and-explaining-diffusion-models-in-huggingface-diffusers-a83d64348d90?source=post_page-----e603c4770f7e--------------------------------)
[towardsdatascience.com](https://towardsdatascience.com/comparing-and-explaining-diffusion-models-in-huggingface-diffusers-a83d64348d90?source=post_page-----e603c4770f7e--------------------------------)
## Diffusion formulation
At an intuitive level, **diffusion models** function by first taking images, introducing noise (usually Gaussian), and then training a neural network to reverse this noise-adding…