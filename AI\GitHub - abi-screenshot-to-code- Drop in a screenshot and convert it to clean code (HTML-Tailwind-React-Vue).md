---
Updated: 2024-03-03T11:27
tags:
  - AI->-DevPlatform
  - AI->-Programming
  - AI->Automation
URL: https://github.com/abi/screenshot-to-code
Created: 2024-03-03T07:52
---
# screenshot-to-code
This simple app converts a screenshot to code (HTML/Tailwind CSS, or React or Bootstrap or Vue). It uses GPT-4 Vision to generate the code and DALL-E 3 to generate similar-looking images. You can now also enter a URL to clone a live website!
Youtube.Clone.mp4
See the [Examples](https://github.com/abi/screenshot-to-code#-examples) section below for more demos.
## 🚀 Try It Out!
🆕 [Try it here](https://screenshottocode.com/) (bring your own OpenAI key - **your key must have access to GPT-4 Vision. See** [**FAQ**](https://github.com/abi/screenshot-to-code#%EF%B8%8F-faqs) **section below for details**). Or see [Getting Started](https://github.com/abi/screenshot-to-code#-getting-started) below for local install instructions.
## 🌟 Recent Updates
- Dec 11 - Start a new project from existing code (allows you to come back to an older project)
- Dec 7 - 🔥 🔥 🔥 View a history of your edits, and branch off them
- Nov 30 - Dark mode, output code in Ionic (thanks [@dialmedu](https://github.com/dialmedu)), set OpenAI base URL
- Nov 28 - 🔥 🔥 🔥 Customize your stack: React or Bootstrap or TailwindCSS
- Nov 23 - Send in a screenshot of the current replicated version (sometimes improves quality of subsequent generations)
- Nov 21 - Edit code in the code editor and preview changes live thanks to [@clean99](https://github.com/clean99)
- Nov 20 - Paste in a URL to screenshot and clone (requires [ScreenshotOne free API key](https://screenshotone.com/?via=screenshot-to-code))
- Nov 19 - Support for dark/light code editor theme - thanks [@kachbit](https://github.com/kachbit)
- Nov 16 - Added a setting to disable DALL-E image generation if you don't need that
- Nov 16 - View code directly within the app
- Nov 15 - You can now instruct the AI to update the code as you wish. It is helpful if the AI messed up some styles or missed a section.
## 🛠 Getting Started
The app has a React/Vite frontend and a FastAPI backend. You will need an OpenAI API key with access to the GPT-4 Vision API.
Run the backend (I use Poetry for package management - `pip install poetry` if you don't have it):
```Plain
cd backend
echo "OPENAI_API_KEY=sk-your-key" > .env
poetry install
poetry shell
poetry run uvicorn main:app --reload --port 7001
```
Run the frontend:
```Plain
cd frontend
yarn
yarn dev
```
Open [http://localhost:5173](http://localhost:5173/) to use the app.
If you prefer to run the backend on a different port, update VITE_WS_BACKEND_URL in `frontend/.env.local`
For debugging purposes, if you don't want to waste GPT4-Vision credits, you can run the backend in mock mode (which streams a pre-recorded response):
```Plain
MOCK=true poetry run uvicorn main:app --reload --port 7001
```
## Configuration
- You can configure the OpenAI base URL if you need to use a proxy: Set OPENAI_BASE_URL in the `backend/.env` or directly in the UI in the settings dialog
## Docker
If you have Docker installed on your system, in the root directory, run:
```Plain
echo "OPENAI_API_KEY=sk-your-key" > .env
docker-compose up -d --build
```
The app will be up and running at [http://localhost:5173](http://localhost:5173/). Note that you can't develop the application with this setup as the file changes won't trigger a rebuild.
## 🙋‍♂️ FAQs
- **I'm running into an error when setting up the backend. How can I fix it?** [Try this](https://github.com/abi/screenshot-to-code/issues/3#issuecomment-1814777959). If that still doesn't work, open an issue.
- **How do I get an OpenAI API key?** See [https://github.com/abi/screenshot-to-code/blob/main/Troubleshooting.md](https://github.com/abi/screenshot-to-code/blob/main/Troubleshooting.md)
- **How can I provide feedback?** For feedback, feature requests and bug reports, open an issue or ping me on [Twitter](https://twitter.com/_abi_).
## 📚 Examples
**NYTimes**
|Original|Replica|Files|
|---|---|---|
|[[AI/GitHub - abi-screenshot-to-code- Drop in a screenshot and convert it to clean code (HTML-Tailwind-React-Vue)/Untitled Database/Untitled\|Untitled]]||[![https://private-user-images.githubusercontent.com/23818/284352098-3b644dfa-9ca6-4148-84a7-3405b6671922.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MDkzNTcwMzksIm5iZiI6MTcwOTM1NjczOSwicGF0aCI6Ii8yMzgxOC8yODQzNTIwOTgtM2I2NDRkZmEtOWNhNi00MTQ4LTg0YTctMzQwNWI2NjcxOTIyLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDAzMDIlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwMzAyVDA1MTg1OVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPThhMWQwYjc5Mjc1YTg3NTdkOGJkNjU2MzYzYWVlODZiNWJlMzYxNTJmMmUxYTI5N2Y4OTc4Nzg3OTgxZGMwODImWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.vC5x6xu1DwlwaJOu2lUciCq3IKaBQfof2f-z_k69NOQ](https://private-user-images.githubusercontent.com/23818/284352098-3b644dfa-9ca6-4148-84a7-3405b6671922.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MDkzNTcwMzksIm5iZiI6MTcwOTM1NjczOSwicGF0aCI6Ii8yMzgxOC8yODQzNTIwOTgtM2I2NDRkZmEtOWNhNi00MTQ4LTg0YTctMzQwNWI2NjcxOTIyLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDAzMDIlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwMzAyVDA1MTg1OVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPThhMWQwYjc5Mjc1YTg3NTdkOGJkNjU2MzYzYWVlODZiNWJlMzYxNTJmMmUxYTI5N2Y4OTc4Nzg3OTgxZGMwODImWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.vC5x6xu1DwlwaJOu2lUciCq3IKaBQfof2f-z_k69NOQ)](https://private-user-images.githubusercontent.com/23818/284352098-3b644dfa-9ca6-4148-84a7-3405b6671922.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MDkzNTcwMzksIm5iZiI6MTcwOTM1NjczOSwicGF0aCI6Ii8yMzgxOC8yODQzNTIwOTgtM2I2NDRkZmEtOWNhNi00MTQ4LTg0YTctMzQwNWI2NjcxOTIyLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDAzMDIlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwMzAyVDA1MTg1OVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPThhMWQwYjc5Mjc1YTg3NTdkOGJkNjU2MzYzYWVlODZiNWJlMzYxNTJmMmUxYTI5N2Y4OTc4Nzg3OTgxZGMwODImWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.vC5x6xu1DwlwaJOu2lUciCq3IKaBQfof2f-z_k69NOQ)[![https://private-user-images.githubusercontent.com/23818/284352465-26201c9f-1a28-4f35-a3b1-1f04e2b8ce2a.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MDkzNTcwMzksIm5iZiI6MTcwOTM1NjczOSwicGF0aCI6Ii8yMzgxOC8yODQzNTI0NjUtMjYyMDFjOWYtMWEyOC00ZjM1LWEzYjEtMWYwNGUyYjhjZTJhLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDAzMDIlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwMzAyVDA1MTg1OVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTExYjQ1ZTM0MGI0YzM5YmU0NDkyMDFiMmNlNGY0ZDNkYzM3YTY2NGFjYzQ4MDI4NTU0MGE2MDIxNTAyMDU0ZWQmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.vd_ZZefgJwTf4G66IAWX13sYTD6YFZcC_3uKN0eHZos](https://private-user-images.githubusercontent.com/23818/284352465-26201c9f-1a28-4f35-a3b1-1f04e2b8ce2a.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MDkzNTcwMzksIm5iZiI6MTcwOTM1NjczOSwicGF0aCI6Ii8yMzgxOC8yODQzNTI0NjUtMjYyMDFjOWYtMWEyOC00ZjM1LWEzYjEtMWYwNGUyYjhjZTJhLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDAzMDIlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwMzAyVDA1MTg1OVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTExYjQ1ZTM0MGI0YzM5YmU0NDkyMDFiMmNlNGY0ZDNkYzM3YTY2NGFjYzQ4MDI4NTU0MGE2MDIxNTAyMDU0ZWQmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.vd_ZZefgJwTf4G66IAWX13sYTD6YFZcC_3uKN0eHZos)](https://private-user-images.githubusercontent.com/23818/284352465-26201c9f-1a28-4f35-a3b1-1f04e2b8ce2a.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MDkzNTcwMzksIm5iZiI6MTcwOTM1NjczOSwicGF0aCI6Ii8yMzgxOC8yODQzNTI0NjUtMjYyMDFjOWYtMWEyOC00ZjM1LWEzYjEtMWYwNGUyYjhjZTJhLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDAzMDIlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwMzAyVDA1MTg1OVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTExYjQ1ZTM0MGI0YzM5YmU0NDkyMDFiMmNlNGY0ZDNkYzM3YTY2NGFjYzQ4MDI4NTU0MGE2MDIxNTAyMDU0ZWQmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.vd_ZZefgJwTf4G66IAWX13sYTD6YFZcC_3uKN0eHZos)|
  
  
**Instagram page (with not Taylor Swift pics)**
instagram.taylor.swift.take.1.mp4
**Hacker News** but it gets the colors wrong at first so we nudge it
hacker.news.with.edits.mp4
## 🌍 Hosted Version
🆕 [Try it here](https://screenshottocode.com/) (bring your own OpenAI key - **your key must have access to GPT-4 Vision. See** [**FAQ**](https://github.com/abi/screenshot-to-code#%EF%B8%8F-faqs) **section for details**). Or see [Getting Started](https://github.com/abi/screenshot-to-code#-getting-started) for local install instructions.
[![](https://camo.githubusercontent.com/12f516d86d600c89a6abd2326256045c27325ad7c8532c0d36772965a4923be0/68747470733a2f2f7777772e6275796d6561636f666665652e636f6d2f6173736574732f696d672f637573746f6d5f696d616765732f6f72616e67655f696d672e706e67)](https://camo.githubusercontent.com/12f516d86d600c89a6abd2326256045c27325ad7c8532c0d36772965a4923be0/68747470733a2f2f7777772e6275796d6561636f666665652e636f6d2f6173736574732f696d672f637573746f6d5f696d616765732f6f72616e67655f696d672e706e67)