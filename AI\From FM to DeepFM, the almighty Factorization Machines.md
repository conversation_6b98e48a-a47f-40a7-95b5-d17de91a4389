---
Updated: 2024-05-24T13:04
tags:
  - AI->-Recommend
URL: https://happystrongcoder.substack.com/p/from-fm-to-deepfm-the-almighty-factorization
Created: 2024-05-24T13:03
---
[![](https://substackcdn.com/image/fetch/w_1200,h_600,c_fill,f_jpg,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Faa783617-ad21-458b-b16e-9bb9fa77fc90_1171x517.png)](https://substackcdn.com/image/fetch/w_1200,h_600,c_fill,f_jpg,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Faa783617-ad21-458b-b16e-9bb9fa77fc90_1171x517.png)
==In this post, let’s revisit the classic ranking algorithm Factorization Machines and the successor DeepFM in the Deep Learning era.==
1. ==The idea of Factorization Machines (FMs from now on) is to learn a polynomial kernel by representing high-order terms as a low-dimensional inner product of latent factor vectors. In other words, learning feature interactions using similarity of embeddings==[==1==](https://happystrongcoder.substack.com/p/from-fm-to-deepfm-the-almighty-factorization#footnote-1-130083204)
2. ==FM is very efficient at learning second-order feature interactions. And besides ranking, it can also be transformed into a Candidate Retriever (CR from now on) which makes it still useful in the Deep Learning dominated recommender system today==
3. ==The DeepFM is an end-to-end model that combines the power of FM for recommendation and deep learning for feature learning in a new neural network architecture to learn both low and high-order feature interactions==[==2==](https://happystrongcoder.substack.com/p/from-fm-to-deepfm-the-almighty-factorization#footnote-2-130083204)
4. ==DeepFM has a shared input to its “wide” and “deep” parts, with no need for feature engineering besides raw features which makes it effortless compared to the W&D model==
==These two algorithms are super popular in the recommender system and let’s follow our tradition, ignore trivial details in the paper and focus on the main ideas.==
==Be a happy and strong coder is a reader-supported publication. To receive new posts and support my work, consider becoming a free or paid subscriber.==
==Recall the linear regression model, here y is the target, w0 is the bias, wi is the i-th weights, xi is the i-th feature value.==
==The biggest disadvantage of the model is, if we want to capture the 2nd order feature interaction, like a job SDE and a location in San Francisco, we have to do this manually. Adding a weight like:==
==This is a pretty heavy workload. FM solves it by introducing a generalized order 2 polynomial.==
==And the weight of the 2nd order feature can be calculated by the dot product of 2 vectors, suppose the length of each vector is k.==
==Recall the idea of the Matrix Factorization (MF) model, actually the FM shares the same idea. The only difference is the features are not limited to the user and item IDs.==[==3==](https://happystrongcoder.substack.com/p/from-fm-to-deepfm-the-almighty-factorization#footnote-3-130083204)
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Faa783617-ad21-458b-b16e-9bb9fa77fc90_1171x517.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Faa783617-ad21-458b-b16e-9bb9fa77fc90_1171x517.png)
==In FM, we will learn latent vector pairs for each user and item interaction feature.==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F047ea4bc-9025-430f-9b5d-aeffbe887722_1226x538.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F047ea4bc-9025-430f-9b5d-aeffbe887722_1226x538.png)
==So we can say that:==
==**FM is a generalization of MF and MF is a special case of FM**==
==The original formula basically means the time complexity of the FM model is O(k*n^2). This is quite slow in terms of trillions of features used in industry.==
==But fortunately, it can be optimized to an== ==**O(k*n)**== ==complexity, here the deduction is:==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Ff2f4829c-de5e-43d3-ad3f-9fb5f2f8eac7_1065x541.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Ff2f4829c-de5e-43d3-ad3f-9fb5f2f8eac7_1065x541.png)
==I won’t go through too many math details here, you can refer to this== [==post==](https://zhuanlan.zhihu.com/p/58160982) ==for the detailed explanation.==
==This transformation is critical, all of our implementation will be based on it.==
==There are many implementations for FM, you can find some examples from== [==Microsoft==](https://github.com/microsoft/recommenders/blob/main/examples/02_model_hybrid/fm_deep_dive.ipynb)==. Here I prefer to use TensorFlow and don’t want to involve other tech stacks.==
==A naive example is like this== [==one==](https://github.com/gmodena/tensor-fm/blob/master/tensorfm/base.py#L21)==, which uses the native TensorFlow APIs without any high-level Keras layers.==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F6db25d25-2385-4ac5-b099-3cca10720575_988x644.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F6db25d25-2385-4ac5-b099-3cca10720575_988x644.png)
==We can see here the calculation here follows exactly the math formula. After getting the interaction value, then added it to the linear and bias value.==
==Also, let’s look at another example from== [==DeepCTR==](https://github.com/shenweichen/DeepCTR/blob/master/deepctr/layers/interaction.py#L557) ==which is implemented in TensorFlow 1.0.==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F8d987592-791a-4672-adb1-56f29ea515ab_968x622.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F8d987592-791a-4672-adb1-56f29ea515ab_968x622.png)
==Here the== ==**feature vector X**== ==disappears, why?==
1. ==Embedding can be considered as a one hot feature vector X multiply an embedding table matrix V==
2. ==In the DeepCTR’s version, the inputs are already the outcome of the multiplication of X and V. So these 2 versions are actually== ==**equivalent**==
==Combine these 2 versions together, and let’s re-==[==implement==](https://github.com/caesarjuly/reginx/blob/master/trainer/models/common/feature_cross.py#L4) ==it in TensorFlow 2 and remove redundant code.==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb246035e-5d9d-40ac-b819-997089863dfd_940x271.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb246035e-5d9d-40ac-b819-997089863dfd_940x271.png)
==And define a ranking== [==model==](https://github.com/caesarjuly/reginx/blob/master/trainer/models/fm.py) ==using this FM layer. Here I separate linear user and item features from each other intentionally. I will explain more about this later.==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F5e9a9b0a-0f76-4f92-9a78-f92479c453ba_1074x1177.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F5e9a9b0a-0f76-4f92-9a78-f92479c453ba_1074x1177.png)
==That’s it. The final logits = sigmoid(bias + linear + fm).==
==Deep ranking models dominate the recommender ranking system nowadays and they outperform FM a lot on ranking metrics. Is FM still useful?==
==**Yes, it can be easily transformed into a CR.**==
==Compared to collaborative filtering based CR or two-tower based CR, FM has a clear advantage in explicit modeling feature interactions.==
==Suppose we have in total n features and m user features, then the features can be split into a user feature part Vu and an item feature part Vk:== [==4==](https://happystrongcoder.substack.com/p/from-fm-to-deepfm-the-almighty-factorization#footnote-4-130083204)
==The original FM formula is equivalent to (ignore the x features for simplicity):==
==This means the target is equal to the following:==
==**y = bias + linear_user_part_score + linear_item_part_score + user2user_interaction_score + item2item_interaction_score + user2item_interaction_score**==
==Because in the online recall scenario, the querying user is the same. So for each item, the user part is the same. So we only need to calculate the item part score:==
==**user_item_matching_score = linear_item_part_score + item2item_interaction_score + user2item_interaction_score**==
==And notice that:==
==Simplify the above formulas:==
==**user_item_matching_score = item_part_score + dot<sum_of_user_vector, sum_of_item_vector>**==
==So we can build the user and item embedding as:==
==Then the dot product of these 2 embeddings will be our target matching score.==
==In the training phase, we need to separate the user and item features and save the corresponding== [==models==](https://github.com/caesarjuly/reginx/blob/master/trainer/tasks/fm_ranker_train.py) ==as:==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F044b8207-bee7-464f-bcd2-5aea26338309_1097x798.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F044b8207-bee7-464f-bcd2-5aea26338309_1097x798.png)
==When generating embeddings, we just== [==follow==](https://github.com/caesarjuly/reginx/blob/master/trainer/tasks/generate_fm_embedding.py) ==the math calculation:==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F5f2874c0-742f-4094-b8c9-16f44ccbf1be_1037x1246.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F5f2874c0-742f-4094-b8c9-16f44ccbf1be_1037x1246.png)
==And let’s add the user part score to verify the correctness.==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F23ccf803-87eb-41a7-8e8f-b75db4b58dde_1734x599.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F23ccf803-87eb-41a7-8e8f-b75db4b58dde_1734x599.png)
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F74eb2ceb-7df4-46bc-9e9e-1f6cafb8c7b6_1844x107.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F74eb2ceb-7df4-46bc-9e9e-1f6cafb8c7b6_1844x107.png)
==That’s it. Then we can save all the item embeddings to any ANN search engine like Faiss and deploy the index to the online service.==
==We already fully understand the theory of FM, then DeepFM is just easy peasy!==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb155a1eb-307a-47bd-b2c3-86de6dab27e7_961x587.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb155a1eb-307a-47bd-b2c3-86de6dab27e7_961x587.png)
==As we can see in the picture, the prediction logits of DeepFM is:==
==y = sigmoid(linear_score + fm_score + dense_score)==
==Notice that there are 2 unique requirements:==
1. ==The embedding dimension of all features are the same, to support dot product for FM==
2. ==The embeddings are shared between the FM and Dense part==
==Sharing a similar structure with the W&D model, the forward pass of== [==DeepFM==](https://github.com/caesarjuly/reginx/blob/master/trainer/models/deepfm.py) ==is:==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F1114f846-3e43-48f4-b30a-0181ae597a82_949x271.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F1114f846-3e43-48f4-b30a-0181ae597a82_949x271.png)
==FM layer is just the same code as I shared before. Notice the== ==**deep_emb**== ==is shared between FM and Deep layers.==
==Train the above models on MovieLens 1m dataset (I use similar hyperparameter configs for all models without much tuning):==
1. ==DeepFM and MaskNet is slightly better than FM, empowered by the Deep NN layers==
2. ==W&D performs worst because I only do limited work on feature engineering==
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F9affaa0a-0092-4afd-8418-7adfd81573e8_1981x965.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F9affaa0a-0092-4afd-8418-7adfd81573e8_1981x965.png)
1. [==My 20 Year Career is Technical Debt or Deprecated==](https://blog.visionarycto.com/p/my-20-year-career-is-technical-debt)==, a fun story about how the techniques evolve.==
2. [==The Curse of Recursion: Training on Generated Data Makes Models Forget==](https://www.reddit.com/r/LocalLLaMA/comments/13ymov8/the_curse_of_recursion_training_on_generated_data/)==, What will happen to GPT-n once LLMs contribute much of the language found online?==
3. [==Sequential Testing at Booking.com==](https://booking.ai/sequential-testing-at-booking-com-650954a569c7)==, a practical guide on how booking leverage Sequential Testing to make reliable and fast product decisions. We don’t have to wait for the the required sample size arrives anymore.==
    
    ==A major benefit of sequential testing is that it does allow for interim analyses while maintaining the correct alpha error rate.==
    
4. [==Mosh==](https://mosh.org/)==, a remote terminal application that allows== ==**roaming**====, supports== ==**intermittent connectivity**====, and provides intelligent== ==**local echo**== ==and line editing of user keystrokes.==
==Thank you for reading Be a happy and strong coder. This post is public so feel free to share it.==
[==Share==](https://happystrongcoder.substack.com/p/from-fm-to-deepfm-the-almighty-factorization?utm_source=substack&utm_medium=email&utm_content=share&action=share)
[==1==](https://happystrongcoder.substack.com/p/from-fm-to-deepfm-the-almighty-factorization#footnote-anchor-1-130083204)
==https://nowave.it/factorization-machines-with-tensorflow.html==
[==2==](https://happystrongcoder.substack.com/p/from-fm-to-deepfm-the-almighty-factorization#footnote-anchor-2-130083204)
==https://arxiv.org/pdf/1703.04247.pdf==
[==3==](https://happystrongcoder.substack.com/p/from-fm-to-deepfm-the-almighty-factorization#footnote-anchor-3-130083204)
==https://zhuanlan.zhihu.com/p/58160982==
[==4==](https://happystrongcoder.substack.com/p/from-fm-to-deepfm-the-almighty-factorization#footnote-anchor-4-130083204)
==https://zhuanlan.zhihu.com/p/456982760==
### ==Subscribe to Be a happy and strong coder==
==I'm a machine learning, back end engineer and also a big fan of rock climbing lives in bay area. This post will cover my personal experience and knowledge in machine learning and recommender system. Be happy, be strong and enjoy life.==