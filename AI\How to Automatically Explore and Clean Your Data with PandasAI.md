---
Updated: 2023-10-09T11:53
tags:
  - AI->-Dataset
  - AI->-Fine-Tuning
  - AI->-Pandas
  - AI->-Programming
Created: 2023-10-09T11:52
---
[![](https://miro.medium.com/v2/resize:fit:1024/1*3jX6jduV0yDW-1mnxUT2GQ.jpeg)](https://miro.medium.com/v2/resize:fit:1024/1*3jX6jduV0yDW-1mnxUT2GQ.jpeg)
## ==Easily clean your data with the prompt====  
  
====根据提示轻松清理数据==
==[==
[![](https://miro.medium.com/v2/resize:fill:44:44/1*x6yI4Kr46iwkoGn1QwG7qw.png)](https://miro.medium.com/v2/resize:fill:44:44/1*x6yI4Kr46iwkoGn1QwG7qw.png)
==](https://pub.towardsai.net/@cornelliusyudhawijaya?source=post_page-----bd982c9a7ec1--------------------------------)[==
[![](https://miro.medium.com/v2/resize:fill:24:24/1*JyIThO-cLjlChQLb6kSlVQ.png)](https://miro.medium.com/v2/resize:fill:24:24/1*JyIThO-cLjlChQLb6kSlVQ.png)
==](https://medium.com/towards-artificial-intelligence?source=post_page-----bd982c9a7ec1--------------------------------)==
[![](https://miro.medium.com/v2/resize:fit:700/1*3jX6jduV0yDW-1mnxUT2GQ.jpeg)](https://miro.medium.com/v2/resize:fit:700/1*3jX6jduV0yDW-1mnxUT2GQ.jpeg)
==Image generated with ideogram.ai====  
  
====使用 ideogram.ai 生成的图像==
==If you are not subscribed as a Medium Member, please consider subscribing through== [==my referral==](https://cornelliusyudhawijaya.medium.com/membership)==.====  
  
====如果您不是中型会员，请考虑通过我的推荐进行订阅。==
==We are at the height of Large Language Model (LLM) adoption, where much of our textual requirements are provided quickly. From question answering, link searching, and daily task planning to content creation — LLM can give them now.====  
  
====我们正处于大型语言模型 (LLM) 采用的高峰期，我们的许多文本需求都可以快速提供。从问题回答、链接搜索、日常任务规划到内容创建——法学硕士现在可以给他们。==
==One of the leading companies in the LLM research is the== [==OpenAI==](https://openai.com/)==, especially with their GPT family model. With the introduction of ChatGPT, the public has been introduced to the power of the LLM, which is hard to come back without.====  
  
====OpenAI 是法学硕士研究领域的领先公司之一，尤其是他们的 GPT 系列模型。随着 ChatGPT 的推出，公众已经了解了 LLM 的力量，而没有这种力量就很难再回来。==
==The Python programming language has a renowned data manipulation library called Pandas. It's a staple for many data people who want to perform any data exploration in Python. With the GPT model around, one of the exciting applications is combining the power of LLM with Pandas — the package called== [==PandasAI==](https://github.com/gventuri/pandas-ai)==.====  
  
====Python 编程语言有一个著名的数据操作库，称为 Pandas。对于许多想要在 Python 中执行任何数据探索的数据人员来说，它是一个主要工具。随着 GPT 模型的出现，令人兴奋的应用之一是将 LLM 的强大功能与 Pandas 相结合——称为 PandasAI 的软件包。==
==Using generative AI's power, PandasAI would help us manipulate data with natural language. How could we do that? Let's explore them further.====  
  
====利用生成式人工智能的力量，PandasAI 将帮助我们用自然语言操作数据。我们怎么能这么做呢？让我们进一步探讨它们。==
## ==**PandasAI 熊猫人工智能**==
[==PandasAI==](https://github.com/gventuri/pandas-ai) ==is a Python package that provides LLM implementation in Pandas. It is aimed at complementing Pandas, not replacing them. By using PandasAI, we can turn the Pandas package into a conversational tool that would automatically explore and clean our data.====  
  
====PandasAI 是一个 Python 包，提供 Pandas 中的 LLM 实现。它的目的是补充 Pandas，而不是取代它们。通过使用 PandasAI，我们可以将 Pandas 包变成一个对话工具，可以自动探索和清理我们的数据。==
==To use PandasAI, we need to install them with the following code.====  
  
====要使用 PandasAI，我们需要使用以下代码安装它们。==
==pip install pandasai==
==To use the PandasAI package, we need access to the LLM APIs. We can choose from various models — from OpenAI GPT to the HuggingFace model.====  
  
====要使用 PandasAI 包，我们需要访问 LLM API。我们可以从各种模型中进行选择——从 OpenAI GPT 到 HuggingFace 模型。==
==We would use the OpenAI model in this example, but I would give you a code example if you want to change the model into something else. We will also== ==**perform the instance in the Notebook**== ==so that this article would assume the work in that environment.====  
  
====我们将在本例中使用 OpenAI 模型，但如果您想将模型更改为其他模型，我会给您一个代码示例。我们还将在 Notebook 中执行实例，以便本文假设在该环境中进行工作。==
## ==OpenAI 开放人工智能==
==To use the OpenAI API, we need to generate the key. Use the website to get the key and use it in the code below.====  
  
====要使用 OpenAI API，我们需要生成密钥。使用该网站获取密钥并在下面的代码中使用它。==
==from pandasai.llm import OpenAI==
==llm = OpenAI(api_token="Your OpenAI API Key")==
## ==**HuggingFace 抱脸**==
==We can access only two models from the HuggingFace for PandasAI:== [==Starcoder==](https://huggingface.co/bigcode/starcoder) ==and== [==Falcon==](https://huggingface.co/tiiuae/falcon-7b-instruct)==. To set them up, we can use the following code.====  
  
====我们只能从 HuggingFace for PandasAI 访问两个模型：Starcoder 和 Falcon。要设置它们，我们可以使用以下代码。==
==from pandasai.llm import Starcoder, Falcon==
==llm = Starcoder(api_token="Your HuggingFace API Key")==
==llm = Falcon(api_token="Your HuggingFace API Key")==
==To get your HuggingFace API key, you can use the following== [==page==](https://huggingface.co/settings/tokens)==.====  
  
====要获取 HuggingFace API 密钥，您可以使用以下页面。==
## ==**Google PaLM 谷歌PaLM**==
==We can also use the Google PaLM model for the PandasAI LLM model. To do that, we can use the following code.====  
  
====我们还可以使用 Google PaLM 模型作为 PandasAI LLM 模型。为此，我们可以使用以下代码。==
==from pandasai.llm import GooglePalm==
==llm = GooglePalm(api_key="Your Google PaLM key")==
==To access the Google PaLM, we can use this== [==page==](https://developers.generativeai.google/tutorials/setup) ==to acquire the Google Cloud API key.====  
  
====要访问Google PaLM，我们可以使用此页面获取Google Cloud API密钥。==
## ==Exploring Data with PandasAI====  
  
====使用 PandasAI 探索数据==
==With your chosen LLM model, we would explore the data. We could automatically explore the data by relying on the PandasAI conversational ability.====  
  
====根据您选择的法学硕士模型，我们将探索数据。我们可以依靠PandasAI的对话能力来自动探索数据。==
==First, let's set up the dataset and pass it into the PandasAI object. I would use the MPG dataset from the Seaborn package in this example.====  
  
====首先，我们设置数据集并将其传递到 PandasAI 对象中。在此示例中，我将使用 Seaborn 包中的 MPG 数据集。==
==import seaborn as sns====  
  
====from pandasai import SmartDataframe==
==mpg = sns.load_dataset('mpg')====  
  
====df = SmartDataframe(mpg, config = {'llm': llm})==
==Using the== ==`SmartDataframe`== ==object, it would accept the llm we initiated before and allow our DataFrame to become a conversational object.====  
  
====使用== ==`SmartDataframe`== ==对象，它将接受我们之前启动的 llm 并允许我们的 DataFrame 成为一个会话对象。==
==Now, let's start from the simple one. We would ask for the primary== ==**data information**== ==with the code below.====  
  
====现在，让我们从简单的开始。我们将通过以下代码询问主要数据信息。==
==response = df.chat("""Can you help me understand the basic====  
  
====information of the dataset""")==
==response==
[![](https://miro.medium.com/v2/resize:fit:343/1*oRhO3WFWKArZQzzetzRgow.png)](https://miro.medium.com/v2/resize:fit:343/1*oRhO3WFWKArZQzzetzRgow.png)
==Image by Author 作者提供的图片==
==The result is the Pandas DataFrame== ==`info`== ==attribute. Then we can do a follow-up analysis; for example, how is the== ==**dataset statistic**====.====  
  
====结果是 Pandas DataFrame== ==`info`== ==属性。然后我们就可以进行后续分析；例如，数据集统计情况如何。==
==response = df.chat("""Can you give me the basic statistic====  
  
====for both numerical and non-numerical object""")==
==response==
[![](https://miro.medium.com/v2/resize:fit:700/1*55QG0rQ97OOPqzQ8Eupgxg.png)](https://miro.medium.com/v2/resize:fit:700/1*55QG0rQ97OOPqzQ8Eupgxg.png)
==Image by Author 作者提供的图片==
==The result is the DataFrame statistics for the whole dataset and the correct method for the data type.====  
  
====结果是整个数据集的DataFrame统计信息以及数据类型的正确方法。==
==Let's try something else. We would ask to find the== ==**missing data**== ==from our dataset and isolate them.====  
  
====让我们尝试一些别的东西。我们会要求从数据集中找到丢失的数据并将其隔离。==
==response = df.chat("""Please provide me the data rows that====  
  
====contains missing data in any columns""")==
==response==
[![](https://miro.medium.com/v2/resize:fit:689/1*wBQaSl6o0Bgn1gqJaqGSvQ.png)](https://miro.medium.com/v2/resize:fit:689/1*wBQaSl6o0Bgn1gqJaqGSvQ.png)
==Image by Author 作者提供的图片==
==We can also ask the object to== ==**fill in the missing data**====.====  
  
====我们还可以要求对象填写缺失的数据。==
==response = df.chat('Please fill the missing data with the mean')==
==Then, we can try to find the== ==**data outlier based on specific columns**====. For example, I would fill in the missing data and try to find the outlier based on the== ==`horsepower`== ==column.====  
  
====然后，我们可以尝试根据特定列查找数据异常值。例如，我会填写缺失的数据，并尝试根据== ==`horsepower`== ==列查找异常值。==
==response = df.chat("""Please fill the missing data with the mean then====  
  
====find the data rows outlier based on the horsepower column""")==
==response==
[![](https://miro.medium.com/v2/resize:fit:700/1*8li37yzZaFXhggpbrsUw4w.png)](https://miro.medium.com/v2/resize:fit:700/1*8li37yzZaFXhggpbrsUw4w.png)
==Image by Author 作者提供的图片==
==We can extend the prompt to== ==**remove the outlier**== ==if we want it (plus index reset).====  
  
====如果需要的话，我们可以扩展提示以删除异常值（加上索引重置）。==
==response = df.chat("""Please fill the missing data with the mean then find and====  
  
====remove the data rows outlier based on the horsepower column====  
  
====plus reset the index""")==
==response==
[![](https://miro.medium.com/v2/resize:fit:700/1*zi7EutgvTMgrxgTa0IVsfg.png)](https://miro.medium.com/v2/resize:fit:700/1*zi7EutgvTMgrxgTa0IVsfg.png)
==Image by Author 作者提供的图片==
==We can also ask to== ==**convert the categorical object into a numerical column**== ==and== ==**standardize the numerical column**====.====  
  
====我们还可以要求将分类对象转换为数字列并对数字列进行标准化。==
==response = df.chat("""Please give me the whole dataframe after====  
  
====perform one hot encoding to the origin column====  
  
====and standardize the mpg column""")==
==response==
[![](https://miro.medium.com/v2/resize:fit:700/1*m26j3atfxcnkTZiSFidHSg.png)](https://miro.medium.com/v2/resize:fit:700/1*m26j3atfxcnkTZiSFidHSg.png)
==Image by Author 作者提供的图片==
==The data exploration result depends on the given prompt. We can ask the PandasAI to do whatever data activity we require.====  
  
====数据探索结果取决于给定的提示。我们可以要求 PandasAI 执行我们需要的任何数据活动。==
==The result is not limited to the DataFrame object; we can try to get data visualization with PandasAI.====  
  
====结果不限于DataFrame对象；我们可以尝试使用 PandasAI 来实现数据可视化。==
==For example, I want to know about the data distribution of the== ==`mpg`== ==column.====  
  
====比如我想了解== ==`mpg`== ==列的数据分布情况。==
==response = df.chat('Please provide me the mpg column data distribution')====  
  
====response==
[![](https://miro.medium.com/v2/resize:fit:405/1*W6N5fzPFHeiBLZ3zFmgRFQ.png)](https://miro.medium.com/v2/resize:fit:405/1*W6N5fzPFHeiBLZ3zFmgRFQ.png)
==Image by Author 作者提供的图片==
==Or we can try a more complex visualization. For example, we want to check the distribution of the== ==`mpg`== ==column divided by both== ==`cylinders`== ==and== ==`origin`== ==column.====  
  
====或者我们可以尝试更复杂的可视化。例如，我们想要检查== ==`mpg`== ==列除以== ==`cylinders`== ==和== ==`origin`== ==列的分布。==
==df.chat("""Please plot the distribution of mpg column and====  
  
====compared it with cylinders and origin column""")==
[![](https://miro.medium.com/v2/resize:fit:641/1*p_NLHFrQ3x9aOV0FZ5Za6g.png)](https://miro.medium.com/v2/resize:fit:641/1*p_NLHFrQ3x9aOV0FZ5Za6g.png)
==Image by Author 作者提供的图片==
## ==**Advance Usage 提前使用**==
==PandasAI also provides a shortcut attribute for several activities often used for data exploration and cleaning. Here are several functions you can try out:====  
  
====PandasAI 还为经常用于数据探索和清理的几个活动提供了快捷属性。您可以尝试以下几个功能：==
- ==**Feature Generation 特征生成**==
==df.generate_features()==
[![](https://miro.medium.com/v2/resize:fit:700/1*5kbZlKz4fvR5NeZHT06MJA.png)](https://miro.medium.com/v2/resize:fit:700/1*5kbZlKz4fvR5NeZHT06MJA.png)
==Image by Author 作者提供的图片==
- ==**Cleaning and Missing Data imputation**====  
      
    ====**清理和缺失数据插补**==
==df.clean_data().impute_missing_values()==
[![](https://miro.medium.com/v2/resize:fit:700/1*JPvvwHPtNz-6W4ZmijRTPA.png)](https://miro.medium.com/v2/resize:fit:700/1*JPvvwHPtNz-6W4ZmijRTPA.png)
==Image by Authors 作者提供的图片==
- ==**Histogram Plot 直方图**==
==df.plot_histogram(column = 'mpg')==
[![](https://miro.medium.com/v2/resize:fit:396/1*cUa097uGUg4dkrCVkp0Amg.png)](https://miro.medium.com/v2/resize:fit:396/1*cUa097uGUg4dkrCVkp0Amg.png)
==Image by Author 作者提供的图片==
==There are still many shortcuts you can try out. Visit the== [==documentation==](https://docs.pandas-ai.com/en/latest/shortcuts/) ==to learn more about them.====  
  
====您仍然可以尝试许多快捷方式。请访问文档以了解有关它们的更多信息。==
## ==Conclusion 结论==
==PandasAI is a Python package that can utilize the power of LLM for Pandas data manipulation. Using the conversational natural language, we can simplify data exploration and cleaning. We only need to prepare our prompt, which will run automatically.====  
  
====PandasAI 是一个 Python 包，可以利用 LLM 的强大功能进行 Pandas 数据操作。使用会话式自然语言，我们可以简化数据探索和清理。我们只需要准备好提示符，它就会自动运行。==
==I hope it helps! 我希望它有帮助！==