---
Updated: 2023-03-06T20:24
tags:
  - AI->-Prompt
  - AI->-Theory
Created: 2023-03-06T08:38
---
![[1BoBQWZkDSIcZZKS4i7ROog.png]]
Only read this blog post if you answer YES to the following four questions.
1. **Do you agree** that ChatGPT is an extremely powerful tool that everyone now has access to? Yes/No
2. **Do you agree** that more and more Generative AI applications/start-ups will be coming up soon in the market, and some were already in 2022? Yes/No
3. **Do you agree** that if you can master, a tool like this can give you immense power to speed up your tasks and create more income potential? Yes/No
4. **Do you agree** that if you learn enough about ChatGPT powers, you might find your AI startup idea soon? Yes/No
If you said **YES** to all the above, you agree that a powerful tool needs powerful prompts, or you are not utilizing the full potential.

> Everyone has access to this ultimate power called ‘ChatGPT’ but only people who master the input prompts will get ahead of everybody else.
ChatGPT is like a magic genie, but instead of granting wishes, it answers questions and performs tasks. But just like a genie, you must know how to properly word your requests to get the most out of ChatGPT. So, how do you do that? Let’s dive in and learn how to write the most effective prompts for ChatGPT.
In this article, you will learn:
· [ChatGPT prompts for Writers](https://medium.com/data-driven-fiction/perfect-prompt-that-chatgpt-loves-7b542fae62c3#df3b)  
∘  
[The Power prompt](https://medium.com/data-driven-fiction/perfect-prompt-that-chatgpt-loves-7b542fae62c3#9425)  
∘  
[Here is the generated article using our power prompt](https://medium.com/data-driven-fiction/perfect-prompt-that-chatgpt-loves-7b542fae62c3#3899)  
∘  
[Dont forget to look at these 4 masterstroke inputs to train ChatGPT](https://medium.com/data-driven-fiction/perfect-prompt-that-chatgpt-loves-7b542fae62c3#495f)  
∘  
[Update: AI vs Human generate content](https://medium.com/data-driven-fiction/perfect-prompt-that-chatgpt-loves-7b542fae62c3#4242)  
∘  
[Efficiency at its Finest](https://medium.com/data-driven-fiction/perfect-prompt-that-chatgpt-loves-7b542fae62c3#d4d6)  
∘  
[Surprisingly it also passed the AI detector test](https://medium.com/data-driven-fiction/perfect-prompt-that-chatgpt-loves-7b542fae62c3#4d4d)  
∘  
[📣 Save the date for Live Coaching](https://medium.com/data-driven-fiction/perfect-prompt-that-chatgpt-loves-7b542fae62c3#a2be)  
·  
[📣Special Announcement for our readers and writers](https://medium.com/data-driven-fiction/perfect-prompt-that-chatgpt-loves-7b542fae62c3#1b8f)
![[1R6WcU6A0_HN4crhRFocZ4A.gif]]
**Pro Tip** ➡ We will be publishing more guides, such as “ChatGPT prompts for Data Scientists”, “ChatGPT prompts for Programmers”, “ChatGPT prompts for Musicians” better follow us here and on other social platforms, don’t miss anything! [YouTube](https://www.youtube.com/@the-latest-now?sub_confirmation=1), [Twitter](https://twitter.com/TheLatestN0W), [Instagram](https://www.instagram.com/the.latest.now/), [TikTok](https://www.tiktok.com/@thelatestnow.com).
## 📣 **Save the date for Live Coaching**

> 10th March YouTube live on “ChatGPT Prompt Engineering Tactics everyone should learn”
> 
> 17th March [YouTube](https://www.youtube.com/@the-latest-now?sub_confirmation=1) live on “**Build an impressive AI App using ChatGPT under 20 mins**”
> 
> More announcements and info soon on posted on [YouTube](https://www.youtube.com/@the-latest-now?sub_confirmation=1) and [Twitter](https://twitter.com/TheLatestN0W).
![[1yoDbz4Yuf4X5LQQwar5knQ.png]]
# ChatGPT prompts for Writers
You must have tried asking chatGPT to give you an article like this.
![[19cLpUUS4-uazruZCWb4UEg.png]]
AI gives you basic, boring, blah blah results because AI assumes many parameters (per the baseline), which is how it gives you these basic logical results.
## The Power prompt
The key is to educate ChatGPT on the specifics you want. Check these TEN inputs you need to provide to get the best results (But that is not all of it)
1. **Topic** or idea for the article: Main subject and focus of the article.
2. P**urpose** or **goal** of the article: What the article is trying to achieve, whether it’s to inform, persuade, entertain, or something else.
3. **Article’s Tone**: Usually, GPT sets the tone based on the topic, but it’s good to provide it as input.
4. **Limit**: The number of words or you can use short or long lengths.
5. Any specific **SEO** **keywords** or **phrases**: If there are specific SEO keywords or phrases that you would like to include in the article.
6. T**arget audience**: Who the article is for; this way, GPT can tailor the language, tone, and style to suit the readers.
7. Any specific **sources** or **references**: If you want to add any information or specific sources/references, please provide a paragraph for those details. Update: ChatGPT New Version 3.5 -can read website links, so you can also reference articles! Yeyyy!
8. **Call to Action**: You can include your CTA in the conclusion paragraph.
9. **Includes:** Something you want to add, like relevant examples, case studies, social proofs, comparisons, or anything else.
10. **Title** and **Subtitle** Suggestion: Well, it says all.
## **Here is the generated article using our power prompt (**[**Download updated version**](https://ko-fi.com/s/4352616e4d)**)**
[Download the extended updated version of this prompt](https://ko-fi.com/s/4352616e4d)
![[1bssvMrPFvQoUWM2JL-iPnQ.jpeg]]
**Edits: Thanks for donating. I humbly appreciate your support.**
[Howard L. Keziah](https://medium.com/u/c7e53537ebf9?source=post_page-----7b542fae62c3--------------------------------)
,
[Eric Fernwood](https://medium.com/u/65b8b2dc038e?source=post_page-----7b542fae62c3--------------------------------)
,
[D. Scott Smith](https://medium.com/u/f5c4ae8245f2?source=post_page-----7b542fae62c3--------------------------------)
,
[Dr. Ernesto Lee](https://medium.com/u/53ee0651b83e?source=post_page-----7b542fae62c3--------------------------------)
,
[Valentina Porcu](https://medium.com/u/1f1a38421897?source=post_page-----7b542fae62c3--------------------------------)
[Donations](https://ko-fi.com/s/4352616e4d)
![[1uOg9nBaVwyzE9rx9DBJpsg.png]]
## Update: AI vs Human generate content
Add this before you ask Chat GPT to write anything to get Human-like text generation.

> Hey ChatGPT, regarding generating writing content, two factors are crucial to be in the highest degree, “perplexity” and “burstiness.” Perplexity measures the complexity of the text. Separately, burstiness compares the variations of sentences. Humans tend to write with greater burstiness, for example, with some longer or more complex sentences alongside shorter ones. Al sentences tend to be more uniform. Therefore, generated text content must have the highest degree of perplexity and the highest degree of burstiness. The other two factors are that writing should be maximum contextually relevant and maximum coherent.
## Surprisingly it also passed the AI detector test
100 words limit
![[1A-4Dg4iAF7tK17CKkU7AZQ.png]]
150 words limit
![[1NmmZKh5rTBqtMTLFiswvFw.png]]
# 📣Special Announcement
A chance to write for [TheLatestAI](https://medium.com/thelatestai/become-an-ai-writer-9f5174815fdd) publication today. **Writers**: Become a writer and gain exposure to broad audiences on multiple social platforms. **Readers**: Learn fast with The Latest AI content, tutorials, tips, hacks, news, and more.