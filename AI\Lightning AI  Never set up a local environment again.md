---
Updated: 2023-12-15T10:40
tags:
  - AI->-CloudEnv
Created: 2023-12-15T10:40
---
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/06/fallback_social.png)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/06/fallback_social.png)
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/wireframe.svg)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/wireframe.svg)
==LIGHTNING AI STUDIOS==
## ==All-in-one AI development cloud platform==
==Code together. Prototype. Prep data. Serve. Train models.====  
  
====All in the same place.==
==Free GPU hours. No credit card==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/wireframe.svg)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/wireframe.svg)
==FROM THE CREATORS OF PYTORCH LIGHTNING==
### ==81+ million==
### ==10,000 GPUs==
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/open-source-plt.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/open-source-plt.svg)
==PyTorch Lightning==
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/github-icon.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/github-icon.svg)
==24.9k==
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/open-source-fabric.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/open-source-fabric.svg)
==Lightning Fabric==
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/github-icon.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/github-icon.svg)
==24.9k==
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/open-source-torchmetrics.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/open-source-torchmetrics.svg)
==Torchmetrics==
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/github-icon.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/github-icon.svg)
==1.7k==
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/open-source-litllama.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/open-source-litllama.svg)
==Lit Llama==
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/github-icon.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/github-icon.svg)
==5.4k==
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/open-source-litgpt.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/open-source-litgpt.svg)
==Lit GPT==
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/github-icon.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/github-icon.svg)
==3.6k==
==Thousands build on Lightning AI Studios==
### ==Multi-node in seconds==
=="Snap teams spent 1+ year on multi-node training - and it still never worked. With Lightning we were successfully training multi-node in under 3 days"==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/snap_logo.jpg)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/snap_logo.jpg)
### ==Justin, Sr. ML Engineer==
### ==Snap==
### ==Prototype 6x faster==
=="Instead of taking 36 hours to know if this model works, we knew by the end of the day."==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/mit.svg)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/mit.svg)
### ==Sam, Researcher==
### ==MIT Sustainable Design Lab==
### ==My IDE on the cloud==
=="My workflow was drastically simplified. I get all the native VS code plugins in the web browser, with all my dependencies, my code, my compute in one spot. It all just works."==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/lexset.png)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/lexset.png)
### ==Frances, CEO==
### ==Lexset==
### ==Sweeps at scale==
=="Lightning AI transformed our model fitting process. We fit 1000 models in only two weeks. At our scale, this would have taken months without Lightning AI."==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/columbia.svg)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/columbia.svg)
### ==Matt, Lead Researcher==
### ==The Paninski Lab, Columbia University==
### ==Feels local, but on the cloud==
=="My computer on the cloud. It's like working on my local machine but using the computing power of Lightning."==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/bordeaux.png)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/bordeaux.png)
### ==Majd Abdallah, Researcher==
### ==Bordeaux Bioinformatics==
### ==Change GPUs in seconds==
=="The experience of changing from CPU to GPU was mindblowing. It’s extremely quick and I didn’t even need to think about copying or mounting my files."==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/user.png)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/user.png)
### ==Irina, ML Engineer==
### ==Lightning User==
### ==Debug together==
=="When it came time for our experts to collaborate on common experiments we ran into the “it works on my machine” problem. With Lightning, we saved hours per week."==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/earthdaily.jpg)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/earthdaily.jpg)
### ==Robin Cole, Researcher==
### ==EarthDaily Analytics==
### ==Experiment at lightning speed==
=="We validated our hypothesis in just 4 weeks for under $10k. It would've taken us at least 70% longer if we did not have the Lightning platform!"==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/octai.jpg)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/octai.jpg)
### ==Malcolm, co-founder==
### ==OctAI==
### ==No environment setup==
=="The environment management is so much better with Lightning AI; like an environment already provisioned for a workspace is a really good model."==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/aeira.png)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/homepage/aeira.png)
### ==Jacqueline, Sr. ML Engineer==
### ==Aeira==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/wireframe.svg)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/wireframe.svg)
### ==Zero-setup, always ready cloud environment==
==Every Studio remembers its environment. Install dependencies and upload files once. Reuse any time without losing data.==
==Click to explore==
==Click to explore==
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/studio-environment.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/studio-environment.svg)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/gpu-selector.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/gpu-selector.svg)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/connect-ide.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/connect-ide.svg)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/streamlit-popover.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/streamlit-popover.svg)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/gradio-popover.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/gradio-popover.svg)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/terminal-popover.svg)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/terminal-popover.svg)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/homepage-hero.webp)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/homepage-hero.webp)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/homepage-hero.webp)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/homepage-hero.webp)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/mmt.webp)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/mmt.webp)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/homepage-hero.webp)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/homepage-hero.webp)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/drive-1.webp)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/drive-1.webp)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/together.webp)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/together.webp)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/streamlit-1.webp)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/streamlit-1.webp)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/gradio-1.webp)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/gradio-1.webp)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/mmt.webp)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/mmt.webp)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/jobs-1.webp)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/jobs-1.webp)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/jupyter-hero.webp)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/jupyter-hero.webp)
[![](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/terminal-1.webp)](https://lightningaidev.wpengine.com/wp-content/uploads/2023/11/terminal-1.webp)
### ==No environment setup.==
### ==Python, PyTorch, CUDA, and everything is already installed. Use the default environment or make your own. Studio remembers dependencies, files and data==
### ==Switch GPUs without losing your data or environment.==
### ==Try 4+ GPU types. Each Studio environment is persistent - the Studio remembers all installed packages, files and data.==
### ==Train models on single or multi-node.==
### ==Multi-gpu, multi-node, LLMs. Lightning is the standard for scaling the world's largest AI models.==
### ==Code in the browser. Connect your local IDE.==
### ==Use the cloud or connect your local IDE without changing your current workflow.==
### ==Upload and share files. Connect S3 buckets.==
### ==Share checkpoints and files. Connect S3 buckets and datasets. All Studios share the same file system.==
### ==Code together.==
### ==Debug, share code and develop as a team.==
### ==Host and share AI apps. Streamlit. Gradio. React JS.==
### ==Share links to any web app like Tensorboard, Streamlit, Gradio and more.==
==Finetune a 13B LLM for==
### ==$100==
==Serve Llama 13B for==
### ==$0.00002==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/wireframe.svg)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/wireframe.svg)
### ==Automate ML workflows==
==Create pipelines. Integrate workflows into your favorite tools.==
### ==Build Studio pipelines==
### ==Integrate any studio into your current workflow. Build pipelines that string together Studios.==
### ==Hyperparameter sweeps your way==
### ==Write custom sweep logic in plain Python. Say goodbye to clunkly CLI tools.==
### ==Multi-node speed benchmark==
### ==Build custom, specialized workflows without building an AI platform that does that one thing only.==
==`from lightning_sdk import Studio, Machine # Prepare dataset s = Studio("prepare-dataset") s.start() s.installed_plugins["dataset-prep"].run("python process.py") # Train model s = Studio("llm-baseline") s.start() s.installed_plugins["multi-machine-training"].run("python train.py") # Serve model s = Studio("inference-api") s.start() s.switch_machine(Machine.A10G) s.run("nvidia-smi") s.run("python server.py --port 8888")``from lightning_sdk import Studio, Machine # start studio s = Studio("sweeper-studio") s.start() # use the jobs plugin jobs_plugin = s.installed_plugins["jobs"] # -------------------------- # Configure your own search space in plain Python instead of clunky abstractions # -------------------------- learning_rates = ['0.01', '0.02', '0.03'] for lr in learning_rates: cmd = f'python main.py --lr {lr}' jobs_plugin.run(cmd, name="sweep", machine=Machine.A10G)``from lightning_sdk import Studio, Machine # start studio s = Studio("hardware-benchmarker") s.start() # use the multi_node plugin multi_node_plugin = s.installed_plugins["multi-machine-training"] # -------------------------- # Benchmark model on different GPU types (each benchmark is multi-node) # -------------------------- machines = [Machine.A10G, Machine.A100, Machine.V100] for machine in machines: cmd = f'python main.py' multi_node_plugin.run(cmd, name="bmrk-1", machine=machine, num_instances=4)`==
[![](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/wireframe.svg)](https://pl-bolts-doc-images.s3.us-east-2.amazonaws.com/wireframe.svg)
### ==A GPU Studio for every AI use case==
==Go beyond demos and prototypes. Start from real, production templates.==
==;==
### ==Get 1 free active Studio and 15 credits monthly==
==15 Lightning credits gets you 6 GPU hours==
==Free==
==$0==
==Students, researchers, hobbyists==
==Key features==
==15 monthly Lightning credits included==
==1 free active Studio. Pay for extra active.==
==Single GPU machines (T4, V100, A10G)==
==Unlimited background execution==
==Connect via SSH and local IDE (VSCode, ...)==
==Persistent storage (2TB limit)==
==Multiplayer live collaboration==
==Use private and public models==
==Community support (via Discord)==
==Pro==
==Engineers, researchers, scientists==
==Additional key features==
==50 monthly Lightning credits included==
==Multi-GPU machines (T4, V100, A10G)==
==Multi-node training (up to 2 machines)==
==Persistent storage (10TB limit)==
==Connect public, private S3 buckets==
==Distributed data prep (up to 2 machines)==
==Community support (via Discord)==