---
Updated: 2024-07-22T17:12
tags:
  - AI->-<PERSON><PERSON>
  - AI->-Embedding
  - AI->-Memory
  - AI->-Programming
Created: 2024-07-22T17:12
---
[![](https://opengraph.githubassets.com/e1e7b381fda5df07b5eac345858748b8166b72b273ad3eb0e43f5f485673f8f0/mem0ai/mem0)](https://opengraph.githubassets.com/e1e7b381fda5df07b5eac345858748b8166b72b273ad3eb0e43f5f485673f8f0/mem0ai/mem0)
[![](https://github.com/mem0ai/mem0/raw/main/docs/images/mem0-bg.png)](https://github.com/mem0ai/mem0/raw/main/docs/images/mem0-bg.png)
==  
  
==
[![](https://camo.githubusercontent.com/0252430f8116d90ae4310895c1572a88497efdb231943027384da688136edc2d/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f736c61636b2d656d626564636861696e2d627269676874677265656e2e7376673f6c6f676f3d736c61636b)](https://camo.githubusercontent.com/0252430f8116d90ae4310895c1572a88497efdb231943027384da688136edc2d/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f736c61636b2d656d626564636861696e2d627269676874677265656e2e7376673f6c6f676f3d736c61636b)
[![](https://camo.githubusercontent.com/1f2725757d06bceff748cdf3a0fec549c770713c9c332c2024279bd597cd54ae/68747470733a2f2f646362616467652e76657263656c2e6170702f6170692f7365727665722f36507a584467456a47353f7374796c653d666c6174)](https://camo.githubusercontent.com/1f2725757d06bceff748cdf3a0fec549c770713c9c332c2024279bd597cd54ae/68747470733a2f2f646362616467652e76657263656c2e6170702f6170692f7365727665722f36507a584467456a47353f7374796c653d666c6174)
[![](https://camo.githubusercontent.com/3cc8567b28c0e3a7e0bc716bbebdda54da55841b4de2e7639272c55275ee90b6/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f666f6c6c6f772f6d656d306169)](https://camo.githubusercontent.com/3cc8567b28c0e3a7e0bc716bbebdda54da55841b4de2e7639272c55275ee90b6/68747470733a2f2f696d672e736869656c64732e696f2f747769747465722f666f6c6c6f772f6d656d306169)
# ==Mem0: The Memory Layer for Personalized AI==
==Mem0 provides a smart, self-improving memory layer for Large Language Models, enabling personalized AI experiences across applications.==
==Note: The Mem0 repository now also includes the Embedchain project. We continue to maintain and support Embedchain ❤️. You can find the Embedchain codebase in the== [==embedchain==](https://github.com/mem0ai/mem0/tree/main/embedchain) ==directory.==
## ==🚀 Quick Start==
### ==Installation==
==pip install mem0ai==
### ==Basic Usage==
==import os  
from mem0 import Memory  
os.environ["OPENAI_API_KEY"] = "xxx"  
# Initialize Mem0  
m = Memory()  
# Store a memory from any unstructured text  
result = m.add("I am working on improving my tennis skills. Suggest some online courses.", user_id="alice", metadata={"category": "hobbies"})  
print(result)  
# Created memory: Improving her tennis skills. Looking for online suggestions.  
# Retrieve memories  
all_memories = m.get_all()  
print(all_memories)  
# Search memories  
related_memories = m.search(query="What are Alice's hobbies?", user_id="alice")  
print(related_memories)  
# Update a memory  
result = m.update(memory_id="m1", data="Likes to play tennis on weekends")  
print(result)  
# Get memory history  
history = m.history(memory_id="m1")  
print(history)  
==
## ==🔑 Core Features==
- ==**Multi-Level Memory**====: User, Session, and AI Agent memory retention==
- ==**Adaptive Personalization**====: Continuous improvement based on interactions==
- ==**Developer-Friendly API**====: Simple integration into various applications==
- ==**Cross-Platform Consistency**====: Uniform behavior across devices==
- ==**Managed Service**====: Hassle-free hosted solution==
## ==📖 Documentation==
==For detailed usage instructions and API reference, visit our documentation at== [==docs.mem0.ai==](https://docs.mem0.ai/)==.==
## ==🔧 Advanced Usage==
==For production environments, you can use Qdrant as a vector store:==
==from mem0 import Memory  
config = {  
"vector_store": {  
"provider": "qdrant",  
"config": {  
"host": "localhost",  
"port": 6333,  
}  
},  
}  
m = Memory.from_config(config)  
==
## ==🗺️ Roadmap==
- ==Integration with various LLM providers==
- ==Support for LLM frameworks==
- ==Integration with AI Agents frameworks==
- ==Customizable memory creation/update rules==
- ==Hosted platform support==
## ==🙋‍♂️ Support==
==Join our Slack or Discord community for support and discussions. If you have any questions, feel free to reach out to us using one of the following methods:==
- [==Join our Discord==](https://embedchain.ai/discord)
- [==Join our Slack==](https://embedchain.ai/slack)
- [==Follow us on Twitter==](https://twitter.com/mem0ai)
- Email us