---
Updated: 2024-05-08T10:46
tags:
  - AI->-<PERSON><PERSON>
  - AI->-Programming
URL: https://github.com/AdityaNG/kan-gpt
Created: 2024-05-08T02:01
---
![[kan-gpt]]
# KAN-GPT
[![](https://camo.githubusercontent.com/57cecf4dfa1ab679d8d9bf9133d719422ed7abd717f1b646b60e573f02bbc819/68747470733a2f2f636f6465636f762e696f2f67682f4164697479614e472f6b616e2d6770742f6272616e63682f6d61696e2f67726170682f62616467652e7376673f746f6b656e3d6b616e2d6770745f746f6b656e5f68657265)](https://camo.githubusercontent.com/57cecf4dfa1ab679d8d9bf9133d719422ed7abd717f1b646b60e573f02bbc819/68747470733a2f2f636f6465636f762e696f2f67682f4164697479614e472f6b616e2d6770742f6272616e63682f6d61696e2f67726170682f62616467652e7376673f746f6b656e3d6b616e2d6770745f746f6b656e5f68657265)
[![](https://github.com/AdityaNG/kan-gpt/actions/workflows/main.yml/badge.svg)](https://github.com/AdityaNG/kan-gpt/actions/workflows/main.yml/badge.svg)
The PyTorch implementation of Generative Pre-trained Transformers (GPTs) using Kolmogorov-Arnold Networks (KANs) for language modeling
## Install it from PyPI
```Plain
pip install kan_gpt
```
## Usage
Refer to the [KAN_GPT.ipynb](https://github.com/AdityaNG/kan-gpt/blob/main/KAN_GPT.ipynb) and [kan_gpt/prompt.py](https://github.com/AdityaNG/kan-gpt/blob/main/kan_gpt/prompt.py) for usage examples. The following is an ourtine of how to use the model:
```Plain
from kan_gpt.model import GPT
from transformers import GPT2Tokenizer
model_config = GPT.get_default_config()
model_config.model_type = "gpt2"
model_config.vocab_size = 50257
model_config.block_size = 1024
model = GPT(model_config)
tokenizer = GPT2Tokenizer.from_pretrained('gpt2')
prompt = "Bangalore is often described as the "
prompt_encoded = tokenizer.encode(
  text=prompt, add_special_tokens=False
)
x = torch.tensor(prompt_encoded).unsqueeze(0)
model.eval()
y = model.generate(x, 50)  # sample 50 tokens
result = tokenizer.decode(y)
print(result)
# Bangalore is often described as the Silicon Valley of India.
# The city has witnessed rapid growth in the past two decades.....
```
## Setup for Development
```Plain
# Download Repo
git clone https://github.com/AdityaNG/kan-gpt
cd kan-gpt
git pull
# Download Dataset
./scripts/download_webtext.sh
# Install dependencies for development
pip install -r requirements.txt
pip install -e .
```
## Train
Use the following dummy script to make sure everything is working as expected
```Plain
WANDB_MODE=offline CUDA_VISIBLE_DEVICE="" python3 -m kan_gpt.train --architecture MLP --batch_size 1 --dummy_dataset --device cpu
WANDB_MODE=offline CUDA_VISIBLE_DEVICE="" python3 -m kan_gpt.train --architecture KAN --batch_size 1 --dummy_dataset --device cpu
```
Then make use of the training script
```Plain
python -m kan_gpt.train
```
## Prompt
You can prompt the model to produce text as follows
```Plain
python -m kan_gpt.prompt --prompt "Bangalore is often described as the " --model_path (checkpoint)
```
## TODOs
- Integrate [minGPT](https://github.com/karpathy/minGPT) and [pykan](https://github.com/KindXiaoming/pykan)
- Dataset downloading script for [WebText](https://github.com/openai/gpt-2-output-dataset)
- PyTorch Dataset parser for [WebText](https://github.com/openai/gpt-2-output-dataset)
- Mini training POC for KAN-GPT
    - Integrate KAN training logic from `KAN.train_kan`
    - Train a dummy batch w/o any memory issues
- Mini training POC for MLP-GPT
- Train MLP-GPT on the webtext dataset as a baseline
- Train KAN-GPT on the webtext dataset as a baseline
- Metrics comparing KAN-GPT and MLP-GPT
- Auto Save checkpoints
- Auto Save checkpoints to W&B
- Auto Download model weights from git / huggingface
- Script to load checkpoint in interactive mode
- Training script to PyTorch Lighting
- Integrate with [efficient-kan](https://github.com/Blealtan/efficient-kan/blob/master/src/efficient_kan/kan.py)
- Test Cases
    - KAN: Forward-Backward test
    - GPT: Forward-Backward test
    - KAN_GPT: Forward-Backward test
    - EFFICIENT_KAN: Forward-Backward test
## Development
Read the [CONTRIBUTING.md](https://github.com/AdityaNG/kan-gpt/blob/main/CONTRIBUTING.md) file.
## References
- [minGPT](https://github.com/karpathy/minGPT)
- [pykan](https://github.com/KindXiaoming/pykan)
- [WebText](https://github.com/openai/gpt-2-output-dataset)