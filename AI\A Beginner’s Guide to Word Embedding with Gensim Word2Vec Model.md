---
DocFlag:
  - ToBeTested
Updated: 2023-08-11T22:02
tags:
  - AI->-Embedding
Created: 2023-08-11T22:02
---
[![](https://miro.medium.com/v2/resize:fit:974/1*-em5mUlKP3fTaOHc3xaZhg.png)](https://miro.medium.com/v2/resize:fit:974/1*-em5mUlKP3fTaOHc3xaZhg.png)
---
# A Beginner’s Guide to Word Embedding with Gensim Word2Vec Model
![[2LIh0MmBKYzUzjyfr9BdQwg.jpeg]]
![[1CJe3891yB1A1mzMdqemkdg.jpeg]]
[Zhi <PERSON>](https://medium.com/@zhlli?source=post_page-----5970fa56cc92--------------------------------)
·
[Follow](https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fsubscribe%2Fuser%2F19bb3e3825ba&operation=register&redirect=https%3A%2F%2Ftowardsdatascience.com%2Fa-beginners-guide-to-word-embedding-with-gensim-word2vec-model-5970fa56cc92&user=Zhi+Li&userId=19bb3e3825ba&source=post_page-19bb3e3825ba----5970fa56cc92---------------------post_header-----------)
Published in
[Towards Data Science](https://towardsdatascience.com/?source=post_page-----5970fa56cc92--------------------------------)
·
8 min read
·
May 30, 2019
![[1-em5mUlKP3fTaOHc3xaZhg.png]]

> Word embedding is one of the most important techniques in natural language processing(NLP), where words are mapped to vectors of real numbers. Word embedding is capable of capturing the meaning of a word in a document, semantic and syntactic similarity, relation with other words. It also has been widely used for recommender systems and text classification. This tutorial will show a brief introduction of genism word2vec model with an example of generating word embedding for the vehicle make model.
## Table of Contents
- [1. Introduction of Word2vec](https://medium.com/p/5970fa56cc92#702d)
- [2. Gensim Python Library Introduction](https://medium.com/p/5970fa56cc92#b513)
- [3. Implementation of word Embedding with Gensim Word2Vec Model](https://medium.com/p/5970fa56cc92#9a6d)
- [3.1 Data Preprocessing:](https://medium.com/p/5970fa56cc92#ffb7)
- [3.2. Genism word2vec Model Training](https://medium.com/p/5970fa56cc92#e71b)
- [4. Compute Similarities](https://medium.com/p/5970fa56cc92#9731)
- [5. T-SNE Visualizations](https://medium.com/p/5970fa56cc92#23fc)
# 1. Introduction of Word2vec
Word2vec is one of the most popular technique to learn word embeddings using a two-layer neural network. Its input is a text corpus and its output is a set of vectors. Word embedding via word2vec can make natural language computer-readable, then further implementation of mathematical operations on words can be used to detect their similarities. A well-trained set of word vectors will place similar words close to each other in that space. For instance, the words women, men, and human might cluster in one corner, while yellow, red and blue cluster together in another.
![[1HmmFCZpKk3i4EvMYZ855tg.png]]
There are two main training algorithms for word2vec, one is the continuous bag of words(CBOW), another is called skip-gram. The major difference between these two methods is that CBOW is using context to predict a target word while skip-gram is using a word to predict a target context. Generally, the skip-gram method can have a better performance compared with CBOW method, for it can capture two semantics for a single word. For instance, it will have two vector representations for Apple, one for the company and another for the fruit. For…