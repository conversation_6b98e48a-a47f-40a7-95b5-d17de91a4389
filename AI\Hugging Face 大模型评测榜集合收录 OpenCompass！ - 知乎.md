---
DocFlag:
  - Reference
Updated: 2023-09-29T12:14
tags:
  - AI->-Model
  - AI->-Programming
  - AI->-Theory
Created: 2023-09-28T01:35
---
![[v2-7b20339d667b8f5ee27ce3bef785a4eb_1440w.jpg]]
在过去的半年多里，大语言模型的发展突飞猛进，无论是产业界还是学术界，均取得了丰硕成果。如何量化大模型性能，开源模型与闭源模型的差距有多大，这些都是整个社区都非常关心的话题。然而大模型评测本身也是一项复杂的系统性工程，各类评测榜单的具体标准也是一团迷雾，让人真假莫辩。
我们面向开源社区，推出 OpenCompass 开放评测体系，以期从更纯粹的学术和中立视角来对大模型的性能进行评价和分析。我们也提供基于 OpenCompass 开源工具的大模型榜单，并在近期将大模型榜单发布在 HuggingFace Space，**同时我们也很荣幸** **OpenCompass LLM Leaderboard** **被** **HuggingFace** **官方的** **The Big Benchmarks Collection** **所收录**
- OpenCompass LLM Leaderboard:
- The Big Benchmarks Collection:
## OpenCompass 是什么？
OpenCompass 是一个开源开放的大模型评测平台，构建了包含学科、语言、知识、理解、推理五大维度的通用能力评测体系，支持了超过 50 个评测数据集和 30 万道评测题目，支持零样本、小样本及思维链评测，是目前最全面的开源评测平台。OpenCompass 支持大部分主流 **HuggingFaces** 上的大语言模型的评测，只需几行简单配置，便可轻松开展模型评测。自 7 月份发布以来，获得了企业界和学术界的大量关注，被阿里巴巴、腾讯、清华大学等数十所企业与科研机构广泛应用于大语言模型和多模态模型研发。
OpenCompass在过去的两个月里进行了大量功能升级，结合大模型的发展需求，新增多项特色能力评测。
- 支持评测**工具调用能力**，配合 Lagent/LangChain 等智能体框架可以快速开展工具调用能力的评测
- 新增**长文本评测**能力，支持 L-Eval, LongBench 等长文本评测集
- 开放**多模态评测**能力，支持 MMBench, SEED-Bench, MME, ScienceQA 等十余个多模态评测集
- 提升**代码评测能**力，支持 HumanEval-X，并提供统一的多语言代码能力测试环境
OpenCompass 平台提供大模型榜单，开源评测工具，自建评测集等多种不同的开源项目，旨在为社区提供丰富强大的一站式评测平台。我们也欢迎开源社区一起共建 OpenCompass 平台，助力大模型研发与应用。
## 玩转 OpenCompass LLM Leaderboard
我们在 HuggingFace 的 Space 和 OpenCompass 官方网站均已同步上线面向大语言模型的 LLM Leaderboard。
### LLM 性能榜单，全能选手还是单项冠军?
基于 OpenCompass LLM Leaderboard，我们可以通过查看不同的能力维度选项来分别查看综合榜单与专项榜单，同时我们也区分了中文数据集与英文数据集，方便面向中文语境进行模型评测和模型选型。
### 打开 Model Card， 一览模型性能全貌
在 LLM LeadBoard 页面（[opencompass.org.cn/lead](https://link.zhihu.com/?target=https%3A//opencompass.org.cn/leaderboard-llm)），我们点击模型的名称即可跳转进入模型详情页（Model Card），查看单个模型的各项具体数据，通过选定不同能力维度，即可快速查看模型在各个细分能力的数据集上的性能表现。
例如以下是我们进入 GPT-4 Model Card 的页面 ([opencompass.org.cn/mode](https://link.zhihu.com/?target=https%3A//opencompass.org.cn/model-detail/GPT-4))。
使用 Dataset Card ，更懂大模型评测集
在 Dataset 页面([opencompass.org.cn/abil](https://link.zhihu.com/?target=https%3A//opencompass.org.cn/ability))，我们点击数据集的名称即可跳转进入数据集详情页（Dataset Card），支持查看每个评测数据集的具体信息，如论文，官网，数据示例等等，让你迅速了解每一个评测数据集。同时我们提供大量开源模型在该数据集上的评测结果，支持更进一步的数据分析与模型分析。
例如以下是我们进入 MMLU Dataset Card 的页面 ([opencompass.org.cn/data](https://link.zhihu.com/?target=https%3A//opencompass.org.cn/dataset-detail/MMLU))。
强大的模型对比能力，知己知彼方可百战百胜
OpenCompass 支持模型对比功能，通过在大模型榜单上选取需要进行对比的多个模型，我们即可方便地通过雷达图，直方图等快速进行模型分析和性能对比，轻松获取各个能力维度和具体数据集上的差距与优势。
例如这是我们预设好的 GPT-4、ChatGPT、StableBeluga2 和 LLaMA-2-70B 四个模型各个维度的能力，体验网址为：[opencompass.org.cn/mode](https://link.zhihu.com/?target=https%3A//opencompass.org.cn/model-compare/GPT-4%2CChatGPT%2CStableBeluga2%2CLLaMA-2-70B)
如果需要对比其他模型，只需要在模型对比页面，移除不需要的模型，添加需要的模型即可，操作如下图所示：
## 不止 LLM，解锁多模态能力评测
OpenCompass 团队在 LLM 评测的基础上，进一步研发了 MMBench。MMBench 是 OpenCompass 官方自建的视觉语言模型评测数据集，从感知到认知能力逐级细分评估，从互联网与权威基准数据集采集约 3000 道单项选择题 ，覆盖目标检测、文字识别、动作识别、图像理解、关系推理等 20 个细粒度评估维度。同时提出了更具鲁棒性的评估方式，相同单选问题循环选项提问，模型输出全部指向同一答案认定为通过，最大程度减少各种噪声因素对评测结果的影响，保证了结果的可复现性。
MMBench 也提供 Test 榜单，目前也有多个主流视觉语言模型在 MMBench 提交并发布评测结果。
## 开源评测工具 OpenCompass 量化模型性能提升
我们将各类评测能力开源在 OpenCompass 项目：[https://github.com/open-compass/opencompass](https://link.zhihu.com/?target=https%3A//github.com/open-compass/opencompass/discussions/categories/community-task)，社区用户可以自行使用此工具对大模型进行全方面能力的评测以及复现 OpenCompass 官网的评测结果。
为了帮助用户快速上手 OpenCompass 评测工具，迅速开展模型能力评估，OpenCompass 提供丰富完善的文档，包含环境安装、数据集配置、模型准备、高效评测、任务运行监控、评估指标和结果展示等详细文档。
作为开源工具，我们积极吸引社区力量共建 OpenCompass 工具，并筹备了贡献者组织 OpenCompass SIG，为社区小伙伴们精心准备了 10+ 社区共建任务，欢迎大家访问以下链接领取任务，完成任务将获得一定积分，累计 50 积分可兑换定制抱枕、鼠标垫和水杯等精美周边~
[github.com/open-compass](https://link.zhihu.com/?target=https%3A//github.com/open-compass/opencompass/discussions/categories/community-task)