---
DocFlag:
  - Reference
Updated: 2023-05-24T09:24
tags:
  - <PERSON>->-<PERSON><PERSON><PERSON><PERSON>
  - <PERSON>->-Programming
Created: 2023-05-24T00:43
---
Photo by [<PERSON>](https://unsplash.com/pt-br/@cgower?utm_source=medium&utm_medium=referral) on [Unsplash](https://unsplash.com/?utm_source=medium&utm_medium=referral)
In the previous articles ([1](https://betterprogramming.pub/creating-my-first-ai-agent-with-vicuna-and-langchain-376ed77160e3),[2](https://betterprogramming.pub/fine-tuning-my-first-wizardlm-lora-ca75aa35363d)), we saw that LLMs could generate and execute coding instructions sequences — however, often, they get stuck on errors, especially related to package installation.
I wanted to have something similar to Langchain Python REPL, but that instead:
1. Allowed the generated source code to be saved in a file.
2. Allowed the agent to install pip packages.
3. Allowed execution of the source code, with the full output/traceback
4. Allowed linters to be applied and different code generations sampled accordingly.
5. Offered the option to delegate the code generation completely to the tool, so the main agent does not have to be necessarily good at coding (though the model used in code still has to be good at coding).
To fulfill these requirements, I rolled my own library, [code-i](https://github.com/paolorechia/code-it)t — it’s still early in development, lacking some documentation and more testing. However, the basic functionality is working, and I would like to present it.
In this article, we’ll go:
1. The overall architecture/idea of the library
2. The inner workings of the package
3. Conclusion and next steps
**Note:** this library does not support OpenAI API, as I’ve been using exclusively local LLMs — one could easily extend it to support those models.
You can find the [library at this link](https://github.com/paolorechia/code-it).
# Overall Architecture of Code-It
Fundamentally, it’s a very simple library. It uses a collection of prompts to request completions from LLMs while performing some basic control logic to build the coding solution.
## The task executor
This encompasses almost the whole box. It’s a simple code that calls each LLM in turn and performs some cleaning of the output to use it for some purpose.
It also initializes the code editor class, which contains a virtualenv manager. The code editor exposes simple methods, like update code, run code, etc.
For instance, two relevant methods are replacing the source code:
```Plain
def overwrite_code(self, new_source: str):
    new_lines_of_code = [line for line in new_source.split("\n") if line]
    self.source_code = new_lines_of_code
    self.save_code()
    return self.display_code()
```
And executing code:
```Plain
def run_code(self, *args, **kwargs):
    completed_process = subprocess.run(
        [self.interpreter, self.filename], capture_output=True, timeout=10
    )
    print(completed_process, completed_process.stderr)
    succeeded = "Succeeded" if completed_process.returncode == 0 else "Failed"
    stdout = completed_process.stdout
    stderr = completed_process.stderr
    return f"Program {succeeded}\nStdout:{stdout}\nStderr:{stderr}"
```
Please look at the source code of this class if you’d like to understand it better.
## The planner prompt
The first step in execution is to generate a plan of action. This is the main part of the planner prompt:
```Plain
You're an AI master at planning and breaking down a coding task into smaller, tractable chunks.
You will be given a task, please helps us thinking it through, step-by-step.
First, let's see an example of what we expect:
Task: Fetch the contents of the endpoint 'https://example.com/api' and write to a file
Steps:
1. I should import the requests library
2. I should use requests library to fetch the results from the endpoint 'https://example.com/api' and save to a variable called response
3. I should access the variable response and parse the contents by decoding the JSON contents
4. I should open a local file in write mode and use the json library to dump the results.
```
In summary, we take a user request and break it down into smaller tasks.
Let’s see it in action with an example:
```Plain
Your job is to generate cat jokes and save in a file called 'cat_jokes.txt'. Be creative!
```
Here’s the output plan from the planner:
```Plain
[
  '1. Import the random library.',
  '2. Define a function to generate a random cat joke.',
  '3. Define a function to write the cat joke to a file.',
  '4. Call the write function after generating the joke.'
]
```
This helps guide the LLM into actually defining functions and defining the dependencies. Let’s see another example, which I copied and pasted from one of my older langchain agents (hence the weird instructions).
Task
```Plain
Your job is to plot an example chart using matplotlib. Create your own random data.
Run this code only when you're finished.
DO NOT add code and run into a single step.
```
Generated plan
```Plain
[
  '1. I should import the necessary libraries: matplotlib and random.',
  '2. I should define the data points for the chart.',
  '3. I should create a function to generate random data.',
  '4. I should create a function to plot the chart.',
  '5. I should call the plot function in the end.'
]
```
## The dependency tracker
Now that we have a plan, we can feed it into the next LLM so it can extract the required dependencies from the plan. Here’s what the prompt looks like:
```Plain
You're an AI master at understanding code.
You will be given a task plan, please helps us find the necessary python packages to install.
First, let's see an example of what we expect:
Plan:
1. import the requests library
2. use the requests library to retrieve the contents form
3. parse results into a dictionary
4. write dictionary tos a file
Requirements:
requests
END OF PLANNING FLOW
```
If we look at the matplotlib plan example, we’ll see that in the plan, the libraries are defined:
```Plain
'1. I should import the necessary libraries: matplotlib and random.'
```
Because the plan calls a random library, the dependency extractor will think it’s also a Python package to install.
So, we need some magic checking to discard wrong extracted requirements:
```Plain
dep = self.dependency_tracker.execute_task(plan="\n".join(plan))
for d in dep:
    d = d.replace("-", "").strip()
    if " " in d:
        d = d.split(" ")[0]
    if self.config.check_package_is_in_pypi:
        url = f'https://pypi.org/project/{d}'
        res = requests.get(url)
        if res.status_code != 200:
            pass
    if len(d) < 2 or d in DEPENDENCY_BLACKLIST:
        continue
    dependencies.append(d)
```
This tries to fix the format of the output, checks that the package exists and pypi and is not blacklisted (like `random` or `json` modules).
## The virtualenv manager
Once we parse the requirements, we can then install using our virtualenv manager while also creating a virtual environment for our code editor/executor:
```Plain
for dependency in dependencies:
    self.code_editor.add_dependency(dependency)
self.code_editor.create_env()
process = self.code_editor.install_dependencies()
if process.returncode != 0:
    logger.error("Dependency install failed for: %s", "\n".join(dependencies))
    attempt += 1
```
The `VirtualenvManager` itself is a very simple class that leverages the power of the `virtualenv` package:
```Plain
import logging
import string
import random
import os
import subprocess
from virtualenv import cli_run
logger = logging.getLogger(__name__)
RANDOM_NAME_LENGTH = 16

class VirtualenvManager:
    def __init__(self, name: str = "", base_path="/tmp") -> None:
        if not name:
            name = ""
            for _ in range(RANDOM_NAME_LENGTH):
                population = string.ascii_letters + string.digits
                char = random.sample(population, k=1)
                name += char[0]
        self.name = name
        self.path = os.path.join(base_path, name)
        self.python_interpreter = os.path.join(self.path, "bin/python3")
        self.dependencies = []
    def add_dependency(self, dependency):
        logger.info("Adding dependency '%s' ", dependency)
        self.dependencies.append(dependency)
    def create_env(self):
        logger.info("Creating virtualenv at path '%s' ", self.path)
        cli_run([self.path], setup_logging=False)
    def install_dependencies(self):
        logger.info("Installing dependencies")
        process = subprocess.run(
            [self.python_interpreter, "-m", "pip", "install"] + self.dependencies,
            capture_output=True,
        )
        return process
```
## Generate code
We’re now ready to run the coder prompt to generate code. Note that this prompt is of very low quality! Yet it seems to perform reasonably well within the context.
```Plain
You're an expert python programmer AI Agent. You solve problems by using Python code,
and you're capable of providing code snippets, debugging and much more, whenever it's asked of you. You are usually given
an existing source code that's poorly written and contains many duplications. You should make it better by refactoring and removing errors.
You should fulfill your role in the example below:
Objective: Write a code to print 'hello, world'
Plan: 1. Call print function with the parameter 'hello, world'
Source Code:
import os
import os
import os
print('hello, world')
Thought: The code contains duplication and an unused import. Here's an improved version.
New Code:
print('hello, world')
Objective:
Notice that you once you finish the subtask, you should add the word 'Objective:' in a new line,
like in the example above.
You should ALWAYS output the full code.
```
Notice we inject the original task as the “Objective” and the “Plan” generated by the Planner. This helps us ensure that the coder won’t try something wildly different that requires different packages.
With the output, we can save this to a local file with our code editor tool, and attempt to trim away markdown tags, since the model sometimes wraps the code inside `python` tags.
```Plain
self.code_editor.overwrite_code(new_code)
_trim_md(self.code_editor)
```
## Linting and sampling
Now that we have a source code, we can use the linter to see how bad our code is. Here’s the relevant snippet:
```Plain
from pylint import epylint as lint
(...)

# Run pylint
(pylint_stdout, _) = lint.py_run(self.code_editor.filename, return_std=True)
pylint_stdout = pylint_stdout.getvalue()
pylint_lines = pylint_stdout.splitlines()
# Extract pylint score
linting_score_str = None
for line in pylint_lines:
    if PYLINT_SCORE_SUBSTRING in line:
        split_1 = line.split(PYLINT_SCORE_SUBSTRING)[1]
        linting_score_str = split_1.split("/")[0]
# If we fail to extract core, we probably have a syntax error, so we set score to -1
if not linting_score_str:
    logger.warn(f"Failed to parse pylint score from stdout: {pylint_stdout}")
    score = -1  # Code probably does not compile
# Else finish converting the score to float
else:
    score = float(linting_score_str)
```
As a result, we see the following in the logs:
```Plain
2023-05-18 14:34:35,523 [INFO] Coding sample: 0 (temperature: 0.0)
import matplotlib.pyplot as plt
import random
def generate_data():
    return [random.randint(1, 10) for i in range(10)]
def plot_chart(data):
    plt.plot(data)
    plt.xlabel('X-axis')
    plt.ylabel('Y-axis')
    plt.title('Example Chart')
    plt.show()
data = generate_data()
plot_chart(data)
2023-05-18 14:34:40,259 [INFO] Applying linter...
2023-05-18 14:34:40,558 [INFO] Sample score: 0.83
2023-05-18 14:34:44,189 [INFO] <class 'list'>
```
By default, we sample three code samples. We do this by increasing the temperature each time and regenerating a new plan. Notice that we don’t retry to install dependencies to keep things simple, though this could yield errors (though then the sample would be filtered out with a -1).
Here’s our second plan/sample being generated:
```Plain
2023-05-18 14:34:55,841 [INFO] Parsed plan: ['1. Import the necessary libraries: matplotlib and random.', '2. Define the data points as a list of tuples. Each tuple contains x and y values.', '3. Define the chart type as a string.', '4. Create a figure and a subplot.', '5. Use the scatter function to plot the data points.', '6. Use the xlabel and ylabel functions to label the axes.', '7. Use the title function to title the chart.', '8. Show the chart using the show function.', '9. Clean up the figure and the subplot.']
2023-05-18 14:34:55,842 [INFO] Coding sample: 2 (temperature: 0.2)
2023-05-18 14:35:01,783 [INFO]
import matplotlib.pyplot as plt
import random
def generate_data():
    return [random.randint(1, 10) for i in range(10)]
def plot_chart(data):
    plt.plot(data)
    plt.xlabel('X-axis')
    plt.ylabel('Y-axis')
    plt.title('Example Chart')
    plt.show()
    plt.text(50, 90, 'Random Data', fontsize=16)
data = generate_data()
plot_chart(data)
2023-05-18 14:35:01,783 [INFO] Applying linter...
2023-05-18 14:35:02,096 [INFO] Sample score: 1.54
2023-05-18 14:35:02,096 [INFO] Score of highest sample: 1.54
```
So the code is almost the same, but for some reason, pylint decided it’s a higher score. The third sample is just a repetition with the same score.
## Executing the code
All right, now that we have the samples, we sort and take the one with the highest score.
Then we save it again into the file and execute it!
```Plain
coding_samples.sort(key=lambda x: x["score"], reverse=True)
highest_score = coding_samples[0]
logger.info("Score of highest sample: %s", highest_score["score"])
self.code_editor.overwrite_code(highest_score["code"])
if not self.config.execute_code:
    return self.code_editor.display_code()
result = self.code_editor.run_code()
```
In this example, the execution was successful (it’s not always the case)!
```Plain
CompletedProcess(args=['/tmp/2Gh5EfN6MNxahZcI/bin/python3', 'persistent_source.py'], returncode=0, stdout=b'', stderr=b'/home/<USER>/code-it/persistent_source.py:10: UserWarning: Matplotlib is currently using agg, which is a non-GUI backend, so cannot show the figure.\n  plt.show()\n') b'/home/<USER>/code-it/persistent_source.py:10: UserWarning: Matplotlib is currently using agg, which is a non-GUI backend, so cannot show the figure.\n  plt.show()\n'
2023-05-18 14:35:02,439 [INFO] Finished generating code!
2023-05-18 14:35:02,439 [INFO] Source code is functional!
```
# Conclusion and Next Steps
So this sketch of a library shows that effectively leveraging LLMs for complex tasks is not as easy as it first seems. For this reason, libraries like `guidance` have been developed: [https://github.com/microsoft/guidance](https://github.com/microsoft/guidance)
I think this `code-it` took a similar direction, but not as well structured and flexible as the Microsoft library.
A good next step would be to integrate `guidance` into `code-it` or try a new, completely new approach using it.
It would be really interesting to index local code with embeddings, too, so that this package could reuse local snippets to aid in the code generation.
One thing that did not work as well as I had hoped is the sampling of different samples based on the linter score, so maybe there are better ways of:
1. Increasing the diversity of the samples
2. Using a more effective code scorer
3. Figuring out a way to apply the linter in the code instead of sampling
Still, I think there’s a lot of potential in what can be achieved with these models — time will tell how good these automated coding tools will become :).