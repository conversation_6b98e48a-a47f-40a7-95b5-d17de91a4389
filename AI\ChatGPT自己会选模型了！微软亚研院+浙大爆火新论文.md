---
DocFlag:
  - ToBeTested
Updated: 2023-04-04T21:10
tags:
  - AI->-Programming
  - AI->-ToDO
Created: 2023-04-04T10:59
---
### Datawhale干货 **项目：HuggingGPT，来源：新智元**
![[640]]
### **【新智元导读】**「贾维斯」已来！微软亚研院和浙大推出了一个大模型协作系统HuggingGPT，让ChatGPT协调HF社区模型，处理各种多模态任务能力超强。
ChatGPT引爆的AI热潮也「烧到了」金融圈。
近来，彭博社的研究人员也开发了一个金融领域的GPT——Bloomberg GPT，有500亿参数。
GPT-4的横空出世，让许多人浅尝到了大型语言模型的强大能力。
然而，OpenAI不open。业界许多人纷纷开始做GPT的克隆，而且许多ChatGPT平替的模型都是基于已经开源的模型上构建，尤其是Meta开源的LLMa模型。
比如，斯坦福的草泥马（Alpaca）、UC伯克利联手CMU、斯坦福等骆马（Vicuna），初创公司Databricks的Dolly等等。
针对不同任务和应用构建的各种类ChatGPT的大型语言模型，在整个领域呈现出百家争鸣之势。
那么问题来了，研究者如何选择合适的模型，甚至是多个模型，去完成一项复杂的任务呢？
近日，微软亚洲研究院和浙江大学的研究团队，发布了一个大模型协作系统HuggingGPT。
论文地址：https://arxiv.org/pdf/2303.17580.pdf
HuggingGPT利用ChatGPT作为控制器，连接HuggingFace社区中的各种AI模型，来完成多模态复杂任务。
这意味着，你将拥有一种超魔法，通过HuggingGPT，便可拥有多模态能力，文生图、文生视频、语音全能拿捏了。
HuggingGPT搭桥
研究者指出解决大型语言模型（LLMs）当前的问题，可能是迈向AGI的第一步，也是关键的一步。

> 因为当前大型语言模型的技术仍然存在着一些缺陷，因此在构建 AGI 系统的道路上面临着一些紧迫的挑战。
- 受限于文本生成的输入和输出形式，当前LLMs缺乏处理复杂信息（如视觉和语音）的能力；
- 在实际应用场景中，一些复杂任务通常由多个子任务组成，因此需要多个模型的调度和协作，这也超出了语言模型的能力范围；
- 对于一些具有挑战性的任务，LLMs在零样本或少样本设置下表现出优异的结果，但它们仍然比一些专家弱（如微调模型）。
为了处理复杂的人工智能任务，LLMs应该能够与外部模型协调，以利用它们的能力。因此，关键点在于如何选择合适的中间件来桥接LLMs和AI模型。研究者发现，每个AI模型都可以通过总结其模型功能表示为一种语言形式。由此，便引入了一个概念，「语言是LLMs，即ChatGPT，连接人工智能模型的通用接口」。通过将AI模型描述纳入提示中，ChatGPT可以被视为管理人工智能模型的大脑。因此，这一方法可以让ChatGPT能够调用外部模型，来解决实际任务。简单来讲，HuggingGPT是一个协作系统，并非是大模型。它的作用就是连接ChatGPT和HuggingFace，进而处理不同模态的输入，并解决众多复杂的人工智能任务。所以，HuggingFace社区中的每个AI模型，在HuggingGPT库中都有相应的模型描述，并将其融合到提示中以建立与ChatGPT的连接。随后，HuggingGPT将ChatGPT作为大脑来确定问题的答案。到目前为止，HuggingGPT已经围绕ChatGPT在HuggingFace上集成了数百个模型，涵盖了文本分类、目标检测、语义分割、图像生成、问答、文本到语音、文本到视频等24个任务。实验结果证明，HuggingGPT拥有处理多模态信息和复杂人工智能任务的能力。**四步工作流程**HuggingGPT整个工作流程可以分为如下四个阶段：- 任务规划：ChatGPT解析用户请求，将其分解为多个任务，并根据其知识规划任务顺序和依赖关系- 模型选择：LLM根据HuggingFace中的模型描述将解析后的任务分配给专家模型- 任务执行：专家模型在推理端点上执行分配的任务，并将执行信息和推理结果记录到LLM中- 响应生成：LLM总结执行过程日志和推理结果，并将摘要返回给用户
多模态能力，有了
**实验设置**实验中，研究者采用了gpt-3.5-turbo和text-davinci-003这两种GPT模型的变体作为大型语言模型（LLMs），这些模型可以通过OpenAI API公开访问。为了使LLM的输出更加稳定，我们将解码温度设置为0。同时，为了调整LLM的输出以使其符合预期格式，我们在格式约束上设置了logit_bias为0.1。研究人员在如下表格中提供了为任务规划、模型选择和反应生成阶段而设计的详细提示，其中{{variable}}表示在提示被输入LLM之前，需要用相应的文本填充域值。研究人员在广泛的多模态任务上测试了HuggingGPT。在ChatGP和专家模型的配合下，HuggingGPT可以解决语言、图像、音频和视频等多种模式的任务，包含了检测、生成、分类和问题回答等多种形式的任务。虽然这些任务看起来很简单，但掌握HuggingGPT的基本能力是解决复杂任务的前提条件。比如，视觉问答任务：文本生成：文生图：HuggingGPT可以整合多个输入的内容来进行简单的推理。可以发现，即使有多个任务资源，HuggingGPT也能将主要任务分解成多个基本任务，最后整合多个模型的推理结果，得到正确答案。此外，研究人员通过测试评估了HuggingGPT在复杂任务情况下的有效性。就HuggingGPT处理多项复杂任务的能力进行了展示。当处理多个请求的时候，可能包含多个隐含任务或者需要等多方面的信息，这时依靠一个专家模型来解决是不够的。而HuggingGPT可以通过任务规划组织多个模型的协作。一个用户请求中可能明确包含多个任务：下图展示了HuggingGPT在多轮对话场景下应对复杂任务的能力。用户将一个复杂的请求分成几个步骤，通过多轮请求达到最终目标。结果发现，HuggingGPT可以通过任务规划阶段的对话情境管理来跟踪用户请求的情境状态，并且可以很好地解决用户提到的请求资源以及任务规划。
「贾维斯」开源
目前，这一项目已经在GitHub上开源，但是代码并没有完全公布。有趣的是，研究者给这个项目命名为《钢铁侠》中的贾维斯，无敌AI这就来了。

> JARVIS：一个连接LLMs和ML社区的系统
顺便提一句，HuggingGPT需要有了OpenAI的API才可以使用。
网友：研究的未来
JARVIS / HuggingGPT就像此前Meta提出的Toolformer一样，都在充当着连接器的作用。甚至，包括ChatGPT plugins也是如此。网友称，「我强烈怀疑第一个人工通用智能（AGI）的出现将比预期更早。它将依靠「胶水」人工智能，能够智能地将一系列狭义人工智能和实用工具粘合在一起。我获得了访问ChatGPT的Wolfram插件的权限，这使它一夜之间从数学菜鸡变成了数学天才。当然，这只是一个小步骤，但却预示着未来的发展趋势。我预测，在接下来的一年左右，我们将看到一种人工智能助手，它与数十个大型语言模型（LLMs）及类似工具相连，而终端用户只需向其助手发出指令，让其为他们完成任务。这个科幻般的时刻即将到来。还有网友称，这就是未来的研究方法。GPT在一大堆工具面前，知道如何使用它们了。参考资料：https://twitter.com/johnjnay/status/1641609645713129473https://news.ycombinator.com/item?id=35390153**一起“****点****赞”****三连****↓**