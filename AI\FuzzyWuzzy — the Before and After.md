---
Updated: 2024-02-14T08:33
tags:
  - AI->-Embedding
  - AI->-Programming
Created: 2024-02-14T08:33
---
[![](https://miro.medium.com/v2/resize:fit:1017/1*3794j8ANpJyGcmHF-VnQaw.png)](https://miro.medium.com/v2/resize:fit:1017/1*3794j8ANpJyGcmHF-VnQaw.png)
## ==FuzzyWuzzy — the Before and After====  
  
====FuzzyWuzzy - 之前和之后==
## ==About data preprocessing, choosing the adequate FuzzyWuzzy function, and working with the results====  
  
====关于数据预处理、选择合适的 FuzzyWuzzy 函数和处理结果==
==[==
[![](https://miro.medium.com/v2/resize:fill:88:88/1*IXcoooR9WpE8XCrYp1SsPg.jpeg)](https://miro.medium.com/v2/resize:fill:88:88/1*IXcoooR9WpE8XCrYp1SsPg.jpeg)
==](https://naomikriger.medium.com/?source=post_page-----c3661ea62ef8--------------------------------)[==
[![](https://miro.medium.com/v2/resize:fill:48:48/1*CJe3891yB1A1mzMdqemkdg.jpeg)](https://miro.medium.com/v2/resize:fill:48:48/1*CJe3891yB1A1mzMdqemkdg.jpeg)
==](https://towardsdatascience.com/?source=post_page-----c3661ea62ef8--------------------------------)==
[![](https://miro.medium.com/v2/resize:fit:700/1*3794j8ANpJyGcmHF-VnQaw.png)](https://miro.medium.com/v2/resize:fit:700/1*3794j8ANpJyGcmHF-VnQaw.png)
==picture generated by AI with== [==simplified==](http://app.simplified.com/)==  
  
====由人工智能生成的简化图片==
==In the previous== [==article==](https://medium.com/naomikriger/string-comparison-is-easy-with-fuzzywuzzy-library-611cc1888d97)==, I introduced FuzzyWuzzy library which calculates a 0–100 matching score for a pair of strings. The different FuzzyWuzzy functions enable us to choose the one that would most accurately fit our needs.====  
  
====在上一篇文章中，我介绍了 FuzzyWuzzy 库，它可以为一对字符串计算 0-100 的匹配分数。通过不同的 FuzzyWuzzy 函数，我们可以选择最符合我们需要的函数。==
==However, conducting a successful project is much more than just calculating scores. We need to clean the data before we start working on it, choose the best method to calculate our scores, learn how to work not only with a pair of strings but with tables of data, and eventually know how to use the scores we received to make the most out of our results.====  
  
====然而，一个成功的项目不仅仅是计算分数那么简单。我们需要在开始工作前清理数据，选择计算分数的最佳方法，学习如何不仅处理一对字符串，而且处理数据表，并最终知道如何使用我们得到的分数来最大限度地利用我们的结果。==
==So, without further ado, let’s dive into some best practices we should be familiar with.====  
  
====所以，话不多说，让我们深入了解一些我们应该熟悉的最佳实践。==
## ==Using a Table With Pandas In order To Compare Multiple Strings====  
  
====使用 Pandas 表来比较多个字符串==
==As discussed earlier, FuzzyWuzzy functions calculate matching scores for two strings. But when working with “real life” data, we will probably want to compare at least two sets of strings. This means working with a table, or when speaking in Pandas terms — working with DataFrames.====  
  
====如前所述，FuzzyWuzzy 函数计算两个字符串的匹配分数。但在处理 "现实生活 "中的数据时，我们可能希望至少比较两组字符串。这意味着需要使用表格，或者用 Pandas 术语来说，需要使用数据帧。==
==A good table will resemble this one:====  
  
====一个好的表格应与此表相似：==
[![](https://miro.medium.com/v2/resize:fit:700/0*AS-cveFs4yX3-Pq1)](https://miro.medium.com/v2/resize:fit:700/0*AS-cveFs4yX3-Pq1)
==The table above contains two comparison columns, each with a relevant header, where the strings to be compared are in parallel rows.====  
  
====上表包含两个比较列，每个比较列都有一个相关的标题，要比较的字符串位于并列行中。==
==Given such a dataset, we can read the table to a DataFrame using a relevant function. The example below reads directly from a CSV, but if you are interested in using other formats — you can check out the following== [==documentation==](https://pandas.pydata.org/pandas-docs/stable/reference/io.html)==.====  
  
====有了这样一个数据集，我们就可以使用相关函数将表格读取为 DataFrame。下面的示例直接从 CSV 读取数据，但如果你对使用其他格式感兴趣，可以查看下面的文档。==
==>>> my_data_frame = pd.read_csv("====**my_folder/my_file_name.csv"**====)==
## ==Data Preprocessing — Cleaning the Data Before Analysis====  
  
====数据预处理--分析前的数据清理==
==Before we choose our FuzzyWuzzy function and start comparing strings, we want to clean the data to ensure that our results will be as accurate as possible.====  
  
====在我们选择 FuzzyWuzzy 函数并开始比较字符串之前，我们需要清理数据，以确保结果尽可能准确。==
==Cleaning the data means removing irrelevant strings, and thus improving the functions’ performance.====  
  
====清理数据意味着删除无关字符串，从而提高函数的性能。==
==For example, let’s assume we compare strings of two addresses, where one address is “Joe Boulevard” and the other is “Jule Boulevard”. The matching score will be relatively high, but mostly due to the existence of “Boulevard” in both strings. Removing it and recalculating will result in a much lower matching score:====  
  
====例如，假设我们比较两个地址字符串，其中一个地址是 "Joe Boulevard"，另一个地址是 "Jule Boulevard"。匹配得分会相对较高，但主要是因为两个字符串中都有 "Boulevard"。去掉 "Boulevard "后重新计算，匹配得分会低很多：==
==>>> fuzz.ratio("====**Joe Boulevard"**====, "====**Jule Boulevard"**====)====  
  
====89====  
  
====>>> fuzz.ratio("====**Joe"**====, "====**Jule"**====)====  
  
====57==
==The type of cleaning required for your data depends on your domain.====  
  
====数据所需的清理类型取决于您的领域。====  
  
====We saw an example of the required cleaning for addresses. Similarly, when comparing phone numbers — we will probably want to remove parentheses and dashes that have no added value. It is also recommended to normalize all of your strings to lowercase since some FuzzyWuzzy functions treat differently-capitalized letters as different strings.====  
  
====我们看到过一个地址需要清理的例子。同样，在比较电话号码时，我们可能需要删除没有附加值的括号和破折号。我们还建议将所有字符串规范化为小写，因为某些 FuzzyWuzzy 函数会将不同大小写的字母视为不同的字符串。====  
  
====So, look at your data, and decide what should be modified in order to make it clean and ready for processing.====  
  
====因此，请查看您的数据，并决定应修改哪些内容，以便使数据干净整洁，并为处理做好准备。==
## ==Data Pre-Processing — Let’s Get Technical====  
  
====数据预处理--让我们从技术角度出发==
==Now, let’s define a function with the relevant logic, and iteratively run it on each of the relevant columns in the DataFrame.====  
  
====现在，让我们定义一个具有相关逻辑的函数，并在 DataFrame 中的每一列上反复运行它。==
==** The example below was simplified in order to keep the explanation clear. For best results, it is recommended to use regular expressions (regex) which is beyond the scope of this article. Note that== ==_strings_to_remove_====, in its current form, may lead to imperfect results after the cleanup.====  
  
====** 下面的示例经过简化，以保持解释清晰。为获得最佳效果，建议使用正则表达式 (regex)，这超出了本文的讨论范围。请注意，strings_to_remove 目前的形式可能会导致清理后的结果不完美。==
==>>> strings_to_remove = ["== ==**ave "**====,== ==**" ave. "**====, "====**avenue"**====, "== ==**lane "**====,====  
  
===="== ==**ln "**====, "====**blvd"**====, "====**boulevard"**====, "== ==**rd. "**====, "====**road"**====, "====**street"**====, "== ==**st. "**====,====  
  
====**" str "**====,== ==**" dr. "**====, "====**drive"**====,== ==**" apt "**====, "====**apartment"**====, "====**valley"**====, "====**city"**====,== ==**"."**====,== ==**","**====]==
==>>> comparison_table =====  
  
====comparison_table.astype(str).apply(lambda x: x.str.lower())==
==>>> for current_string in strings_to_remove:====  
  
====comparison_table = comparison_table.astype(str).apply(====  
  
====lambda x: x.str.replace(current_string,== ==**' '**====))==
==>>> comparison_table = comparison_table.astype(str).apply(====  
  
====lambda x: x.str.replace(====**' +'**====,== ==**' '**====))==
==And — voilà! 然后--瞧！==
[![](https://miro.medium.com/v2/resize:fit:664/1*s_rHGH1hVZkVrjuuBn9_uA.png)](https://miro.medium.com/v2/resize:fit:664/1*s_rHGH1hVZkVrjuuBn9_uA.png)
## ==Adding a Score Column And Comparing====  
  
====添加分数列并进行比较==
==All that’s left now is to add an empty column named ‘score’ to the DataFrame, calculating the matching scores using our chosen FuzzyWuzzy function,====  
  
====现在只需在 DataFrame 中添加名为 "score "的空列，并使用我们选择的 FuzzyWuzzy 函数计算匹配分数、====  
  
====and populating the DataFrame with those scores.====  
  
====并用这些分数填充 DataFrame。==
==Here is an example of how to do that -====  
  
====下面举例说明如何做到这一点==
==>>> comparison_table[====**"score"**====] === ==**""**====>>> comparison_table[====**'score'**====] =====  
  
====comparison_table.apply(lambda row:====  
  
====fuzz.token_set_ratio(row[====**'col_a_addresses'**====], row[====**'col_b_addresses'**====]),axis=1)==
==Let’s compare the results with those we would have received if we had run the FuzzyWuzzy function on an unprocessed DataFrame:====  
  
====让我们将结果与在未处理的 DataFrame 上运行 FuzzyWuzzy 函数时得到的结果进行比较：==
==Before Cleaning - 清洁前==
[![](https://miro.medium.com/v2/resize:fit:700/1*iJ4Kj3mw_MdoBPOdZMh8Wg.png)](https://miro.medium.com/v2/resize:fit:700/1*iJ4Kj3mw_MdoBPOdZMh8Wg.png)
==After Cleaning - 清洁后==
[![](https://miro.medium.com/v2/resize:fit:645/1*Qg0ZhyBQMpyU9_sTq8G7oA.png)](https://miro.medium.com/v2/resize:fit:645/1*Qg0ZhyBQMpyU9_sTq8G7oA.png)
==**_So, what actually happened after cleaning the data?====  
  
====那么，清理数据后究竟发生了什么？====  
  
====_**The matching scores became more accurate — either increased or decreased based on the cleaning.====  
  
====匹配分数变得更加准确--根据清洁情况或增或减。==
- ==_Let’s look at row 3 where the score decreased after cleaning._====  
      
    ====_让我们看看第 3 行，该行的分数在清洁后有所下降。_====  
      
    ====In this case — the word “Lane” which appeared on both addresses before cleaning, falsely increased the matching score. But after removing it, we were able to see the addresses are not that similar.====  
      
    ====在本例中，"Lane "一词在清理前出现在两个地址上，错误地增加了匹配分数。但在删除该词后，我们可以发现这两个地址并不那么相似。==
- ==_Let’s look at row 9 where the score increased after cleaning._====  
      
    ====_让我们看看第 9 行，该行的得分在清洁后有所提高。_====  
      
    ====While “Lane” and “ln.” have the same meaning, they are different strings with different capitalization.====  
      
    ====虽然 "Lane "和 "ln. "含义相同，但它们是不同的字符串，大小写也不同。====  
      
    ====Once cleaning the noise out — we were able to receive a much better score, that more accurately reflects the similarity level between those strings.====  
      
    ====清除噪音后，我们就能得到一个更好的分数，更准确地反映出这些字符串之间的相似程度。==
- ==It is also interesting to see that the cleaned strings in row 9 are not identical. ”85" appears only in== ==_col_b_addresses_== ==yet the matching score is 100. Why? Since the strings are “close enough” to be determined as a perfect match by the algorithm. A decision that would have likely been the same if a human being had to make it.====  
      
    ====同样有趣的是，第 9 行中经过清理的字符串并不完全相同。"85 "只出现在 col_b_addresses 中，但匹配得分却是 100。为什么呢？因为这些字符串 "足够接近"，可以被算法判定为完全匹配。如果由人工来做这个决定，结果很可能是一样的。==
## ==Choosing a FuzzyWuzzy Function — In a Nutshell====  
  
====选择 FuzzyWuzzy 函数 - 简要介绍==
==One method to choose the best FuzzyWuzzy function to work with is based on the logic/purpose of the different functions and determining which function seems most relevant for your purposes.====  
  
====选择最佳 FuzzyWuzzy 函数的方法之一是根据不同函数的逻辑/目的，确定哪个函数似乎最符合您的目的。==
==However, if you cannot decide which function may retrieve the most accurate results — you can conduct a small research to determine what to work with.====  
  
====但是，如果您无法确定哪种功能可以检索到最准确的结果，您可以开展一项小型研究，以确定使用哪种功能。==
==The method I would recommend using would be to take a sample of your data set and run each of the relevant functions against it. Then, for each of the results — manually decide if the value in each row is true positive / false positive / true negative / false negative.====  
  
====我建议使用的方法是，从数据集中抽取一个样本，然后针对它运行每个相关函数。然后，针对每个结果，手动判断每一行中的值是否为真阳性/假阳性/真阴性/假阴性。==
==Once this is done, you can either choose where your TP/FP rate is most satisfactory, or go ahead and calculate accuracy* and sensitivity* as well, and use these values to make your decision.====  
  
====完成后，您可以选择最满意的 TP/FP 率，或者继续计算准确度*和灵敏度*，并使用这些值做出决定。====  
  
====For each project, our goals may differ, and the false positive / true negative rates we are willing to take will be different.====  
  
====对于每个项目，我们的目标可能不同，我们愿意接受的假阳性/真阴性率也会不同。==
==* Both accuracy and sensitivity are used in Data-Science and beyond the scope of this article. The formulas for each of these can be found online.====  
  
====* 准确度和灵敏度均用于数据科学，不在本文讨论范围之内。这两个参数的计算公式可在网上找到。==
## ==Choosing A Threshold Score — In a Nutshell====  
  
====选择阈值分数--简而言之==
==My pair of strings returned a matching score of 82. Is it good? Is it bad?====  
  
====我的这对琴弦的匹配分数是 82 分。是好？不好吗？==
==The answer depends on our target, and there are many relevant questions to ask, such as: are we interested in strings that are very similar to one another, or in different ones? What is the maximal false-positive rate we are willing to accept? What is the minimal true-positive rate we want to work with?====  
  
====答案取决于我们的目标，有许多相关问题需要提出，例如：我们对彼此非常相似的字符串感兴趣，还是对不同的字符串感兴趣？我们愿意接受的最大假阳性率是多少？我们希望使用的最小真阳性率是多少？==
==For the same set of strings, we can come up with two different threshold scores — minimal score for similar strings (for example 85), and maximal score for different strings (for example 72).====  
  
====对于同一组字符串，我们可以得出两种不同的阈值分数--相似字符串的最小分数（例如 85）和不同字符串的最大分数（例如 72）。====  
  
====There can be a whole range between these threshold scores that will be doomed as “inconclusive”.====  
  
====在这些临界值之间，可能会有一个很大的范围，但这注定是 "不确定的"。==
==There are different methods to define a threshold score, and we won’t dig into them in this article. I will, however, mention that choosing a threshold score will require some manual work, similar to the one mentioned above regarding how to choose the best FuzzyWuzzy function to work with — taking a sample set of strings with final scores, determining true-positive and false-positive for the results, and eventually deciding where our threshold stands.====  
  
====定义阈值分数的方法有很多种，本文将不再深入探讨。不过，我想说的是，选择阈值得分需要一些手工操作，就像上文提到的如何选择最佳 FuzzyWuzzy 函数一样--获取一组具有最终得分的字符串样本，确定结果的真阳性和假阳性，并最终决定我们的阈值位置。==
==Using FuzzyWuzzy for strings comparison, as well as pre-processing the data, and eventually analyzing the results is a fascinating work. There is always more to do, and different ways to improve the process.====  
  
====使用 FuzzyWuzzy 进行字符串比较、预处理数据并最终分析结果，是一项令人着迷的工作。总有更多的工作要做，也总有不同的方法来改进这一过程。==
==In this article, we explored some of the practices that make this process useful and comfortable.====  
  
====在本文中，我们探讨了使这一过程变得有用和舒适的一些做法。==
==I hope you found this series useful and insightful, and that now you have better tools for your next data-oriented project.====  
  
====希望本系列文章对你有所帮助和启发，也希望你在下一个面向数据的项目中拥有更好的工具。==