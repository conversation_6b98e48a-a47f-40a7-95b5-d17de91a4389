---
Updated: 2023-04-13T20:20
tags:
  - AI->-News
Created: 2023-04-13T20:06
---
![[v2-169582a5459058913eb7cd8d0f11708f_1440w.jpg]]

> 金磊 鱼羊 萧箫 发自 凹非寺
图像生成领域，看来又要变天了。
就在刚刚，OpenAI开源了**比扩散模型更快、性能更好的一致性模型**：
无需对抗训练，就能生成高质量图片！
这个重磅消息一经发出，立刻引爆学术圈。
![[v2-b17f2765c32b5b641edfe2542693c14c_720w.webp]]
虽说论文本身在3月份就已低调发布，但当时大伙儿普遍认为它只是个OpenAI的前沿研究，并不会真正将细节公开。
没想到这次直接来了个**开源**。有网友立刻上手实测了一波效果，发现只需要3.5秒左右就能生成64张左右256×256的图像：

> 游戏结束！
这是这位网友生成的图像效果，看起来还不错：
还有网友调侃称：这次OpenAI终于Open了！
值得一提的是，论文一作OpenAI科学家**宋飏**，是一位清华校友，16岁就通过领军计划进入清华数理基础科学班求学。
一起来看看这次OpenAI开源了一项怎样的研究。
## 开源了一个怎样的重磅研究？
作为一个图像生成AI，一致性模型（Consistency Model）最大的特点在于**快**又好。
相比扩散模型，它主要有两大优势：
其一，无需对抗训练（adversarial training），就能直接生成高质量的图像样本。
其二，相比扩散模型可能需要几百甚至上千次迭代，一致性模型只需要**一两步**就能搞定多种图像任务——
包括上色、去噪、超分等，都可以在几步之内搞定，而不需要对这些任务进行明确训练。（当然，如果进行少样本学习的话，生成效果也会更好）
所以一致性模型究竟是如何实现这种效果的？
从原理来看，一致性模型的诞生与ODE（常微分方程）式生成扩散模型有关。
图中可见，ODE会先一步步将图片数据转换成噪声，随后再进行一个逆向求解，从噪声中学习生成图像。
而就在这个过程中，作者们试图将ODE轨迹上的任何点（如Xt、Xt和Xr）映射到它的原点（如X0）进行生成建模。
随后，这个映射的模型被命名为一致性模型，因为它们的输出都是同一轨迹上的同一点：
基于这种思路，一致性模型不需要再经过漫长的迭代，才能生成一个相对质量比较高的图像，而是能做到**一步生成**。
下图是一致性模型（CD）和扩散模型（PD）在图像生成指标FID上的对比。
其中，PD是去年斯坦福和谷歌大脑提出的一种最新扩散模型方法渐进式蒸馏（progressive distillation）的简称，CD（consistency distillation）则是一致性蒸馏方法。
可以看出，几乎在所有数据集上，一致性模型的图像生成效果都要比扩散模型更好，唯一的例外是256×256的房间数据集上：
除此之外，作者们也将扩散模型、一致性模型和GAN等模型在其他各种数据集上进行了对比：
不过也有网友提到，这次开源的AI一致性模型，能生成的图像还是太小：

> 很难过，这次开源的版本生成的图像还是太小了，要是能给出生成更大图像的开源版本，肯定会非常让人兴奋。
也有网友猜测，可能只是OpenAI还没训练出来。不过可能训练出来了我们也不一定能搞到代码（手动狗头）。
不过对于这项工作的意义，TechCrunch表示：

> 你若是有一堆GPU，那用扩散模型在一两分钟内迭代1500多次，生成图片的效果当然是极好的。
期待OpenAI会开源一波分辨率更高的图像生成AI~
## 清华校友宋飏一作
论文一作宋飏，目前是OpenAI的研究科学家。
他14岁时，曾以17位评委全票通过的成绩，入选“清华大学新百年领军计划”。在次年高考中，他又成为连云港市理科状元，顺利考入清华。
2016年，宋飏从清华大学数理基础科学班毕业，此后赴斯坦福深造。2022年，宋飏获斯坦福计算机科学博士学位，而后加入OpenAI。
在博士期间，他的一作论文“Score-Based Generative Modeling through Stochastic Differential Equations”还获得过ICLR 2021的杰出论文奖。
根据其个人主页信息，从2024年1月开始，宋飏将正式加入加州理工学院电子系和计算数学科学系，担任助理教授。
项目地址：[github.com/openai/consi](https://link.zhihu.com/?target=https%3A//github.com/openai/consistency_models)
论文地址：[arxiv.org/abs/2303.0146](https://link.zhihu.com/?target=https%3A//arxiv.org/abs/2303.01469)
参考链接：  
[1]  
[twitter.com/alfredplpl/](https://link.zhihu.com/?target=https%3A//twitter.com/alfredplpl/status/1646217811898011648)  
[2]  
[twitter.com/_akhaliq/st](https://link.zhihu.com/?target=https%3A//twitter.com/_akhaliq/status/1646168119658831874)
—完—
[@量子位](https://www.zhihu.com/org/liang-zi-wei-48/columns) · 追踪AI技术和产品新动态
深有感触的朋友，欢迎赞同、关注、分享三连վ'ᴗ' ի ❤