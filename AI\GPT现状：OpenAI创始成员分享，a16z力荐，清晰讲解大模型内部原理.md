---
DocFlag:
  - Reference
Updated: 2023-05-30T17:17
tags:
  - AI->-Theory
Created: 2023-05-30T13:39
---
[![](https://mmbiz.qpic.cn/mmbiz_jpg/V08icaMk7MVZ2PanF5UeWLqPkysJnia4USjw671K8p58UbmdhMLGMDNJVcCiaGH6vohUKW1ribF5nEp69Hyjbvna4g/0?wx_fmt=jpeg)](https://mmbiz.qpic.cn/mmbiz_jpg/V08icaMk7MVZ2PanF5UeWLqPkysJnia4USjw671K8p58UbmdhMLGMDNJVcCiaGH6vohUKW1ribF5nEp69Hyjbvna4g/0?wx_fmt=jpeg)
---
![[Notion/AI/GPT现状：OpenAI创始成员分享，a16z力荐，清晰讲解大模型内部原理/attachments/640|640]]
OpenAI的创始成员，前特斯拉高级 AI 总监 Andrej <PERSON>rpthy 刚在微软 Build 2023 开发者大会上做了专题演讲：State of GPT（GPT 的现状）。
![[640 1]]
在这个朴实无华的题目之下，Andrej带来的是一场超级精彩的分享。他详细介绍了如何从GPT基础模型一直训练出ChatGPT这样的助手模型（assistant model）。作者不曾在其他公开视频里看过类似的内容，**这或许是OpenAI官方第一次详细阐述其大模型内部原理和RLHF训练细节。**难能可贵的是，Andrej不仅深入了细节， 还高屋建瓴的抽象了大模型实现中的诸多概念，牛人的洞察就是不一样。比如，Andrej非常形象的把当前LLM大语言模型比喻为人类思考模式的系统一（快系统），这是相对于反应慢但具有更长线推理的系统二（慢系统）而言。这只是演讲里诸多闪光点的其中一个。并且，Andrej真的有当导师的潜力，把非常技术的内容讲得深入浅出，而又异常透彻。这个演讲完全可以让非专业人士也能理解，并且，认真看完演讲后会有一种醍醐灌顶的感觉。这场主题演讲是如此精彩，以至于作者认为，所有关心LLM大语言模型的人都不容错过。所以，在制作视频之余，特以此文整理，和大家分享。此外，在本文最后还有一些拓展阅读，同样非常推荐。本次演讲的精校完整中文版视频的B站传送门：
https://www.bilibili.com/video/BV1ts4y1T7UH以下是演讲全文。
大家好。我很高兴在这里向您介绍 GPT 的状态，更广泛地介绍大型语言模型快速发展的生态系统。我想把演讲分成两部分：在第一部分我想告诉你我们是如何训练 GPT 助手的；然后在第二部分中，我们将了解如何将这些助手有效地用于您的应用程序。首先让我们看一下如何训练这些助手的新兴秘诀，并记住这一切都是非常新的，并且仍在迅速发展。但到目前为止，食谱看起来像这样。这是一张有点复杂的幻灯片，我将逐一介绍它。粗略地说，我们有四个主要阶段：**预训练、有监督微调、奖励建模、强化学习**，依次类推。现在在每个阶段我们都有一个数据集来支持。我们有一个算法，我们在不同阶段的目的，将成为训练神经网络的目标。然后我们有一个结果模型，然后在上图底部有一些注释。
### **Pretraining**
### **预训练**
我们要开始的第一个阶段是预训练阶段。
这个阶段在这个图中有点特殊：这个图没有按比例缩放，这个阶段实际上是所有计算工作基本上发生的地方，相当于训练计算时间的 99%。因此，这就是我们在超级计算机中使用数千个 GPU 以及可能进行数月的训练来处理互联网规模数据集的地方。其他三个阶段是微调阶段，更多地遵循少量 GPU 和数小时或数天的路线。那么让我们来看看实现基础模型的预训练阶段。首先，我们要收集大量数据。这是我们称之为数据混合的示例，该示例来自 Meta 发布的这篇论文，他们发布了这个 Llama 基础模型。可以大致看到进入这些集合的数据集的种类，我们有common crawl这只是一个网络爬取，C4也是common crawl，然后还有一些高质量的数据集。例如，GitHub、维基百科、书籍、ArXiv论文存档、StackExchange问答网站等。这些都混合在一起，然后根据给定的比例进行采样，形成 GPT 神经网络的训练集。现在，在我们实际训练这些数据之前，我们需要再经过一个预处理步骤，即标记化（tokenization）。这基本上是将我们从互联网上抓取的原始文本翻译成整数序列，因为这是 GPT 运行的原生表示。标记化是文本片段和标记与整数之间的一种无损转换，这个阶段有许多算法。通常您可以使用诸如字节编码之类的东西，它迭代地合并小文本块并将它们分组为标记。在这里我展示了这些标记的一些示例块，然后这是将实际馈入Transformer的原始整数序列。现在我在这里展示了两个类似的例子，用于控制这个阶段的超参数。GPT4，我们没有发布太多关于它是如何训练的信息，所以我使用 GPT3 的数字；GPT3 现在有点老了，大约三年前。但是Llama是 Meta 的一个相当新的模型。这些大致是我们在进行预训练时要处理的数量级：词汇量通常是几万个标记。上下文长度通常是 2,000、4,000，现在甚至是 100,000，这决定了 GPT 在尝试预测序列中的下一个整数时将查看的最大整数数。你可以看到，Llama 的参数数量大概是 650 亿。现在，尽管与 GPT3 的 1750 亿个参数相比，Llama 只有 65 个 B 参数，但 Llama 是一个明显更强大的模型，直观地说==，====**这是因为该模型的训练时间明显更长，训练了1.4 万亿标记而不是 3000 亿标记。所以你不应该仅仅通过模型包含的参数数量来判断模型的能力**==。这里我展示了一些粗略的超参数表，这些超参数通常用于指定 Transformer 神经网络。比如头的数量，尺寸大小，层数等等。在底部，我展示了一些训练超参数。例如，为了训练 65 B 模型，Meta 使用了 2,000 个 GPU，大约训练了 21 天，大约花费了数百万美元。这是您在预训练阶段应该记住的粗略数量级。现在，当我们实际进行预训练时，会发生什么？一般来说，我们将获取我们的标记并将它们放入数据批次中。我们有这些数组将馈入Transformer，这些数组是 B，批量大小，这些都是按行堆叠的独立示例，B 乘以 T，T 是最大上下文长度。在我的这个图里，长度只有十个，实际工作里这可能是 2,000、4,000 等等。这些是非常长的行。我们所做的是获取这些文档并将它们打包成行，然后用这些特殊的文本结束标记将它们分隔开，基本上是为了告诉Transformer新文档从哪里开始。这里我有几个文档示例，然后将它们扩展到这个输入中。现在，将把所有这些数字输入到 Transformer 中。我们只关注一个特定的单元格，但同样的事情会发生在这个图中的每个单元格上。让我们看看绿色单元格。绿色单元会查看它之前的所有标记，所有标记都是黄色的，我们将把整个上下文输入到 Transformer 神经网络中，Transformer 将尝试预测 序列中的下一个标记，在本例中为红色。不幸的是，我现在没有太多时间来详细介绍Transformer这个神经网络架构。（注：特别棒和巧的，Andrej做过一次斯坦福课程，专门深入讲解了Transformer神经网络架构，同样非常推荐，中文版视频附在本文结尾）对于我们的目的来说，Transformer只是一大堆神经网络的东西，通常有几百亿个参数，或者类似的东西。当然，当您调整这些参数时，您会得到这些单元格中的每一个单元格的预测分布略有不同。例如，如果我们的词汇表大小是 50,257 个标记，那么我们将拥有那么多数字，因为我们需要为接下来发生的事情指定概率分布。基本上，我们有可能发生任何事情。现在，在这个特定的例子中，对于这个特定的单元格，513 将是下一个标记，因此我们可以将其用作监督源来更新Transformer的权重。将同样的做法应用于并行中的每个单元格，并且不断交换批次，并且试图让Transformer对序列中接下来出现的标记做出正确的预测。让我更具体地向您展示当您训练其中一个模型时的情况。这实际上来自纽约时报，他们在莎士比亚上训练了一个小的 GPT，这是莎士比亚的一小段，他们在上面训练了一个 GPT。一开始，在初始化时，GPT 以完全随机的权重开始，因此也将获得完全随机的输出。但是，随着时间的推移，当训练 GPT 的时间越来越长时，我们会从模型中获得越来越连贯和一致的样本。当然，你从中抽样的方式是预测接下来会发生什么，你从那个分布中抽样，然后不断将其反馈到过程中，基本上就是对大序列进行抽样。到最后，你会看到 Transformer 已经学会了单词，以及在哪里放置空格，在哪里放置逗号等等。随着时间的推移，模型正在做出越来越一致的预测。然后以下这些，是您在进行模型预训练时会查看的图类型。实际上，我们在训练时查看随时间变化的损失函数，低损失意味着我们的Transformer正在预测正确 - 为序列中正确的下一个整数提供更高的概率。训练一个月后，我们将如何处理这个模型？我们注意到的第一件事，在这个领域，这些模型基本上在语言建模过程中学习了非常强大的通用表示，并且可以非常有效地微调它们以用于您可能感兴趣的任何下游任务 .举个例子，如果对情感分类感兴趣，过去的方法是收集一堆正面和负面的信息，然后为此训练某种 NLP 模型，但新方法是忽略情感分类，直接去进行大型语言模型预训练，训练大型Transformer，然后你可能只有几个例子，已经可以非常有效地为该任务微调你的模型。这在实践中非常有效。这样做的原因基本上是 **Transformer 被迫在语言建模任务中同时处理大量任务，因为就预测下一个标记而言，它被迫了解很多关于文本的结构和其中所有不同的概念。**这就是 GPT-1。在 GPT-2 前后，人们注意到比微调更好的是，你可以非常有效地提示（prompt）这些模型。这些是语言模型，它们想要完成文档，所以你可以通过排列这些假文档来欺骗它们执行任务。在这个例子中，例如，我们有一些段落，然后我们做 QA(问和答），QA，QA，几次提示，然后我们做 Q，然后，当 Transformer 试图完成文档时，它实际上是在回答我们的问题。这就是一个提示工程（prompt engineering）基础模型的示例，通过提示工程让模型相信它正在模仿文档并让它执行特定的任务。这开启了提示高于微调（prompt over finetuning）的时代。我们看到，即使没有对任何神经网络进行微调，它也可以在很多问题上非常有效。从那时起，我们就看到了每个人都知道的，基础模型的完整进化树：并非所有这些模型型号都可用。例如，GPT-4 基础模型从未发布。您可能通过 API 与之交互的 GPT-4 模型不是基础模型，而是辅助模型，我们稍后将介绍如何获取这些模型；GPT-3 基础模型可通过名为 DaVinci 的 API 获得；GPT-2 基础模型可在我们的 GitHub 存储库上作为权重获得；==**目前最好的（可以公开获得的）基础模型是 Meta 的 Llama 系列，尽管它没有商业许可**==**。**需要指出的一件事是，基础模型不是助手（assistant，即类似ChatGPT的问答助手），它们不想回答你的问题，它们只是想完成文件（笑）。所以如果你告诉基础模型：“写一首关于面包和奶酪的诗”，它会用更多的问题来回答问题。它只是在完成它认为是文档的内容。但是，您可以在基础模型里以特定方式提示以更可能得到结果。例如，“这是一首关于面包和奶酪的诗"。在这种情况下，它将正确地自动完成。你甚至可以欺骗基础模型成为助手，你这样做的方法是创建一个特定的小提示，让它看起来像是人和助手之间有一份文件，他们正在交换信息。然后，在底部，您将查询放在最后，基础模型将自我调整为有用的助手和某种答案（生成这种形式的文档）。这不是很可靠，在实践中也不是很好，尽管它可以做到。
### **Supervised Finetuning**
### **监督微调**
相反，我们有不同的途径来制作实际的 GPT 助手(GPT Assistant)，而不仅仅是基础模型文档完成器。这将我们带入有监督的微调。在有监督的微调阶段，我们将收集少量但高质量的数据集。在这种情况下，我们要求人工承包商收集及时和理想响应形式的数据。
我们收集很多这样的东西，通常是类似数万个这种数量。然后我们仍将对这些数据进行语言建模，因此算法上没有任何改变。我们只是换出一个训练集。它曾经是互联网文档，那是一种量很大但质量不高的数据，我们换成用QA即时响应的数据。那是低数量但高质量的。我们还是做语言建模，然后，训练之后，我们得到一个SFT（Supervised Finetuning 监督微调）模型。你可以实际部署这些模型，它们是实际的助手，它们在一定程度上起作用。让我向您展示示例演示的样子。这里有一个人类承包商可能会想出的东西，这是一个随机的演示：你写了一篇关于垄断一词的相关性的简短介绍，或者类似的东西，然后==**承包商也写下了一个理想的回应。当他们写下这些回复时，他们遵循大量的标签文档，并且要求他们生成提供帮助、真实且无害的回答**==。这是这里的标签说明。你可能看不懂，我也看不懂，它们很长，人们按照说明并试图完成这些提示。这就是数据集的样子，你可以训练这些模型，这在一定程度上是有效的。
### **Reward Modeling**
### **奖励建模**
现在，我们可以从这里继续流程，进入 RLHF，即“从人类反馈中强化学习”，它包括奖励建模和强化学习。让我介绍一下，然后我将回过头来讨论为什么您可能想要完成额外的步骤，以及这与 仅有SFT 模型相比如何。在奖励建模步骤中，我们现在要做的是**将数据收集转变为比较形式。**下面是我们的数据集的示例。顶部是相同的提示，它要求助手编写一个程序或一个函数来检查给定的字符串是否为回文。然后我们做的是采用已经训练过的 SFT 模型，并创建多个补全。在这种情况下，我们有模型创建的三个补全。然后我们要求人们对这些补全进行排名。如果你盯着它看一会儿——顺便说一下，要比较其中的一些预测是非常困难的事情，而且这可能需要人们甚至几个小时来完成一个提示补全的比较。但假设我们决定，其中一个比其他的好得多，依此类推，我们对它们进行排名。然后，我们可以对这些补全之间的所有可能对，进行看起来非常像二元分类的东西（以进行排序）。接着，要做的是将提示按行排列，这里所有三行的提示都是相同的，但补全方式不同，黄色标记来自 SFT 模型，我们在最后附加另一个特殊的奖励读出标记，基本上只在这个单一的绿色标记上监督Transformer。Transformer会根据提示的完成程度预测一些奖励。Transformer对每个补全的质量进行了猜测，然后，一旦对每个补全进行了猜测，我们就有了模型对它们排名的基本事实，==**而我们实际上可以强制其中一些数字应该比其他数字高得多，我们将其制定为损失函数，并训练我们的模型，使得模型做出与来自人类承包商的比较事实数据相一致的奖励预测。这就是我们训练奖励模型的方式。这使我们能够对提示的完成程度进行评分。**==
### **Reinforcement Learning**
### **强化学习**
现在我们有了奖励模型，但我们还不能部署它。因为它本身作为助手不是很有用，但是它对于现在接下来的强化学习阶段非常有用。==**因为我们有一个奖励模型，所以我们可以对任何给定提示（prompt）的任意完成/补全（completion）质量进行评分。**==我们在强化学习期间所做的基本上是再次获得大量提示，然后针对奖励模型进行强化学习。这就是它的样子：我们接受一个提示，将其排成行，现在我们使用想要训练的模型，将该模型初始化为 SFT 模型，以创建一些黄色的补全。然后，再追加奖励标记，按照已经固定不变的奖励模型读出奖励分数，现在这个奖励模型的评分不再变化。奖励模型告诉我们这些提示的每一次完成的质量。我们现在基本上可以用（和前面）相同的语言建模损失函数，但我们目前正在对黄色标记进行训练，并且我们正在通过奖励模型指示的奖励来权衡语言建模目标。例如，在第一行中，奖励模型表示这是一个相当高的完成度，因此我们碰巧在第一行采样的所有标记都将得到强化，它们将 获得更高的未来概率。相反，在第二行，奖励模型真的不喜欢这个完成，负 1.2，因此我们在第二行采样的每个标记在未来都会有更低的概率。我们在很多提示、很多批次上一遍又一遍地这样做，基本上，我们得到一个在这里创建黄色标记的策略，让所有完成标记都会根据我们在前一阶段训练的奖励模型获得高分。这就是我们训练的方式——这就是 RLHF 流程。最后，您得到了一个可以部署的模型。例如，ChatGPT 是 RLHF 模型。您可能会遇到其他一些模型，例如 Kuna 13B 等，这些都是 SFT 模型。我们有基础模型、SFT 模型和 RLHF 模型，这基本上是可用模型列表的事物状态。你为什么想要做 RLHF？一个不太令人兴奋的答案是它的效果更好。以上这个图来自instructGPT论文。这些 PPO 模型是 RLHF，根据前一段时间的这些实验，我们看到把它们提供给人类时，它们在很多比较中更受欢迎。与提示为助手的基础模型相比，与 SFT 模型相比，人类基本上更喜欢来自 RLHF 模型的标记（输出文字）。它就是工作得更好。但你可能会问为什么？为什么效果更好？
我不认为社区有一个一致的令人惊奇的答案，但我可能提供一个原因：**它与计算比较容易程度与生成容易程度之间的不对称有关。**让我们以生成俳句为例。假设我请模特写一首关于回形针的俳句。如果你是一个试图提供训练数据的承包商，那么想象一下，作为一个为 SFT 阶段收集基本数据的承包商，你应该如何为一个回形针创建一个漂亮的俳句？你可能不太擅长这个。但是，如果我给你举几个俳句的例子，你可能会比其他人更能欣赏其中的一些俳句。因此，判断其中哪一个是好的是一项容易得多的任务。基本上，**这种不对称性使得比较成为一种更好的方式，可以潜在地利用你作为一个人和你的判断力来创建一个稍微更好的模型。**但是，**RLHF 模型在某些情况下并不是对基础模型的严格改进。**特别是，我们注意到，例如，==**RLHF模型失去了一些熵，这意味着它们给出了更多的峰值结果。它们可以输出更低的变化，可以输出比基础模型变化更少的样本。**==**基础模型有更多熵，会给出很多不同的输出。**我仍然更喜欢使用基础模型的一种地方是。。。比如有 n 个东西并且想要生成更多类似东西的场景中。这是我刚刚编造的一个例子。我想生成很酷的口袋妖怪名字。我给了它七个口袋妖怪的名字，让基础模型完成了文档。它给了我更多的口袋妖怪名字。这些都是虚构的，我还试图查找它们，确定它们不是真正的口袋妖怪。这是我认为基础模型擅长的任务，因为它仍然有很多熵，并且会给你很多不同的、很酷的、更多的东西，看起来像你以前给它的任何东西。说了这么多，这些是你现在可以使用的辅助模型，有一些数字：伯克利有一个团队对许多可用的助手模型进行排名，并基本上给了它们 ELO 评级。目前最好的模型毫无疑问是 GPT-4，其次是 Claude，GPT-3.5，然后是一些模型，其中一些可能作为权重提供，比如 Kuna、Koala 等。这里排名前三的是 RLHF 模型，据我所知，我相信所有其他模型都是 SFT 模型。
### **将GPT助手模型**
### **应用于问题**
以上是我们在高层次上训练这些模型的方式。
现在我要换个方向，让我们看看如何最好地将 GPT 助手模型应用于您的问题。现在我想在一个具体示例的场景里展示。让我们在这里使用一个具体示例。假设你正在写一篇文章或一篇博客文章，你打算在最后写这句话。加州的人口是阿拉斯加的 53 倍。因此出于某种原因，您想比较这两个州的人口。想想我们自己丰富的内心独白和工具的使用，以及在你的大脑中实际进行了多少计算工作来生成这最后一句话。这可能是你大脑中的样子：好的。对于下一步，让我写博客——在我的博客中，让我比较这两个人群。好的。首先，我显然需要得到这两个人群。现在我知道我可能根本不了解这些人群。我有点，比如，意识到我知道或不知道我的自我知识；正确的？我去了——我做了一些工具的使用，然后我去了维基百科，我查找了加利福尼亚的人口和阿拉斯加的人口。现在我知道我应该把两者分开。同样，我知道用 39.2 除以 0.74 不太可能成功。那不是我脑子里能做的事情。因此，我将依靠计算器。我打算用一个计算器，把它打进去，看看输出大约是 53。然后也许我会在我的大脑中做一些反思和理智检查。那么53有意义吗？好吧，这是相当大的一部分，但是加利福尼亚是人口最多的州，也许这看起来还可以。这样我就有了我可能需要的所有信息，现在我开始写作的创造性部分了。我可能会开始写类似，加利福尼亚有 53 倍之类的东西，然后我对自己说，这实际上是非常尴尬的措辞，让我删除它，然后再试一次。在我写作的时候，我有一个独立的过程，几乎是在检查我正在写的东西，并判断它是否好看。然后也许我删除了，也许我重新构造了它，然后也许我对结果感到满意。基本上，长话短说，当你创造这样的句子时，你的内心独白会发生很多事情。但是，当我们在其上训练 GPT 时，这样的句子是什么样的？从 GPT 的角度来看，这只是一个标记序列。因此，当 GPT 读取或生成这些标记时，它只会进行分块、分块、分块，每个块对每个标记的计算工作量大致相同。这些 Transformer 都不是很浅的网络，它们有大约 80 层的推理，但 80 仍然不算太多。这个Transformer将尽最大努力模仿...但是，当然，这里的过程看起来与你采用的过程非常非常不同。特别是，在我们最终的人工制品中，在创建并最终提供给 LLM 的数据集中，所有内部对话都被完全剥离（只给出最后结果作为训练数据）。并且与您不同的是，**GPT 将查看每个标记并花费相同的算力去计算它们中的每一个，实际上，你不能指望它对每个标记做太多的工作。**基本上，这些Transformer就像标记模拟器。它们不知道自己不知道什么，它们只是模仿（预测）下一个标记；它们不知道自己擅长什么，不擅长什么，只是尽力模仿（预测）下一个标记。它们不反映在循环中，它们不检查任何东西，它们在默认情况下不纠正它们的错误，它们只是对标记序列进行采样。它们的头脑中没有单独的内心独白流，它们正在评估正在发生的事情。现在它们确实有某种认知优势，我想说，那就是它们实际上拥有大量基于事实的知识，涵盖大量领域，因为它们有几百亿个参数，这是大量存储和大量事实。而且我认为，它们也有相对大而完美的工作记忆。因此，任何适合上下文窗口的内容都可以通过其内部自注意机制立即供Transformer使用，它有点像完美的记忆。它的大小是有限的，但Transformer可以非常直接地访问它，它可以无损地记住其上下文窗口内的任何内容。这就是我比较这两者的方式。我之提出所有这些，是因为我认为在很大程度上，**提示只是弥补了这两种架构之间的这种认知差异。**就像我们人类大脑和 LLM 大脑（的比较），你可以这么看。人们发现有一件事，在实践中效果很好。
特别是如果您的任务需要推理==**，您不能指望Transformer对每个标记进行太多推理，因此您必须真正将推理分散到越来越多的标记上。例如，您不能向Transformer提出一个非常复杂的问题并期望它在一个标记中得到答案。（用于计算的）时间不够。**==这些Transformer需要标记来思考，我有时喜欢这样说。这是一些实践中运作良好的事情：例如，您可能有一个few-shot prompt提示，向Transformer显示它在回答问题时应该展示其工作，如果您给出几个示例，Transformer将模仿该模板，然后它就会在评估方面做得更好。此外，您可以**通过说“let's think step by step"从Transformer中引发这种行为**，因为这使Transformer变得有点像展示它的工作。而且，因为它有点进入一种显示其工作的模式，它会为每个标记做更少的计算工作，因此它更有可能成功，因为随着时间的推移，它的推理速度会变慢。这是另一个例子。这称为自我一致性。我们看到我们有能力开始写，然后如果没有成功，我可以再试一次，我可以多次尝试，也许会选择一个最好的。因此，在这些类型的实践中，您可能不仅会抽样一次，还会抽样多次，然后有一些过程来找到好的样本，只保留这些样本或者进行多数表决，类似这样的事情。而在这个过程中，这些 Transformer 在预测下一个标记时，就像你一样，它们可能会倒霉，它们可能会采样到一个不太好的标记，它们可能会在推理方面像死胡同一样走下坡路。因此，与您不同，它们无法从中恢复过来。它们被它们采样的每一个标记所困，所以它们会继续这个序列，即使它们知道这个序列不会成功。**让它们有某种能力能够回顾、检查或尝试。基本上围绕它进行抽样，这也是一种技术。**事实证明，实际上LLM像是知道什么时候搞砸了一样。例如，假设您要求模型生成一首不押韵的诗：它可能会给你一首诗，但它实际上是押韵的。事实证明，特别是对于更大的模型，比如 GPT-4，你可以问它，“你完成任务了吗？”实际上，GPT-4 很清楚自己没有完成任务。它只是在采样方面有点不走运。它会告诉你，“不，我没有完成任务。让我再尝试一次。”但是如果你不提示它，它甚至不会——就像它不知道要重新访问等等，==**你必须在你的提示中弥补这一点。你必须推动它来检查。如果你不要求它检查，它不会自己检查**==。它只是一个标记模拟器。我认为更一般地说，**很多这些技术都属于我所说的重建我们人类的系统二的范围。**你可能熟悉人类思考的系统一和系统二模式。系统一是一个快速的自动过程，我认为有点对应于 LLM，只是对标记进行抽样。系统二是大脑中较慢的、经过深思熟虑的计划部分。这实际上是上周的一篇论文，这个领域正在迅速发展。它被称为思想树（Tree of Thought）。在思想树中，这篇论文的作者建议为任何给定的提示维护多个完成，然后也会在整个过程中对它们进行评分，并保留那些进展顺利的。大家看看这是否有意义。很多人真的在把玩一些prompt工程，基本上是希望让LLM恢复一些我们大脑中具有的能力。我想在这里指出的一件事是，这不仅仅是一个提示。这实际上是与一些 Python 胶水代码一起使用的提示，因为你实际上要维护多个提示，你还必须在这里做一些树搜索算法来找出扩展哪个提示等等。因此，它是 Python 胶水代码和在 while 循环或更大算法中调用的各个提示的共生体。我还认为这里与 AlphaGo 有一个非常酷的相似之处。AlphaGo下围棋有一个放下一块棋子的策略，这个策略本来就是模仿人训练出来的。但是除了这个策略之外，它还会做蒙特卡洛搜索，它会在围棋中打出多种可能性并评估所有这些，只保留那些运作良好的。我认为这有点类似于AlphaGo，但是针对于文本。就像思想树一样，我认为更普遍的是，**人们开始真正探索更通用的技术，不仅仅是简单的问答提示，而是看起来更像是将许多提示串在一起的 Python 胶水代码。**在右边，我有一个来自这篇论文的例子，叫做 React，他们将提示的答案构造为一系列思考、行动、观察、思考、行动、观察，这是一个完整的展开，一种思考 回答查询的过程。在这些动作中，模型也被允许使用工具。在左边，我有一个AutoGPT 的例子。顺便说一句，AutoGPT 是一个我认为最近炒得沸沸扬扬的项目，但我仍然觉得它有点鼓舞人心。它是一个允许 LLM 保留任务列表并继续递归分解任务的项目，我认为目前效果不是很好，不建议人们在实际应用中使用它。但我认为，随着时间的推移，这是可以从中汲取灵感的东西。**AutoGPT有点像让我们的模型系统思考。**接下来一件事，我觉得有点意思的是 ，LLM 有种不想成功的心理怪癖。**它们只是想模仿。如果你想成功，你应该要求它。**我的意思是，当 Transformer 被训练时，它们有训练集，并且它们的训练数据中可以有一个完整的性能质量范围。例如，可能有一些物理问题或类似问题的提示，可能有一个学生的解决方案完全错误，但也可能有一个非常正确的专家答案。Transformer无法区分它们之间的区别——它们知道低质量解决方案和高质量解决方案，但默认情况下，它们想要模仿所有这些，因为它们只是接受过语言建模方面的训练。在测试的时候，你必须要求一个好的表现。上面论文中的这个例子，他们尝试了各种提示，“let's think step by step”非常强大，因为它把推理分散到许多标记上但==**更好的提示方法是："让我们一步一步地解决这个问题 确定我们有正确的答案"**==。这就像获得正确答案的条件一样，这实际上使Transformer工作得更好，因为Transformer现在不必在低质量解决方案上对冲其概率质量，尽管这听起来很荒谬。**基本上，请随意寻求一个强有力的解决方案**，说出您是该主题的领先专家之类的话，假装你有IQ 120，等等。不要试图要求太多的智商，因为如果你要求400的智商，你可能会在数据分布之外；或者更糟糕的是，你可能会在一些科幻的数据分布中，它会开始 进行一些科幻角色扮演，或类似的事情。我认为你必须找到合适的智商设定。那里有一些U形曲线。接下来，正如我们所看到的，当我们试图解决问题时，我们知道自己擅长什么，不擅长什么，并且我们在计算上依赖工具。你想对你的LLM做同样的事情。特别是，我们可能希望为它们提供计算器、代码解释器等，以及进行搜索的能力，并且有很多技术可以做到这一点。再次要记住的一件事是，**默认情况下这些Transformer可能不知道它们不知道的事情**，你甚至可能想在提示中告诉Transformer，“你的心算不太好。每当您需要进行大数加法、乘法或其他操作时，请使用此计算器。以下是您如何使用计算器。使用这个标记组合，等等，等等。”你必须把它拼出来，因为默认情况下模型不知道它擅长什么或不擅长什么，就像你和我一样。接下来，我认为一件非常有趣的事情是，我们从一个只有检索的世界走到钟摆摆动的另一个极端，那里只有LLM的记忆但实际上，**在这两者之间有检索增强模型的整个空间，这在实践中非常有效。**正如我所提到的，Transformer的上下文窗口是它的工作内存。如果您可以将与任务相关的任何信息加载到工作内存中，那么该模型将运行得非常好，因为它可以立即访问所有内存。我认为很多人对基本上检索增强生成非常感兴趣，在上图底部，我有一个Llama索引的例子，它是许多不同类型数据的一个数据连接器，你可以索引所有这些数据，让 LLM 访问它。新兴的秘诀是**获取相关文档，将它们分成块，将它们全部嵌入，得到表示该数据的嵌入向量，将其存储在向量存储中；然后在测试时，对矢量存储进行某种查询，获取可能与您的任务相关的块，然后将它们填充到提示中，然后生成。这在实践中可以很好地工作。**这类似于你我解决问题的时候，你可以凭记忆做任何事情，Transformer的记忆力非常大，但它有助于参考一些主要文件。无论何时，您发现自己要回到教科书上找东西，或者每当您发现自己要回到图书馆的文档中查找东西时，Transformer肯定也想这样做。您对库的某些文档如何工作有一定的记忆，但最好查找一下。同样的事情也适用于LLM。接下来，我想简单说一下约束提示。我也觉得这很有趣。这是在 LLM 的输出中强制使用特定模板的技术，实际上这是 Microsoft 的一个示例。在这里，我们强制 LLM 的输出将是 JSON，这实际上将保证输出将采用这种形式，因为它们进入并扰乱了来自Transformer的所有不同标记的概率，并且固定住这些标记。然后Transformer只填充此处的空白，然后可以对可能进入这些空白的内容实施额外的限制。这可能真的很有帮助，我认为这种约束抽样也非常有趣。我还想说几句微调。您可以通过快速prompt工程取得很大进展，但也可以考虑微调您的模型。微调模型意味着你实际上要改变模型的权重。现在在实践中做到这一点变得越来越容易，这是因为最近开发了许多技术并拥有库调用。例如，==**像 Lora 这样的参数高效微调技术可确保您只训练模型的小而稀疏的部分。因此大部分模型都保持在基础模型上，并且允许更改其中的一些部分， 这在经验上仍然很有效，并且使得仅调整模型的一小部分成本更低。这也意味着，因为你的大部分模型都是固定的，你可以使用非常低的精度推理来计算这些部分，因为它们不会被梯度下降更新，这也使得一切都更加高效。**==此外，我们还有许多开源的高质量基础模型。目前，正如我提到的，我认为 Llama 相当不错，尽管我认为它现在还没有获得商业许可。需要记住的是，微调在技术上涉及更多，它需要更多的技术专长才能做对。它需要数据集和/或可能非常复杂的合成数据流程的人工数据承包商。这肯定会大大减慢你的迭代周期。我想在较高的层次上说，SFT 是可以实现的，因为你只是在继续语言建模任务。它相对简单；但 RLHF 是一个非常多的研究领域，而且它更难开始工作。我可能不建议有人尝试推出他们自己的 RLHF 实现。这些东西非常不稳定，很难训练，现在对初学者来说不是很友好，而且它也有可能变化得非常快。以下这些是我现在的默认建议。我会把你的任务分成两个主要部分。第一，实现你的最佳表现，第二，按照这个顺序优化你的费用。
- ==**首先，目前最好的性能来自 GPT4 模型。它是迄今为止功能最强大的模型。**==
- ==**然后， 让提示里包含详细的任务内容、相关信息和说明。想想如果它们不能给你回邮件你会告诉它们什么。要记住任务承包商是人，他们有内心独白，他们非常聪明；而LLM不具备这些品质。因此，请务必仔细考虑LLM的心理，并迎合这一点。甚至向这些提示添加任何相关的上下文和信息。**==
- ==**多参考很多提示工程技术。我在上面的幻灯片中突出显示了其中一些，但这是一个非常大的空间，我只建议您在线寻找快速的Prompt工程技术。那里有很多内容。**==
- ==**尝试使用少样本few-shots示例提示。这指的是你不只是想问，你还想尽可能地展示（你想要的），给它举例子，如果可以的话，帮助它真正理解你的意思。**==
- ==**尝试使用工具和插件来分担 LLM 本身难以完成的任务。**==
- ==**然后不仅要考虑单个提示和答案，还要考虑潜在的链条和反射，以及如何将它们粘合在一起，以及如何制作多个样本等。**==
- ==**最后，如果你认为你已经最大化了提示工程的效果，我认为你应该坚持一段时间，看看一些可能对你的应用程序的模型微调，但预计这会更慢并且涉及更多。**==
- ==**然后这里有一个脆弱的专家研究区，我想说的是 RLHF，如果你能让它工作的话。它目前确实比 SFT 好一点，但是，我想说的是，这非常复杂。**==
- ==**为了优化您的成本，请尝试探索容量较低的模型或更短的提示等。**==
我还想谈谈我认为 LLM 目前非常适合的用例。特别要注意的是，今天的 LLM 有很多限制，我会在所有应用中牢记这一点。模型，顺便说一句，这可能是一个完整的演讲，我没有时间详细介绍它。模型可能有偏见，它们可能捏造、产生幻觉信息，它们可能有推理错误，它们可能在整个类别的应用程序中都挣扎，它们有知识截止日期，比如说，2021 年 9 月。Twitter 每天都在发生大量对LLM的攻击，包括即时注入、越狱攻击、数据中毒攻击等。我现在的建议是在低风险应用程序中使用 LLM，将它们与始终与人工监督结合起来，将它们用作灵感和建议的来源，并考虑副驾驶而不是在某处执行任务的完全自主的代理。目前尚不清楚这些模型是否合适。最后我想说，GPT-4 是一个了不起的人工制品，我非常感谢它的存在。它很漂亮，它在很多领域都有大量的知识，它可以做数学、代码等等。此外，还有一个蓬勃发展的生态系统，包括正在构建并纳入生态系统的其他所有事物，其中一些我已经谈到了。所有这些功能都触手可及。我和GPT-4说，“你能说些什么来激励Microsoft Build 2023的观众吗？”我将其输入Python并逐字记录，GPT-4说了以下内容。顺便说一句，我不知道它们在主题演讲中使用了这个技巧，我以为我很聪明，但它真的很擅长这个。它说：“女士们，先生们，微软 Build 2023 的创新者和开拓者，欢迎来到与众不同的聪明才智的聚会。你们是未来的建筑师，是塑造人类蓬勃发展的数字领域的远见者。拥抱技术的无限可能性，让您的想法像您的想象一样飞翔。让我们一起为子孙后代创造一个联系更紧密、更卓越、更具包容性的世界。准备好释放您的创造力，探索未知，将梦想变为现实。你的旅程从今天开始。”。谢谢！
关注 Founder Park，我们将持续推出更全面更深度的大模型相关讨论与报道。
**Founder Park 正在搭建大模型相关话题的交流社群，群内聚集着致力于在大模型相关领域创业的创业者、产品经理、研发工程师、对大模型技术和场景应用开发进展感兴趣的学者及投资人。**
在这里，你肯定可以获得大模型相关领域的最新动态资讯、或许可以进行高质量的行业对话交流，还可能连接更多有价值的行业认知。
如果你关注大模型领域，欢迎扫码加入我们的大模型交流群，来一起探讨大模型时代的共识和认知，跟上大模型时代的这股浪潮。
没时间看直播，可以扫码关注我们的播客
**更多阅读**
[微软个人助理来了！Copilot是最好的AI产品形态，插件将成为新的平台模式](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247487831&idx=1&sn=a530a8083d873ad2e56e9a8b63ff2b81&chksm=c00af76bf77d7e7df488353a41b8f54ea806473480c41352d7bc4a74c95b8bd5f17a8479e0e0&scene=21#wechat_redirect)
[最好的回应，孙燕姿为人机之争画上句号](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247487760&idx=1&sn=4ebd41332abe4cc987a3f04d8cbe0d42&chksm=c00af72cf77d7e3abb84df0569b01898fb66fff1df1cded57da1deb69dc6862eb51848eae88f&scene=21#wechat_redirect)
[Stability.AI创始人万字访谈：数据不要全球化，五大巨头谁能赢得AI大战？](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247487708&idx=1&sn=fc3cfb45c9971b47a3c6cb6c45955f11&chksm=c00af6e0f77d7ff650d460ff7c6d594e95acda154256ef066afc36c7f4612ad580be52b4c426&scene=21#wechat_redirect)
[微软CEO纳德拉访谈：OpenAI合作，谷歌搜索竞争，世界需要10亿开发者](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247487681&idx=1&sn=395a1e1651d09a0137e3f324effa2f53&chksm=c00af6fdf77d7feb59ad43cd5fd57606d6974f8abd06e54c6249676e70b761f102fdc24288c3&scene=21#wechat_redirect)
[最豪华AI创业访谈：DeepMind创始人如何做个人助理，AI不止是效率革命](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247487363&idx=1&sn=7c4863004dd51a515b9659f353a05196&chksm=c00ae9bff77d60a939faf757e908adef79d4ad7892a26762503daa8ee07659a7a5ee2208a392&scene=21#wechat_redirect)
[OpenAI发布炸裂研究：让AI解释AI黑箱，人类无法理解，语言无法描述](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247487186&idx=1&sn=a56765b60819cc88af73019ae15cc8a9&chksm=c00ae8eef77d61f8a22af16b4238c75c72eb12a5e792b6285ceb76d31b0f0bd167bc95164aa0&scene=21#wechat_redirect)
[AI 教父 Hinton & MIT 万字访谈: 人类可能只是 AI 演化过程中的一个过渡阶段](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247487084&idx=1&sn=3e1cff12b43aa73bedb1344dbe6b3ffc&chksm=c00ae850f77d614610824c9b5bd0a60978fd7d9c235e78e0bb3a4a09461ec861cb224cf7abf7&scene=21#wechat_redirect)
[马斯克聊年轻人教育、社区管理和人生幸福，巴菲特强烈推荐观看](http://mp.weixin.qq.com/s?__biz=Mzg5NTc0MjgwMw==&mid=2247487041&idx=1&sn=de8711050f10347786093d4f360d9c91&chksm=c00ae87df77d616b6f3d66fd17b6048bdd9b0c445a5584f98fd8440ff118f48edc8f361867ef&scene=21#wechat_redirect)
转载原创文章请添加微信：geekparker
Founder Park 正在招募新的 AGI 观察者