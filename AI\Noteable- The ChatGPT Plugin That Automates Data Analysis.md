---
DocFlag:
  - Reference
Updated: 2023-06-06T14:25
tags:
  - AI->-ChatGPTPlugin
  - AI->Automation
Created: 2023-06-06T12:38
---
[![](https://miro.medium.com/v2/resize:fit:1200/1*tJnz84w006hlWzkIvWl9XA.jpeg)](https://miro.medium.com/v2/resize:fit:1200/1*tJnz84w006hlWzkIvWl9XA.jpeg)
---
Image licensed from Shutterstock
ChatGPT has taken data analysis to the next level (again) with the help of a new plugin. And no, I’m not referring to the code interpreter, which is not yet available to all ChatGPT Plus subscribers, but to a new plugin called Noteable.
Noteable is a new ChatGPT plugin available on the plugin store that helps us do data analysis in seconds! You only need to use the prompt I’ll show you in this article to start working with this plugin.
Here’s how Noteable can help you automate your data analysis.
_If you don’t feel like reading, you can watch my YouTube video instead._
## Setting up Noteable
First, we need to install Noteable. To do so, go to the Plugin Store and search for Noteable. Once you find it, just click on install!
Screenshot
After installing Noteable, the window below will pop up. You have to create an account before starting to work with this plugin.
Screenshot
Why do we need an account? Well, ChatGPT will display the visualizations and analysis we ask for, but all the code is only going to be available in a notebook format in Noteable. On top of that, we need to create a project on Noteable to work with the plugin.
After you log in, you’ll see a space like the one below with a default project called “My First Project.” Click on that project or create one from scratch.
Image by author
Once you’re on a project, copy the link. We’ll use that link in the next section. The format of the link should look like the one below.
```Plain
https://app.noteable.io/p/<project_id>/<project_name>
```
## The Prompt to Use
Now it’s time to let Noteable do data analysis for us! Here’s the prompt we’re going to use.
```Plain
Load this dataset: <link of the dataset>
Use this as my default project: <link of the project>
Act as a data scientist and analyze ...
```
As you can see, we only need to provide the link to our dataset and project. That’s pretty much it!
Things get a bit more tricky with the instructions. You can let Noteable make a generic but quick analysis or you can specify what you want to get in the prompt. Let’s have a look.
## 1. Quick Data Analysis with Noteable
First, we’ll let Noteable carry out the data analysis on its own without giving much detail on the aspects we want to focus on.
Let’s automate the data analysis for a [dataset](https://github.com/ifrankandrade/python-course-for-excel-users/blob/main/4.Data%20Visualization/population_total.csv) with population data from 1955 to 2020 for all countries around the world.

> Load this dataset: https://github.com/ifrankandrade/python-course-for-excel-users/blob/main/4.Data%20Visualization/population_total.csv
> 
> Use this as my default project: <link of the project>
> 
> Act as a data scientist and analyze the dataset and make charts and graphs to show the major trends in population growth around the world
Here’s the dataset overview Noteable gives.
Image by author
Then it automatically focuses on the 5 most populous countries in 2020 and makes a lineplot to show the population growth from 1955 to 2020.
Image by author
Finally, it adds some notes to complete the analysis. Pretty good!
## 2. Customized Data Analysis with Noteable
Now we’ll tell ChatGPT and Noteable the type of analysis we want to carry out. For this example, we’ll use a [dataset](https://raw.githubusercontent.com/ifrankandrade/data-visualization/main/datasets/players_20.csv) that contains information about soccer players from the game FIFA 20. We’ll specify the visualizations we want to get and the columns to use in the prompt below.

> Load this dataset: https://raw.githubusercontent.com/ifrankandrade/data-visualization/main/datasets/players_20.csv
> 
> Use this as my default project: <link of the project>
> 
> Act as a data scientist and analyze the soccer players from only the countries United States, Canada, England, Brazil, and Argentina. Make a barplot to analyze the column "overall" which represents how good a player is at soccer. Make a histogram and a boxplot to explore the average height of players in such countries. Make a scatterplot to see how the weight of players is distributed. Finally, make a piechart to see the top 10 most valuable players (value_eur) from the USA.
The result is just amazing! ChatGPT and Noteable consider all the details I mentioned
```JavaScript
<<How to use it>>
https://medium.com/artificial-corner/noteable-the-chatgpt-plugin-that-automates-data-analysis-7f7ccd84f3c8
Install plugin
Here is the prompt
-------------------------
Load this dataset: <link of dataset>
User this as my default project: <link of project in noteble>
Act as a data scientist and analyze the dataset and make charts and graphs to show the major trends in population growth around the world
-------------------------
Load this dataset: https://github.com/netcaster1/golang/blob/master/data_pred.csv
Act as a data scientist and analyze the dataset and make charts and graph. meanwhile give you prediction in this year
---------------------
Load this dataset: https://github.com/netcaster1/golang/blob/master/data_pred.csv
Please use LTSM model to analysis and predict the outlook for the next 1 months. Plot the data for the prediction alongside the actual historical data
```