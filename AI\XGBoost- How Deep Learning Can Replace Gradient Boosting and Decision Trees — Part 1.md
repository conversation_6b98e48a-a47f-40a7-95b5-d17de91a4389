---
Updated: 2023-10-12T22:06
tags:
  - AI->-Competition
  - AI->-Fine-Tuning
Created: 2023-10-12T22:06
---
[![](https://miro.medium.com/v2/resize:fit:1200/0*ITtkOQsCAVcwnPPA)](https://miro.medium.com/v2/resize:fit:1200/0*ITtkOQsCAVcwnPPA)
==[==
[![](https://miro.medium.com/v2/resize:fill:44:44/2*6JGfgbkxIy0-JVbQIinQnw.png)](https://miro.medium.com/v2/resize:fill:44:44/2*6JGfgbkxIy0-JVbQIinQnw.png)
==](https://towardsdatascience.com/@guillaume.saupin?source=post_page-----291dc9365656--------------------------------)[==
[![](https://miro.medium.com/v2/resize:fill:24:24/1*CJe3891yB1A1mzMdqemkdg.jpeg)](https://miro.medium.com/v2/resize:fill:24:24/1*CJe3891yB1A1mzMdqemkdg.jpeg)
==](https://medium.com/towards-data-science?source=post_page-----291dc9365656--------------------------------)==
[![](https://miro.medium.com/v2/resize:fit:700/0*ITtkOQsCAVcwnPPA)](https://miro.medium.com/v2/resize:fit:700/0*ITtkOQsCAVcwnPPA)
==Photo by== [==Preethi Viswanathan==](https://unsplash.com/@sallybrad2016?utm_source=medium&utm_medium=referral) ==on== [==Unsplash==](https://unsplash.com/?utm_source=medium&utm_medium=referral)==  
  
====摄影：Preethi Viswanathan在Unsplash上==
==In this article, you will learn about rewriting decision trees using a Differentiable Programming approach, as proposed by the NODE paper, enabling the reformulation of this model in a manner similar to Neural Networks.====  
  
====在本文中，您将学习如何使用NODE论文提出的可微编程方法重写决策树，从而能够以类似于神经网络的方式重新制定该模型。==
==Deriving this formulation is an excellent exercise, as it raises many issues common when building and training Custom Neural Networks:====  
  
====推导此公式是一个很好的练习，因为它引发了许多构建和训练自定义神经网络时常见的问题：==
- ==How to avoid the vanishing gradient problem?====  
      
    ====如何避免梯度消失问题？==
- ==What is a good choice for initial weights?====  
      
    ====什么是初始砝码的好选择？==
- ==Why use batch normalization?====  
      
    ====为什么要使用批量规范化？==
==But before answering these questions, let’s see how to rephrase Decision Tree in the mathematical framework of Neural Networks: Differentiable Programming.====  
  
====但在回答这些问题之前，让我们看看如何在神经网络的数学框架中改写决策树：可微编程。==
==Update — the second article is online:====  
  
====更新 — 第二篇文章已上线：==
## ==XGBoost is highly efficient, but…====  
  
====XGBoost非常高效，但是...==
==If you have read my previous articles on Gradient Boosting and Decision Trees, you are aware that Gradient Boosting, combined with Ensembles of Decision Trees, has achieved excellent performance in classification or regression tasks involving tabular data.====  
  
====如果您读过我之前关于梯度提升和决策树的文章，您就会知道梯度提升与决策树集成相结合，在涉及表格数据的分类或回归任务中取得了出色的性能。==
==However, despite the efforts made by XGBoost, LightGBM, or CatBoost to enhance the construction of Ensemble of Decision Trees, training such a model still relies on a brute force approach. It involves systematically evaluating the gain associated with splitting values and features.====  
  
====然而，尽管XGBoost，LightGBM或CatBoost努力增强决策树集成的构建，但训练这样的模型仍然依赖于蛮力方法。它涉及系统地评估与拆分值和特征相关的增益。==
==Refer to my previous article on the subject:====  
  
====请参阅我之前关于该主题的文章：==
==The modern approach to training a Machine Learning model, and programs in general, involves using Differentiable Programming. If you are unfamiliar with the concept, I have written an introductory article on the subject:====  
  
====训练机器学习模型和一般程序的现代方法涉及使用可微分编程。如果你不熟悉这个概念，我写了一篇关于这个主题的介绍性文章：==
==Essentially, the idea is to identify the parameters to optimize in your model/program and use a gradient-based approach to find the optimal parameters. However, for this to work, your model must be differentiable with respect to the parameters of interest.====  
  
====从本质上讲，这个想法是确定要在模型/程序中优化的参数，并使用基于梯度的方法找到最佳参数。但是，要使此操作正常工作，您的模型必须相对于感兴趣的参数可微分。==
==Although Ensemble of Decision Trees employ a Gradient-Based approach for training, their construction process is not differentiable.====  
  
====尽管决策树集成采用基于梯度的训练方法，但它们的构建过程是不可区分的。==
## ==Building Decision Trees is not a differentiable process====  
  
====构建决策树不是一个可微分的过程==
==A decision tree is a simple yet powerful data structure with numerous applications. When training such a structure for regression or classification tasks, we aim to identify three main parameters:====  
  
====决策树是一种简单而强大的数据结构，具有众多应用程序。在为回归或分类任务训练这样的结构时，我们的目标是确定三个主要参数：==
1. ==The feature to use for each decision node to minimize the error, which involves feature selection.====  
      
    ====用于每个决策节点以最小化误差的特征，这涉及特征选择。==
2. ==The threshold used to split the input data at each node. Data with a selected feature value greater than the threshold will be assigned to the right child node, while the rest will be assigned to the left child node.====  
      
    ====用于在每个节点拆分输入数据的阈值。所选特征值大于阈值的数据将分配给右侧子节点，而其余数据将分配给左侧子节点。==
3. ==The value assigned to the leaves of the tree.====  
      
    ====分配给树叶的值。==
==The first two steps are non-differentiable. In current implementations of Gradient Boosted Trees, the first step requires iterating over features and is non-smooth. The second step involves evaluating the gain using the resulting data partition and the lists of possible values. This process is also non-differentiable.====  
  
====前两个步骤是不可微分的。在梯度提升树的当前实现中，第一步需要迭代功能，并且是不平滑的。第二步涉及使用生成的数据分区和可能值列表评估增益。这个过程也是不可微分的。==
==Fortunately, in 2019, Popov, Morozov, and Babenko introduced a differentiable formulation of Decision Trees.====  
  
====幸运的是，在2019年，波波夫、莫罗佐夫和巴边科引入了决策树的可微分公式。==
==I highly recommend reading their paper, as it is always fruitful to gather information from the source. However, to facilitate comprehension, I have written some Python code using Jax.====  
  
====我强烈建议阅读他们的论文，因为从源头收集信息总是富有成效的。但是，为了便于理解，我使用 Jax 编写了一些 Python 代码。==
==The questions addressed in the paper are:====  
  
====本文涉及的问题是：==
1. ==How can we reformulate feature selection to make it smooth and differentiable?====  
      
    ====我们如何重新制定特征选择以使其平滑和可微分？==
2. ==How can we reformulate comparison to make it smooth and differentiable?====  
      
    ====我们如何重新制定比较以使其平滑和可微分？==
## ==Feature selection regularization====  
  
====特征选择正则化==
==As mentioned earlier, feature selection in standard Gradient Boosting algorithms is performed using an exhaustive search: we try each feature and keep the one that provides the highest gain.====  
  
====如前所述，标准梯度提升算法中的特征选择是使用详尽的搜索来执行的：我们尝试每个特征并保留提供最高增益的特征。==
==Instead of this iterative process, we need to reformulate the selection as a function that returns the value of the feature ensuring maximal gain.====  
  
====我们需要将选择重新表述为一个函数，而不是这个迭代过程，该函数返回确保最大增益的特征值。==
==First, let’s assume that the features are gathered in a vector. We need a way to extract the value of the selected feature from this array, ideally using vector arithmetic. Starting with the assumption that we already know the feature to retain, we can achieve this with a simple dot product:====  
  
====首先，让我们假设特征收集在一个向量中。我们需要一种方法从这个数组中提取所选特征的值，最好是使用向量算法。从我们已经知道要保留的特征的假设开始，我们可以通过一个简单的点积来实现这一点：==
==features = np.array([1.1, 3.2, 4.5])====  
  
====selection_weights = np.array([0, 0, 1])====  
  
====selected_feature_value = np.dot(selection_weights, features)====  
  
====print(selected_feature_value)==
==Hence, we can retrieve a specific value from an array of features by multiplying all of them by 0, except the one we need to keep, and summing all the products.====  
  
====因此，我们可以从特征数组中检索一个特定的值，方法是将它们全部乘以 0，除了我们需要保留的那个，然后对所有乘积求和。==
==Learning the feature that achieves the highest performance involves learning the vector== ==`selection_weights`== ==with the additional constraint that the sum of its elements must be equal to 1. Ideally, this 1 should be concentrated in a single element of the vector.====  
  
====学习实现最高性能的特征涉及学习具有附加约束的向量== ==`selection_weights`== ==，即其元素之和必须等于 1。理想情况下，此 1 应集中在向量的单个元素中。==
==Enforcing this constraint on the norm of the selection vector can be achieved using a function. An obvious candidate is the softmax function, which enforces the norm of the weight vector to be 1. However, softmax generates a smooth distribution and cannot generate a 1 for the highest weight and 0 for the others.====  
  
====可以使用函数对选择向量的范数强制执行此约束。一个明显的候选者是 softmax 函数，它强制权重向量的范数为 1。但是，softmax 生成平滑分布，并且不能为最高权重生成 1，为其他权重生成 0。==
==The following code snippet illustrates this:====  
  
====以下代码片段对此进行了说明：==
==import numpy as np====  
  
====from jax.nn import softmax====  
  
====from entmax_jax import entmax15, sparsemax==
==weights = np.array([2, 3, 5, 7, 11, 17])====  
  
====print(np.linalg.norm(softmax(weights)))==
==print(softmax(weights))==
==Not only is the norm not exactly equal to 1.0, but the distribution is spread across all dimensions.====  
  
====不仅范数不完全等于 1.0，而且分布分布在所有维度上。==
==Therefore, the NODE paper proposes using the entmax function instead:====  
  
====因此，NODE论文建议改用entmax函数：==
==import numpy as np====  
  
====from jax.nn import softmax====  
  
====from entmax_jax import entmax15, sparsemax==
==weights = np.array([2, 3, 5, 7, 11, 17])====  
  
====print(np.linalg.norm(entmax15(weights)))==
==print(entmax15(weights))==
==This appears to be a good choice, as it generates the expected result!====  
  
====这似乎是一个不错的选择，因为它会产生预期的结果！==
==By utilizing the dot product in conjunction with the== ==`entmax`== ==function, we can retrieve the value of a given feature.====  
  
====通过将点积与== ==`entmax`== ==函数结合使用，我们可以检索给定特征的值。==
==Differentiable feature selection can be as simple as:====  
  
====可微分特征选择可以像以下那样简单：==
==def pick_features(weights, features):====  
  
====feature = jnp.dot(entmax15(weights), features)====  
  
====return feature==
==During the training phase, the weights will be trained to learn which feature to keep for a given decision.====  
  
====在训练阶段，将训练权重以学习为给定决策保留哪个特征。==
## ==Introducing a threshold 引入阈值==
==The other parameter used in a decision node, which must be learned, is a threshold. If the value of the selected feature is greater than or equal to this threshold, the prediction follows the right path of the tree. Conversely, if the value is strictly lower, the left path is taken.====  
  
====决策节点中使用的另一个参数（必须学习）是阈值。如果所选特征的值大于或等于此阈值，则预测将遵循树的正确路径。相反，如果该值严格较低，则采用左侧路径。==
==A similar approach can be used to generate a 1 if the value is above the threshold or below it, by using a dot product and the== ==_entmax_== ==function, but with a vector of size 2. The first element represents the difference between the selected value and the threshold, while the second element is 0.====  
  
====如果值高于或低于阈值，则可以使用类似的方法生成 1，方法是使用点积和 entmax 函数，但向量大小为 2。第一个元素表示所选值与阈值之间的差异，而第二个元素为 0。==
==Thanks to the== ==_entmax_== ==function, if the value is below the threshold, the first element of the vector is negative. When applying the== ==_entmax_== ==function, the 0 is transformed into a 1, and the negative part becomes equal to 0. Conversely, when the value is greater than the threshold, the first element is 1 and the second element is 0.====  
  
====借助 entmax 函数，如果值低于阈值，则向量的第一个元素为负数。应用 entmax 函数时，0 转换为 1，负部分变为 0。相反，当该值大于阈值时，第一个元素为 1，第二个元素为 0。==
==Here is an example implementation:====  
  
====下面是一个实现示例：==
==import numpy as np====  
  
====import jax.numpy as jnp====  
  
====from jax.nn import softmax====  
  
====from entmax_jax import entmax15, sparsemax==
==def choose(feature, threshold, left, right):====  
  
====choices = jnp.array([left, right])====  
  
====steer = jnp.array([threshold - feature, 0])====  
  
====pred = jnp.dot(entmax15(steer), choices)====  
  
====return pred====  
  
====print(choose(12, 10, -1, 1))==
==print(choose(8, 10, -1, 1))==
==During the training process, the parameters to be learned are not only the threshold but also the weights attached to the right and left leaves of this simple 1-level tree.====  
  
====在训练过程中，要学习的参数不仅是阈值，还包括附加到这个简单 1 级树的左右叶子的权重。==
## ==Next steps 后续步骤==
==In this first article, we have seen how to regularize the highly non-smooth structure of Gradient Boosting, namely the decision tree. This is the minimum requirement to transition from the Gradient Boosting method to the Differentiable Programming paradigm that underlies Neural Networks.====  
  
====在第一篇文章中，我们已经看到了如何正则化梯度提升的高度非平滑结构，即决策树。这是从梯度提升方法过渡到神经网络基础可微编程范式的最低要求。==
==However, there are still three problems that need to be addressed:====  
  
====但是，仍有三个问题需要解决：==
1. ==How to extend the method to support multi-level decision trees?====  
      
    ====如何扩展方法以支持多级决策树？==
2. ==How to learn the parameters of such trees?====  
      
    ====如何学习这些树的参数？==
3. ==How can we ensure that we stay in the linear part of the== ==`entmax`====function, and ensure that gradient does not vanish?====  
      
    ====我们如何确保我们停留在== ==`entmax`== ==函数的线性部分，并确保梯度不会消失？==
==I will provide further details on these topics in a forthcoming article.====  
  
====我将在下一篇文章中提供有关这些主题的更多详细信息。==
==Update — the second article is online:====  
  
====更新 — 第二篇文章已上线：==
==In the meantime, I you want to improve your mastery of Gradient Boosting, check out my book on the subject:====  
  
====与此同时，我想提高你对梯度增强的掌握，看看我关于这个主题的书：==