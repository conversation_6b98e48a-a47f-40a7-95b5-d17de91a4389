---
Updated: 2024-05-19T09:19
tags:
  - AI->-Programming
  - AI->-RAG
  - AI->-llama
Created: 2024-05-19T08:59
---
[![](https://miro.medium.com/v2/da:true/resize:fit:500/1*6f6aBMyr1kksYuKdBSWGvQ.gif)](https://miro.medium.com/v2/da:true/resize:fit:500/1*6f6aBMyr1kksYuKdBSWGvQ.gif)
## ==The chunk size can make or break the retrieval. Here is how to determine the best chunk size for your use case.====  
  
====分块大小可以决定检索的成败。以下是如何根据使用情况确定最佳块大小的方法。==
[![](https://miro.medium.com/v2/resize:fill:55:55/1*Z4yG0-xCzgmOcPZaI7a77Q.jpeg)](https://miro.medium.com/v2/resize:fill:55:55/1*Z4yG0-xCzgmOcPZaI7a77Q.jpeg)
[![](https://miro.medium.com/v2/resize:fill:30:30/1*JyIThO-cLjlChQLb6kSlVQ.png)](https://miro.medium.com/v2/resize:fill:30:30/1*JyIThO-cLjlChQLb6kSlVQ.png)
[==Mandar Karhade, MD. PhD.==](https://pub.towardsai.net/@ithinkbot?source=post_page-----fae9019796b6--------------------------------)
==Published in==
[==Towards AI==](https://medium.com/towards-artificial-intelligence?source=post_page-----fae9019796b6--------------------------------)
==14 min read==
==6 days ago==
==_Today, we will examine chunk-size optimization during the development of an RAG application. We will assume that it is a business-specific use case. We will also observe how and where generic approaches to chunk-size finding can fail or excel._====  
  
====_今天，我们将研究 RAG 应用程序开发过程中的分块大小优化问题。我们将假设这是一个特定业务用例。我们还将观察寻找分块大小的通用方法是如何以及在哪些方面会失败或出色。_==
[![](https://miro.medium.com/v2/resize:fit:625/1*6f6aBMyr1kksYuKdBSWGvQ.gif)](https://miro.medium.com/v2/resize:fit:625/1*6f6aBMyr1kksYuKdBSWGvQ.gif)
==Let me ramble it a little bit! This part is important so that the decisions are apparent in the later part of the article.====  
  
====让我再啰嗦几句！这部分内容很重要，这样在文章的后半部分就能清楚地看到各项决定。==
# ==Translating Business problem to a solution====  
  
====将业务问题转化为解决方案==
==Assume that you are working at your company. The company has a bunch of historical documents that are organized somewhere in SharePoint. Your company has finally decided to invest in Generative AI. Now, you are tasked with creating an application that can find relevant information in the form of answers and provide it to your 200 employees. Let’s chunk your task into smaller issues.====  
  
====假设您正在公司工作。公司有大量的历史文档，这些文档被整理在 SharePoint 的某个地方。贵公司最终决定投资于生成式人工智能。现在，你的任务是创建一个应用程序，以答案的形式查找相关信息，并将其提供给 200 名员工。让我们把任务分成几个小问题。==
1. ==A store of documents (let's say you have 10000 documents)====  
      
    ====文件存储（假设有 10000 份文件）==
2. ==A way to retrieve information====  
      
    ====检索信息的方法==
3. ==A way to generate answer====  
      
    ====生成答案的方法==
4. ==UI/UX to deliver answers back to your team/users====  
      
    ====用户界面/用户体验将答案反馈给团队/用户==
==We will focus on only the store of documents and a way to retrieve information. Two critical issues in the production system are fault tolerance and Scalability/Latency —====  
  
====我们将只关注文件存储和检索信息的方法。生产系统中的两个关键问题是容错和可扩展性/延迟。==
## ==Fault tolerance 容错==
==There are two probabilities that we should be worried about.== ==`P1`== ==is the probability of making a mistake, and== ==`P2`== ==is the probability of harm because of such a mistake. The probability that an error can ruin your business== ==`(P)`== ==by having a serious impact is governed by== ==`P1 * P2`== ==. As developers and data science people, it is our job to minimize the total probability and communicate the calculation to the decision-makers in a language that they can understand (i.e., the efforts to decrease P from X% to X%). More on the communication part later, but just know that without ballpark numbers in $ / person-hrs and X% and Y%, the decision-makers will either make wrong decisions or be paralyzed.====  
  
====`P1`== ==是犯错的概率，而== ==`P2`== ==则是因犯错而造成危害的概率。一个错误会对您的业务== ==`(P)`== ==造成严重影响的概率由== ==`P1 * P2`== ==决定。作为开发人员和数据科学人员，我们的工作就是将总概率降到最低，并用决策者能够理解的语言将计算结果传达给他们（即努力将 P 从 X% 降到 X%）。关于沟通的内容稍后再谈，但我们只需知道，如果没有以美元/人-小时、X% 和 Y% 为单位的大致数字，决策者要么会做出错误的决定，要么会陷入瘫痪。==
==`P2`== ==is the probability that can ruin the business as it is independent of the methodology we use.== ==`P2`== ==is the harm inherent to the wrong answer. We can't change== ==`P2`== ==without changing the business process. So, for all intents, we will consider== ==`P2`== ==to be constant for each type of mistake our pipeline may make.== ==`P1`== ==is likely in our control through good decisions based on the methodology, technique, model of choice, infrastructure, etc.====  
  
====`P2`== ==`P2`== ==是错误答案所固有的危害。如果不改变业务流程，我们就无法改变 。因此，出于所有目的，我们将认为 对于我们的管道可能犯下的每一种错误都是恒定的。 很可能在我们的控制范围之内，我们可以根据方法、技术、选择的模型、基础设施等做出正确的决策。== ==`P2`== ==`P2`== ==`P1`==
## ==Scalability and Latency 可扩展性和延迟==
==From the usefulness point of view, some use cases will have a natural limit on how slow the system can respond to someone’s question. If it is an in-office application, then you could even wait for a minute to get an answer back. Also, if generally it takes 15 minutes to find the answer using CTRL+F and reading through documents until you find your answer, a 1-minute turnaround is actually great. However, if your use case requires an instant answer because it is part of a communication system, then your business decisions and, thereby, the choice of technology will differ. Long story short, scalability and latency are relative terms.====  
  
====从实用性的角度来看，有些使用案例会对系统响应问题的速度有一个自然限制。如果是办公室内的应用，那么你甚至可以等上一分钟才能得到回复。此外，如果使用 CTRL+F 和阅读文档直到找到答案一般需要 15 分钟，那么 1 分钟的周转时间实际上已经很不错了。但是，如果您的用例因为是通信系统的一部分而需要即时答复，那么您的业务决策和技术选择就会有所不同。长话短说，可扩展性和延迟是相对而言的。==
==In the last few years, I have seen most enterprise applications being fairly relaxed regarding latency. Only a few use-cases that are related to chatbots, communications, translation, assistants fall into the critical-low-latency group. The majority of other in-office or in-the-field support applications fall under the other bucket of 5 sec to 1 min latency is not that bad. It is still a huge upgrade compared to the humans doing the work.====  
  
====在过去几年中，我看到大多数企业应用在延迟方面都相当宽松。只有少数与聊天机器人、通信、翻译和助理相关的用例属于关键低延迟组。大多数其他办公室内或现场支持应用属于另一种情况，即 5 秒到 1 分钟的延迟并不算太差。与人类工作相比，这仍然是一个巨大的提升。==
==Rambling is over, now let's get our hands dirty.====  
  
====废话少说，现在让我们动手吧。==
# ==How do you decide what chunk size is the best in a generic use case?====  
  
====在一般用例中，如何确定哪种块大小最合适？==
==We will be using the most commonly available packages to reach a decision. We will use llama-index and OpenAI for this tutorial. I am assuming that you are running this code in Google Colab so that you can replicate it.== ==`qqq`== ==is to declare quiet installation; however, I would recommend not using it until you know that the installation actually works. Otherwise, you will not see the error too.====  
  
====我们将使用最常用的软件包来做出决定。本教程将使用 llama-index 和 OpenAI。我假定你是在 Google Colab 中运行这段代码，这样你就可以复制它。== ==`qqq`== ==是为了宣布安静安装；不过，我建议你在知道安装确实有效之前不要使用它。否则，你也不会看到错误。==
==_**Installing packages 安装软件包**_==
==!pip install -qqq nest-asyncio llama-index openai Cython torch torchvision==
==_**Importing all libraries. 导入所有库。**_==
==I have saved the openai_key in the Google colab secrets. You do not have to but====  
  
====我已将 openai_key 保存在 Google colab secrets 中。您不必这样做，但==
==from llama_index.core import (====  
  
====SimpleDirectoryReader,====  
  
====VectorStoreIndex,====  
  
====ServiceContext,====  
  
====)====  
  
====from llama_index.core.llama_dataset.generator import RagDatasetGenerator====  
  
====from llama_index.core.evaluation import (====  
  
====FaithfulnessEvaluator,====  
  
====RelevancyEvaluator====  
  
====)====  
  
====from llama_index.llms.openai import OpenAI====  
  
====import openai====  
  
====import time==
==from google.colab import userdata====  
  
====openai.api_key = userdata.get('openai_key')==
==import torch====  
  
====print(torch.__version__)==
==import nest_asyncio====  
  
====nest_asyncio.apply()==
==_**Getting data 获取数据**_==
==For the sake of the article, I have downloaded a few pdfs for the articles by searching ‘cancer’ in== [==https://www.ncbi.nlm.nih.gov/pmc==](https://www.ncbi.nlm.nih.gov/pmc)==, if you want to replicate what I am doing. I have added those PDFs to the folder named====  
  
====为了撰写这篇文章，我在 https://www.ncbi.nlm.nih.gov/pmc 中搜索 "癌症"，下载了几篇文章的 PDF 文件，如果你想复制我的做法的话。我已将这些 PDF 文件添加到名为====  
  
====`data`====. I will be using this as a demo store of my documents. You may have thousands of documents from your company here.====  
  
====`data`== ==.我将把它用作我的文档演示存储区。您的公司可能有成千上万的文件在这里。==
[![](https://miro.medium.com/v2/resize:fit:484/1*4rUlNmWIkB386OB6wMkYJA.png)](https://miro.medium.com/v2/resize:fit:484/1*4rUlNmWIkB386OB6wMkYJA.png)
==**_Loading data into document store**====  
  
====**将数据载入文件存储库_**==
==Let's load the data using the basic SimpleDirectoryReader from llama_index.core. Depending upon your data and storage solution, you may have to substitute this. I am using something so that we can quickly reach the point where we evaluate chunk sizes.====  
  
====让我们使用 llama_index.core 中的基本 SimpleDirectoryReader 来加载数据。根据你的数据和存储解决方案，你可能需要用它来替代。我使用的是一种可以快速评估块大小的方法。==
==documents = SimpleDirectoryReader("./data").load_data()====  
  
====print(len(documents))====  
  
====print(documents[0])====  
  
====eval_documents = documents[0:10]==
==The generic code provided by the Agentic method uses a sample of the documents for evaluation. However, in my case, 12 PDFs generated 288 documents. First 10 documents/chunks ended up from the 1st PDF itself. Please avoid this mistake as you are effectively testing if your chunk sizes are of appropriate sizes from a single document.====  
  
====Agentic 方法提供的通用代码使用文档样本进行评估。然而，在我的案例中，12 个 PDF 生成了 288 个文档。前 10 个文档/块最终来自第 1 个 PDF 本身。请避免这种错误，因为您实际上是从单个文档中测试您的块大小是否合适。==
==here is the modified code that will work better to get the random sample for the evaluation document. Select the appropriate N for the sample of evaluation documents before we move to the next step.====  
  
====以下是修改后的代码，它能更好地为评估文档获取随机样本。在进入下一步之前，请为评估文件样本选择合适的 N。==
==import random====  
  
====documents = SimpleDirectoryReader("./data").load_data()====  
  
====eval_documents = [documents[random.randint(0, len(documents)-1)] for _ in range(10)]==
==_**Generate Questions 生成问题**_==
==Now, we can generate some questions using a random sample of our documents. I have updated the code available on both the llama_index and RAGAS frameworks (the evaluation pipeline is really what they developed, which has gotten integrated into llama_index). There are many outdated/deprecated codes floating around on the official websites.====  
  
====现在，我们可以使用文档的随机样本生成一些问题。我已经更新了 llama_index 和 RAGAS 框架上的可用代码（评估管道实际上是他们开发的，已经集成到 llama_index 中）。官方网站上有很多过时/废弃的代码。==
==import time====  
  
====from llama_index.core import Settings====  
  
====from llama_index.embeddings.openai import OpenAIEmbedding==
==Settings.llm = OpenAI(model="gpt-3.5-turbo", temperature=0)====  
  
====Settings.embed_model = OpenAIEmbedding(model="text-embedding-3-small")==
==eval_questions_all = []====  
  
====num_questions_per_chunk = 1==
==data_generator = RagDatasetGenerator.from_documents(eval_documents)==
==eval_questions = data_generator.generate_questions_from_nodes()====  
  
====eval_questions_all.append(eval_questions.to_pandas()['query'].to_list())==
==faithfulness_gpt3_5_t = FaithfulnessEvaluator()====  
  
====relevancy_gpt3_5_t = RelevancyEvaluator()==
==questions = eval_questions.to_pandas()['query'].to_list()====  
  
====display(questions)==
==The output looks like the following. The questions are generic. You can tune how the questions are generated by defining the input query to the question generator and modifying its default behavior.====  
  
====输出结果如下。问题是通用的。您可以通过定义问题生成器的输入查询并修改其默认行为来调整问题的生成方式。==
==In my case, I created a query for my questions based on my context so that I could get questions relevant to my field of interest or my business at hand.====  
  
====就我而言，我根据自己的情况创建了一个问题查询，这样我就能得到与我感兴趣的领域或我手头业务相关的问题。==
==q_gen_query = f"You are a scientific researcher. \====  
  
====Your task is to setup {num_questions_per_chunk} questions. \====  
  
====The questions must be related to following \====  
  
====1. my interest 1 2.My interest 2 3. My interest 3 \====  
  
====Restrict the questions to the context information provided."====  
  
====data_generator = RagDatasetGenerator.from_documents(eval_documents,====  
  
====question_gen_query=q_gen_query)==
==[==
==**_Setting up the Experiment**====  
  
====**设置实验_**==
==Now we will set up the experiment to assess the quality of responses that we receive while using various chunk sizes. Which we have a handy function provided by RAGAS members, but again, the function was outdated, so I have modified it to make it up-to-date. Please feel free to use it as-is. Let me know if you see any errors, issues etc.====  
  
====现在，我们将进行实验，以评估在使用不同大小的分块时收到的响应质量。我们有一个由 RAGAS 成员提供的便捷函数，但该函数同样已经过时，因此我对其进行了修改，使其与时俱进。请放心使用。如果您发现任何错误或问题，请告诉我。==
==In short, for each question, we are going to pass our context, question, and answer and ask if the answer is relevant to the provided context. If the returning response is yes, then the model returns integer 1.0; otherwise, 0. We averaged all the questions and gave a score. You can ask for more details like the reasoning for either accepting or rejecting but that is for some other day. This is a topic for another day because this is likely business critical if your SMEs are going to evaluate the reasoning for the evaluators to confirm that evaluation is actually doing its job.====  
  
====简而言之，对于每个问题，我们都要传递上下文、问题和答案，并询问答案是否与所提供的上下文相关。如果返回的回答是 "是"，则模型返回整数 1.0；否则，返回 0。您可以询问更多细节，比如接受或拒绝的理由，但这是后话。这是另一个话题，因为如果你的中小型企业要对评估者的理由进行评估，以确认评估是否真正完成了任务，那么这很可能是至关重要的业务。==
==We are going to evaluate the time and accuracy using the function== ==`evaluate_response_time_and_accuracy()`== ==. Both P1 and P2 probabilities, as well as the latency, will be determined using these functions. Now, it's up to you to see how your experiment needs to be scaled up. If you need to test performance for a million questions, feel free to do so, but this experiment costs significant $$. In my experiment using GPT4, I ended up spending $70 to run an experiment on 25–40 questions.====  
  
====我们将使用函数== ==`evaluate_response_time_and_accuracy()`== ==来评估时间和准确度。P1 和 P2 概率以及延迟时间都将通过这些函数来确定。现在，您可以自行决定如何扩大实验规模。如果你需要测试一百万个问题的性能，请随意，但这项实验需要花费大量的美元。在我使用 GPT4 进行的实验中，我最终花了 70 美元对 25-40 个问题进行实验。==
==def evaluate_response_time_and_accuracy(chunk_size, eval_questions):====  
  
====total_response_time = 0====  
  
====total_faithfulness = 0====  
  
====total_relevancy = 0==
```plain
response\_vectors = \[\]  
  
Settings.llm = OpenAI(model="gpt-3.5-turbo", temperature=0, chunk\_size = chunk\_size )  
  
Settings.chunk\_size = chunk\_size  
Settings.chunk\_overlap = round(chunk\_size/10,0)  
Settings.embed\_model = OpenAIEmbedding(model="text-embedding-3-small", embed\_batch\_size=100)
    vector\_index = VectorStoreIndex.from\_documents(eval\_documents)
query\_engine = vector\_index.as\_query\_engine()  
num\_questions = len(eval\_questions)
for question in eval\_questions:  
    start\_time = time.time()   
      
    response\_vector = query\_engine.query(question)
    elapsed\_time = time.time() - start\_time
              
    faithfulness\_result = faithfulness\_gpt3\_5\_t.evaluate\_response( response=response\_vector ).passing
    relevancy\_result = relevancy\_gpt3\_5\_t.evaluate\_response( query=question, response=response\_vector ).passing
              
    response\_vectors.append({"chunk\_size" : chunk\_size,  
                             "question" : question,  
                             "response\_vector" : response\_vector,  
                             "faithfulness\_result" : faithfulness\_result,  
                             "relevancy\_result" : relevancy\_result})
    total\_response\_time += elapsed\_time  
    total\_faithfulness += faithfulness\_result  
    total\_relevancy += relevancy\_result
      
average\_response\_time = total\_response\_time / num\_questions  
average\_faithfulness = total\_faithfulness / num\_questions  
average\_relevancy = total\_relevancy / num\_questions
return average\_response\_time, average\_faithfulness, average\_relevancy, response\_vectors
```
==We will run this experiment and collect responses for a range of chunk sizes from 128 to 2048 in 2x increments.====  
  
====我们将运行该实验，并以 2 倍的增量收集从 128 到 2048 的一系列块大小的响应。==
==The experiment will look like this. I will emphasize that the more chunk ranges you use, the more you will pay. If you are running these experiments using local LLMs, then this could be a great way to optimize chunking upfront. Remember that as long as your embeddings model and retriever are local, you should be able to run such experiment on a mass scale. Once the retriever gathers its responses, you can pass them to OpenAI or whatever enterprise model you choose and get the generative response back.====  
  
====实验将是这样的。我要强调的是，使用的分块范围越大，支付的费用就越高。如果你使用本地LLMs 来运行这些实验，那么这可能是一个前期优化分块的好方法。请记住，只要您的嵌入模型和检索器是本地的，您就可以大规模地运行此类实验。一旦检索器收集到响应，你就可以将其传递给 OpenAI 或任何你选择的企业模型，并得到生成响应。==
==response_vectors_all = []====  
  
====for chunk_size in [128, 256, 512, 1024, 2048]:====  
  
====avg_time, avg_faithfulness, avg_relevancy, response_vectors = evaluate_response_time_and_accuracy(chunk_size, all_questions)====  
  
====[response_vectors_all.append(i) for i in response_vectors]====  
  
====print(f"Chunk size {chunk_size} - Average Response time: {avg_time:.2f}s, Average Faithfulness: {avg_faithfulness:.2f}, Average Relevancy: {avg_relevancy:.2f}")====  
  
====time.sleep(20)==
==Here is the output I got. Depending upon your Faithfulness and Relevance, you can determine the best chunk size for you. In my case, I would have chosen 512 or 1024 as the number of vectors is the smallest using larger chunk sizes. This is especially true if I was dealing with a million PDFs and needed to keep the number of vectors in my vector database to the smallest. If I had a limited number of PDFs, I could have chosen smaller chunk sizes, too.====  
  
====以下是我得到的输出结果。根据你的忠实度和相关性，你可以确定最适合你的块大小。就我而言，我会选择 512 或 1024，因为使用较大的块大小，向量的数量是最少的。如果我需要处理一百万个 PDF 文件，并且需要将矢量数据库中的矢量数量保持在最小范围内，那就更是如此了。如果我的 PDF 数量有限，我也可以选择较小的块大小。==
==Chunk size 128 - Average Response time: 2.07s, Average Faithfulness: 1.00, Average Relevancy: 1.00====  
  
====Chunk size 256 - Average Response time: 2.12s, Average Faithfulness: 1.00, Average Relevancy: 1.00====  
  
====Chunk size 512 - Average Response time: 2.25s, Average Faithfulness: 1.00, Average Relevancy: 1.00====  
  
====Chunk size 1024 - Average Response time: 2.38s, Average Faithfulness: 1.00, Average Relevancy: 1.00====  
  
====Chunk size 2048 - Average Response time: 2.55s, Average Faithfulness: 0.47, Average Relevancy: 0.60==
# ==Parting throughts 临别赠言==
==This is a generic way of testing if the chunk sizes are appropriate for your use case. This does not mean that it is “THE WAY” to do it. Chunk size determination is as much of an art as it is a science.====  
  
====这是一种测试块大小是否适合使用情况的通用方法。但这并不意味着这是 "唯一 "的方法。确定分块大小既是一门艺术，也是一门科学。==
## ==Model Choice 机型选择==
==Chunk size decisions are sensitive to the choice of embedding model. Chunk sizes are not transferrable from one model to another. So, if you decide to change your embedding model, you will have to redo the experiment.====  
  
====分块大小的决定对嵌入模型的选择很敏感。分块大小不能从一个模型转移到另一个模型。因此，如果您决定改变嵌入模型，就必须重新进行实验。==
## ==Knowledge Tuning 知识调整==
==Chunk size decisions are sensitive to the questions that you have generated. These questions generated using models like GPT-3.5-turbo or GPT-4 are generic. You can tune it up to an extent, but to be truly enterprise-ready, you may have to create a set of your own questions. Another big issue I have noticed is that the business users have an underlying knowledge of the system. Therefore, they do not include all the details necessary in the question (during its use in production). When a question is generated by GPT-4, it takes every single aspect of the sentence and drops 1. The question is about the dropped aspect of the sentence. In reality, this is never true. We, as users, ask general questions about the text and expect specific answers from the model. There is an art in how to ask a question to the deployed application. A significant user training is necessary for them to be able to find the correct answer.====  
  
====分块大小的决定对您生成的问题很敏感。使用 GPT-3.5-turbo 或 GPT-4 等模型生成的这些问题是通用的。您可以在一定程度上对其进行调整，但要真正为企业做好准备，您可能必须创建一套自己的问题。我注意到的另一个大问题是，企业用户对系统有基本的了解。因此，他们不会在问题中包含所有必要的细节（在生产中使用时）。当 GPT-4 生成一个问题时，它会将句子的每个方面都包含在内，然后去掉其中的一个方面。问题就是关于句子中被去掉的方面。在现实生活中，情况并非如此。作为用户，我们会提出关于文本的一般性问题，并期待模型给出具体的答案。如何向已部署的应用程序提问是一门艺术。必须对用户进行大量培训，使他们能够找到正确的答案。==
## ==Custom Evaluation metrics====  
  
====自定义评估指标==
==Faithfulness and relevance are two quality indicators of the responses. However, they might not be the best responses for your use case. Most businesses will need to generate their own faithfulness indicators for their usage. Consider these estimates as the template for generating your own quality indicators. If you can use humans to evaluate responses, that's even better. This is a mixed approach to a human-in-the-loop at the evaluation stage. Please do not consider default prompts used to evaluate the responses. If any of your engineers are blindly using it, don't hesitate to== [==contact me==](https://mandar-karhade.square.site/)==.====  
  
====忠实性和相关性是衡量回复质量的两个指标。但是，它们可能不是适合您的使用案例的最佳回复。大多数企业需要根据自己的使用情况生成自己的忠实度指标。将这些估计值视为生成您自己的质量指标的模板。如果可以使用人工来评估回复，那就更好了。这是一种在评估阶段采用人在环的混合方法。请不要考虑用于评估响应的默认提示。如果您的工程师正在盲目使用，请随时联系我。==
## ==The document processing 文件处理==
==The elephant in the room is your input document. Yes, I showed the pipeline using PDFs without even thinking about the PDF itself. The biggest question is, how to process PDF that maintains the most from the PDF. You could use advanced methods to separate text, tables, and images. You could convert text into markdown (in my opinion, the most compressed representation of the PDF compared to HTML/XML/JSON); you could use various embedding models for all 3 and conduct similar experiments with or without humans in the loop.====  
  
====房间里的大象就是你的输入文档。是的，我展示了使用 PDF 的流水线，却没有考虑到 PDF 本身。最大的问题是，如何处理 PDF，以保持 PDF 的最大价值。你可以使用高级方法来分离文本、表格和图片。您可以将文本转换为 markdown（在我看来，与 HTML/XML/JSON 相比，它是最能压缩 PDF 的表示形式）；您可以为这三种格式使用不同的嵌入模型，并在有人类参与或没有人类参与的情况下进行类似的实验。==
==Our target is to decrease the== ==`P`== ==and prove to your leadership that it is actually decreasing business risk (by changing the cost-benefit ratio in some tangible way). It is data-scientist’s job to explain but it is the leadership’s job to take the business risk. If you need help in evaluating and communicating to turn the leadership’s mind around,== [==contact me==](https://mandar-karhade.square.site/)==. Leaders, if you are having difficulty communicating or evaluating your use case and need a 2nd pair of eyes and ears to check, please== [==contact me==](https://mandar-karhade.square.site/)==.====  
  
====我们的目标是降低== ==`P`== ==，并向您的领导层证明，这实际上是在降低业务风险（通过某种切实的方式改变成本效益比）。解释是数据科学家的工作，但承担业务风险则是领导层的工作。如果你在评估和沟通方面需要帮助，以扭转领导层的想法，请联系我。领导者，如果您在沟通或评估使用案例时遇到困难，需要第二双眼睛和耳朵来检查，请与我联系。==
## ==Your Solution is Bespoke 为您量身定制解决方案==
==Anyone who says that they can solve all of your needs by creating a generic pipeline? Run as fast as you can. Your business case is likely Bespoke whether you like it or not. Do I have all the solutions? No. Can I help? Yes. Can I help you evaluate the right technical teams? Yes. I have advised businesses to develop a realistic strategy, budget, and timeline for Generative AI implementation, including the composition of the working teams, setting expectations, goals, and roadmap.====  
  
====有人说他们可以通过创建一个通用管道来解决您的所有需求吗？有多快跑多快。无论你喜欢与否，你的业务案例都可能是定制的。我有所有的解决方案吗？没有。可以。我能帮您评估合适的技术团队吗？可以。我曾建议企业制定切实可行的生成式人工智能实施战略、预算和时间表，包括工作团队的组成、设定期望、目标和路线图。==
==Btw, I did not say it before, but you just learned the Agentic method of evaluating the RAG chunk size. There were 3 agents: the first question generator, 2nd was the faithfulness evaluator, and 3rd was the relevancy evaluator. Congrats on learning the Agentic method ;)====  
  
====顺便说一下，我之前没有说过，但你刚刚学会了评估 RAG 块大小的代理方法。有 3 个代理：第一个是问题生成器，第二个是忠实性评估器，第三个是相关性评估器。恭喜您学会了代理方法；)==