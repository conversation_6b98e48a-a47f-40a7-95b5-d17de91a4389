---
DocFlag:
  - ToBeTested
Updated: 2023-08-23T23:00
tags:
  - AI->-Model
  - AI->-Voice
Created: 2023-08-23T23:00
---
[![](https://qiita-user-contents.imgix.net/https%3A%2F%2Fcdn.qiita.com%2Fassets%2Fpublic%2Farticle-ogp-background-9f5428127621718a910c8b63951390ad.png?ixlib=rb-4.0.0&w=1200&mark64=aHR0cHM6Ly9xaWl0YS11c2VyLWNvbnRlbnRzLmltZ2l4Lm5ldC9-dGV4dD9peGxpYj1yYi00LjAuMCZ3PTkxNiZ0eHQ9TWV0YSVFMyU4MSVBRVNlYW1sZXNzTTRUJUUzJTgyJTkyJUU4JUE5JUE2JUUzJTgxJTk3JUUzJTgxJUE2JUUzJTgxJUJGJUUzJTgyJThCJTIwb24lMjBEYXRhYnJpY2tzJnR4dC1jb2xvcj0lMjMyMTIxMjEmdHh0LWZvbnQ9SGlyYWdpbm8lMjBTYW5zJTIwVzYmdHh0LXNpemU9NTYmdHh0LWNsaXA9ZWxsaXBzaXMmdHh0LWFsaWduPWxlZnQlMkN0b3Amcz00ZjI0NDc4MWM0NjlmYTczMzA3NGY2ODVlYTc2Nzc1MA&mark-x=142&mark-y=112&blend64=aHR0cHM6Ly9xaWl0YS11c2VyLWNvbnRlbnRzLmltZ2l4Lm5ldC9-dGV4dD9peGxpYj1yYi00LjAuMCZ3PTYxNiZ0eHQ9JTQwaXNhbmFrYW1pc2hpcm8yJnR4dC1jb2xvcj0lMjMyMTIxMjEmdHh0LWZvbnQ9SGlyYWdpbm8lMjBTYW5zJTIwVzYmdHh0LXNpemU9MzYmdHh0LWFsaWduPWxlZnQlMkN0b3Amcz1jOTQ1ZjI1MWY1MzFjOTljNDc2OWE1MTE1YTMxNjY3OQ&blend-x=142&blend-y=491&blend-mode=normal&s=d797aad8b896bcb5235a5e619bff8124)](https://qiita-user-contents.imgix.net/https%3A%2F%2Fcdn.qiita.com%2Fassets%2Fpublic%2Farticle-ogp-background-9f5428127621718a910c8b63951390ad.png?ixlib=rb-4.0.0&w=1200&mark64=aHR0cHM6Ly9xaWl0YS11c2VyLWNvbnRlbnRzLmltZ2l4Lm5ldC9-dGV4dD9peGxpYj1yYi00LjAuMCZ3PTkxNiZ0eHQ9TWV0YSVFMyU4MSVBRVNlYW1sZXNzTTRUJUUzJTgyJTkyJUU4JUE5JUE2JUUzJTgxJTk3JUUzJTgxJUE2JUUzJTgxJUJGJUUzJTgyJThCJTIwb24lMjBEYXRhYnJpY2tzJnR4dC1jb2xvcj0lMjMyMTIxMjEmdHh0LWZvbnQ9SGlyYWdpbm8lMjBTYW5zJTIwVzYmdHh0LXNpemU9NTYmdHh0LWNsaXA9ZWxsaXBzaXMmdHh0LWFsaWduPWxlZnQlMkN0b3Amcz00ZjI0NDc4MWM0NjlmYTczMzA3NGY2ODVlYTc2Nzc1MA&mark-x=142&mark-y=112&blend64=aHR0cHM6Ly9xaWl0YS11c2VyLWNvbnRlbnRzLmltZ2l4Lm5ldC9-dGV4dD9peGxpYj1yYi00LjAuMCZ3PTYxNiZ0eHQ9JTQwaXNhbmFrYW1pc2hpcm8yJnR4dC1jb2xvcj0lMjMyMTIxMjEmdHh0LWZvbnQ9SGlyYWdpbm8lMjBTYW5zJTIwVzYmdHh0LXNpemU9MzYmdHh0LWFsaWduPWxlZnQlMkN0b3Amcz1jOTQ1ZjI1MWY1MzFjOTljNDc2OWE1MTE1YTMxNjY3OQ&blend-x=142&blend-y=491&blend-mode=normal&s=d797aad8b896bcb5235a5e619bff8124)
---
Metaが面白そうなものを出したので、Databricksで試してみました。
[Introducing a foundational multimodal model for speech translationhttps://ai.meta.com](https://ai.meta.com/blog/seamless-m4t/)
# SeamlessM4Tとは
Gigazineさんの記事でも紹介されています。詳しくはこちらを見ていただくのがいいと思います。
[https://gigazine.net/news/20230823-meta-speech-translation-transcription-seamlessm4t/https://gigazine.net](https://gigazine.net/news/20230823-meta-speech-translation-transcription-seamlessm4t/)
記事から抜粋：

> Metaが音声を入力するだけで「文字起こし」「別言語への翻訳」「別言語への吹き替え」を実行できるAI「SeamlessM4T」を2023年8月22日(火)に公開しました。
> 
> (中略)
> 
> 文字起こしAIや翻訳AIは複数存在していますが、既存のAIの多くは「文字起こしだけ」「翻訳だけ」といったように単一の機能しか備えていません。Metaが新たに開発したSeamlessM4TはマルチモーダルなAIで、単一のAIだけで「文字起こし」「別言語への翻訳」「別言語への吹き替え」といった複数の操作を実行できます。
というわけで、音声・テキストのマルチモーダルモデルです。
Metaの公式ブログ上では、下図のようにWhisper V2よりも英語でのBLUEのスコアが優れていると伝えいます。
![[https3A2F2Fscontent-nrt1-1.xx.fbcdn.net2Fv2Ft39.8562-62F368798207_671112214955968_27445120162469134_n.jpg3F_nc_cat3D10926ccb3D1-726_nc_sid3D6825c526_nc_ohc3D-mmXsuErRmcAX8pJmmB26_nc_oc3DAQmJvl1cczfbpDRCBvR8tuQUGMJZj7Qjj1bvdLQKld008hEy5J1C-OXWKXA5yHbBYrk26_nc_ht3Dscontent-nrt1-1.xx26oh3d00_afbfpijd7bad7atrlc0_nsatewjvlatp-hg5hxwhmtoj7g26oe3d64ea4bd0]]
なお、モデルは公開されていますが、現状研究用途を想定したものであり、ライセンスはCC-BY-NC 4.0で商用利用不可です。
# Databricksで動かしてみる
SeamlessM4Tはローカルでも動作可能です。
インストール方法の詳細は公式github上にあります。
[GitHub - facebookresearch/seamless_communication: Foundational Models ...https://github.com](https://github.com/facebookresearch/seamless_communication)
また、hugging face上にモデルが公開されており、Pythonでの動かし方も記載してあります。
この内容に従ってDatabricks上で動かしてみます。
[facebook/seamless-m4t-large · Hugging Facehttps://huggingface.co](https://huggingface.co/facebook/seamless-m4t-large)
環境はAWS Databricks、DBRは13.2ML、クラスタタイプ`g5.2xlarge`のクラスタを利用しました。
## インストール
ノートブックを作成して、gitリポジトリを適当なところにクローン。
```Plain
%sh mkdir /tmp/seamessm4t && cd /tmp/seamessm4t && git clone https://github.com/facebookresearch/seamless_communication.git
```
クローンしたリポジトリを指定してインストール。
```Plain
%pip install /tmp/seamessm4t/seamless_communication
```
libsndfileのインストール。
※ DBR13.2MLには最初からインストールされているようで、この工程は不要だったかも。
```Plain
%sh apt-get --yes install libsndfile1
```
## Translatorの作成
モデルはlargeとmediumがあります。今回はlargeを試しました。
```Plain
import torch
from seamless_communication.models.inference import Translator
# Initialize a Translator object with a multitask model, vocoder on the GPU.
translator = Translator("seamlessM4T_large", vocoder_name_or_card="vocoder_36langs", device=torch.device("cuda:0"))
```
モデルがダウンロードされるのを待って、準備完了。
(torchのキャッシュとして保管されるようで、キャッシュの場所は/root/.cache/torch/hub内でした)
## Text-to-Text translation(T2TT)を試す
まずはベタに日英翻訳。
```Plain
# T2TT
input_text = "大規模言語モデルとは？"
target_lang = "eng"
src_lang = "jpn"
translated_text, _, _ = translator.predict(input_text, "t2tt", target_lang, src_lang=src_lang)
translated_text
```
Output
```Plain
CString('What is a large-scale language model?')
```
単純な文章ではありますが、ちゃんと翻訳されてますね。
## Text-to-Speech Translation(T2ST)を試す
```Plain
# T2ST
input_text = "トンネルを抜けると、そこは雪国だった。"
target_lang = "eng"
src_lang = "jpn"
translated_text, wav, sr = translator.predict(input_text, "t2st", target_lang, src_lang=src_lang)
translated_text
```
Output
```Plain
CString('When we got through the tunnel, it was snowing.')
```
また、wavに音声データが返却されるようになっているので、一度保存します。
```Plain
import torchaudio
# Save the translated audio generation.
torchaudio.save(
    "/tmp/seamlessm4t.wav",
    wav[0].cpu(),
    sample_rate=sr,
)
```
再生。
```Plain
import IPython
IPython.display.Audio("/tmp/seamlessm4t.wav")
```
翻訳された音声データが出来ています。
## Speech-to-Text Translation(S2TT)を試す
SeamlessM4Tが出力した内容をそのままInputとして使ってみます。
```Plain
# S2TT
target_lang = "eng"
translated_text, _, _ = translator.predict("/tmp/seamlessm4t.wav", "s2tt", target_lang)
translated_text
```
Output
```Plain
CString('When we got through the tunnel, it was snowing.')
```
ちゃんと認識されました。
# まとめ
いくつか試してみている感じだと、出力テキスト通りに音声データが必ずしも出来るわけではないみたいですね。
また、公式デモだと日本語翻訳がExperiment扱いになっていて、欧米系の言語に比べると精度落ちそうな感じですね。人物固有名詞を英訳するとHeとかになったし、ユースケースはちょっと考える必要があるかも。
あと、まだ詳しく確認できていませんが、Fine Tuningの方法とかもリポジトリ内のdocにありデータや環境を揃えられればいろいろ面白いことができそうです。
個人的にマルチモーダルモデルには興味があり、継続的に勉強したり動かしたりしてみたいと思います。
あと、これを使って、LLMに対するプロンプト指示を日本語で音声入力→を日英翻訳→LLMで推論→結果を英日翻訳＆音声出力とかしてみたいなあ。
プロンプトは英語指定の方が精度上がる傾向にあるため、そのあたりを楽に行えるような仕組を準備するのもいいなと思います。