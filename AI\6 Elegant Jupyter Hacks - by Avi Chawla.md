---
Updated: 2024-06-27T22:02
tags:
  - AI->-Programming
URL: https://blog.dailydoseofds.com/p/6-elegant-jupyter-hacks
Created: 2024-06-27T17:01
---
_**[Advertise to 76k readers](https://forms.gle/TFXp9ZzQzRbK76sh6)**_ _**|**_ _**[Deep Dives](https://www.dailydoseofds.com/membership/)**_
Despite the widespread usage of Jupyter notebooks, many users do not use them to their full potential.
They tend to use Jupyter using its default interface/capabilities, which, in my opinion, can be largely improved to provide a richer experience.
Today, let me share some of the coolest things I have learned about Jupy<PERSON> after using it for so many years.
Let’s begin!
### \#1) Retrieve a cell’s output in Jupyter
Many Jupyter users often forget to assign the results of a Jupyter cell to a variable.
So they have to (unwillingly) rerun the cell and assign it to a variable.
But very few know that IPython provides a dictionary `Out`, which you can use to retrieve a cell’s output.
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fec3e6caa-f4c9-4e19-8625-fa9ce150b01f_2464x1784.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fec3e6caa-f4c9-4e19-8625-fa9ce150b01f_2464x1784.png)
Just specify the cell number as the dictionary’s key. This will return the corresponding output.
### \#2) Enrich the default preview of a DataFrame
Often when we load a DataFrame in Jupyter, we preview it by printing, as shown below:
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fd7cda878-3e41-45bd-b1d7-b49173848b80_720x342.webp)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fd7cda878-3e41-45bd-b1d7-b49173848b80_720x342.webp)
However, it hardly tells anything about what’s inside this data.
Instead, use [Jupyter-DataTables](https://bit.ly/jupyter-datatables).
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F172aa12b-0fa1-4f53-9e62-6a6f93907b7c_1280x1046.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F172aa12b-0fa1-4f53-9e62-6a6f93907b7c_1280x1046.png)
It supercharges the default preview of a DataFrame with many useful features, as depicted above.
This richer preview provides sorting, filtering, exporting, and pagination operations along with column distribution and data types.
### \#3) Generate helpful hints as you write Pandas code
Pandas has many unoptimized methods.
They can significantly slow down data analysis if you use them.
[Dovpanda](https://bit.ly/py-dovpanda) is a pretty cool tool that gives suggestions/warnings about your data manipulation steps.
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F263abfea-d4c3-423a-9bd6-8d1f5bdefc19_2368x1896.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F263abfea-d4c3-423a-9bd6-8d1f5bdefc19_2368x1896.png)
Whenever we use any unoptimized methods, it automatically prompts a warning and a suggestion.
### \#4) Improve rendering of DataFrames
[In a recent issue on Sparklines](https://www.blog.dailydoseofds.com/p/sparklines-the-hidden-gem-of-data), we learned that whenever we display a DataFrame in Jupyter, it is rendered using HTML and CSS.
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F6a7a9835-a6c3-4485-85ce-cb14dab05989_688x329.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F6a7a9835-a6c3-4485-85ce-cb14dab05989_688x329.png)
This means that we can format its output just like web pages.
One thing that many Jupyter users do is that they preview **raw DataFrames** for data analysis tasks.
But unknown to them, styling can make data analysis much easier and faster, as depicted below:
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F32b3e2f8-bf64-4aac-ab53-6e4d1b4e1c12_2664x2640.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F32b3e2f8-bf64-4aac-ab53-6e4d1b4e1c12_2664x2640.png)
The above styling provides so much clarity over a raw DataFrame.
To style Pandas DataFrames, use its Styling API (𝗱𝗳.𝘀𝘁𝘆𝗹𝗲). As a result, the DataFrame is rendered with the specified styling.
### \#5) Restart the Jupyter kernel without losing variables
While working in a Jupyter Notebook, you may want to restart the kernel due to several reasons.
If there are any active data/model objects, most users dump them to disk, restart the kernel, and then load them back.
But this is never needed.
Use the **%store magic command**.
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb86362fc-bc43-4860-b287-b05fee4e7a08_2168x1680.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb86362fc-bc43-4860-b287-b05fee4e7a08_2168x1680.png)
It allows you to store and retrieve a variable back even if you restart the kernel.
This way, you can avoid the hassle of dumping an object to disk.
### \#6) Search code in all Jupyter Notebooks from the terminal
**Context:** I have published over 600 newsletter issues so far.
So my local directory is filled with Jupyter notebooks (392 as of today), which accompany the code for most of the issues published here.
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fab5e8a0b-d10e-4b77-83a7-5ce8d283c117_748x156.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fab5e8a0b-d10e-4b77-83a7-5ce8d283c117_748x156.png)
Thus, if I ever wanted to refer to some code I wrote previously in a Jupyter notebook, it became tough to find that specific notebook.
This involved plenty of manual effort.
But later, I discovered an open-source tool — **[nbcommands](https://bit.ly/nbcommands)****.**
Using this, we can search for code in Jupyter Notebook right from the terminal:
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F6a765e0f-15a6-447d-a5f3-d3ae1793a15e_2048x1401.jpeg)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F6a765e0f-15a6-447d-a5f3-d3ae1793a15e_2048x1401.jpeg)
A task that often used to take me 5-10 minutes now takes me only a couple of seconds.
Pretty cool, isn’t it?
That’s it for today.
Hope you learned something new :)
**👉** Over to you: What are some other cool Jupyter hacks that you are aware of?
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F4622a4cb-31b3-46e6-8fa6-53df2f208717_2104x984.jpeg)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F4622a4cb-31b3-46e6-8fa6-53df2f208717_2104x984.jpeg)
- **1 Referral**: Unlock 450+ practice questions on NumPy, Pandas, and SQL.
- **2 Referrals**: Get access to advanced Python OOP deep dive.
- **3 Referrals**: Get access to the PySpark deep dive for big-data mastery.
Get your unique referral link:
### **Are you overwhelmed with the amount of information in ML/DS?**
Every week, I publish no-fluff deep dives on topics that truly matter to your skills for ML/DS roles.
[![](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fa463bde0-2a7a-4758-8557-b6122ab4a4d9_2065x826.png)](https://substackcdn.com/image/fetch/w_1456,c_limit,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fa463bde0-2a7a-4758-8557-b6122ab4a4d9_2065x826.png)
For instance:
- [A Beginner-friendly Introduction to Kolmogorov Arnold Networks (KANs)](https://www.dailydoseofds.com/a-beginner-friendly-introduction-to-kolmogorov-arnold-networks-kan/).
- [5 Must-Know Ways to Test ML Models in Production (Implementation Included)](https://www.dailydoseofds.com/5-must-know-ways-to-test-ml-models-in-production-implementation-included/).
- [A Beginner-Friendly Guide to Multi-GPU Model Training](https://www.dailydoseofds.com/a-beginner-friendly-guide-to-multi-gpu-model-training/).
- [Understanding LoRA-derived Techniques for Optimal LLM Fine-tuning](https://www.dailydoseofds.com/understanding-lora-derived-techniques-for-optimal-llm-fine-tuning/)
- [8 Fatal (Yet Non-obvious) Pitfalls and Cautionary Measures in Data Science](https://www.dailydoseofds.com/8-fatal-yet-non-obvious-pitfalls-and-cautionary-measures-in-data-science/)
- [Implementing Parallelized CUDA Programs From Scratch Using CUDA Programming](https://www.dailydoseofds.com/implementing-massively-parallelized-cuda-programs-from-scratch-using-cuda-programming/)
- [You Are Probably Building Inconsistent Classification Models Without Even Realizing](https://www.dailydoseofds.com/you-are-probably-building-inconsistent-classification-models-without-even-realizing/).
- [How To (Immensely) Optimize Your Machine Learning Development and Operations with MLflow](https://www.dailydoseofds.com/how-to-immensely-optimize-your-machine-learning-development-and-operations-with-mlflow/).
- And many many more.
Join below to unlock all full articles:
### **SPONSOR US**
Get your product in front of more than 76,000 data scientists and other tech professionals.
Our newsletter puts your products and services directly in front of an audience that matters — thousands of leaders, senior data scientists, machine learning engineers, data analysts, etc., who have influence over significant tech decisions and big purchases.
To ensure your product reaches this influential audience, reserve your space **[here](https://forms.gle/F32h8etrPVh3pvrTA)** or reply to this email.