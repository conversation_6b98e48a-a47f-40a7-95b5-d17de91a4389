---
Updated: 2024-03-07T21:29
tags:
  - AI->-Voice
Created: 2024-03-07T21:27
---
[![](https://opengraph.githubassets.com/158130a30c61258afec96d274b531599d66f8782f1f60e196ed8e7e141e46333/speechbrain/speechbrain)](https://opengraph.githubassets.com/158130a30c61258afec96d274b531599d66f8782f1f60e196ed8e7e141e46333/speechbrain/speechbrain)
[![](https://raw.githubusercontent.com/speechbrain/speechbrain/develop/docs/images/speechbrain-logo.svg)](https://raw.githubusercontent.com/speechbrain/speechbrain/develop/docs/images/speechbrain-logo.svg)
[![](https://camo.githubusercontent.com/4eeff8d5f70efe445111e484ffca701c5384202955d2c92dbe21cd9c74c41583/68747470733a2f2f726561646d652d747970696e672d7376672e64656d6f6c61622e636f6d3f666f6e743d466972612b436f64652673697a653d3430266475726174696f6e3d373030302670617573653d313030302672616e646f6d3d66616c73652677696474683d31323030266865696768743d313030266c696e65733d53696d706c6966792b436f6e766572736174696f6e616c2b41492b446576656c6f706d656e74)](https://camo.githubusercontent.com/4eeff8d5f70efe445111e484ffca701c5384202955d2c92dbe21cd9c74c41583/68747470733a2f2f726561646d652d747970696e672d7376672e64656d6f6c61622e636f6d3f666f6e743d466972612b436f64652673697a653d3430266475726174696f6e3d373030302670617573653d313030302672616e646f6d3d66616c73652677696474683d31323030266865696768743d313030266c696e65733d53696d706c6966792b436f6e766572736174696f6e616c2b41492b446576656c6f706d656e74)
==| 📘== [==Tutorials==](https://speechbrain.github.io/tutorial_basics.html) ==| 🌐== [==Website==](https://speechbrain.github.io/) ==| 📚== [==Documentation==](https://speechbrain.readthedocs.io/en/latest/index.html) ==| 🤝== [==Contributing==](https://speechbrain.readthedocs.io/en/latest/contributing.html) ==| 🤗== [==HuggingFace==](https://huggingface.co/speechbrain) ==| ▶️== [==YouTube==](https://www.youtube.com/@SpeechBrainProject) ==| 🐦== [==X==](https://twitter.com/SpeechBrain1) ==|==
==_Please, help our community project. Star on GitHub!_==
[![](https://camo.githubusercontent.com/430a1f09b28d4c28bc18b8ac2d9abf09e5d097707578bda89643900878efca79/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f73746172732f737065656368627261696e2f737065656368627261696e3f7374796c653d736f6369616c)](https://camo.githubusercontent.com/430a1f09b28d4c28bc18b8ac2d9abf09e5d097707578bda89643900878efca79/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f73746172732f737065656368627261696e2f737065656368627261696e3f7374796c653d736f6369616c)
==**Exciting News (January, 2024):**== ==Discover what is new in SpeechBrain 1.0== [==here==](https://colab.research.google.com/drive/1IEPfKRuvJRSjoxu22GZhb3czfVHsAy0s?usp=sharing)==!==
## ==🗣️💬 What SpeechBrain Offers==
- ==SpeechBrain is an== ==**open-source**== [==PyTorch==](https://pytorch.org/) ==toolkit that accelerates== ==**Conversational AI**== ==development, i.e., the technology behind== ==_speech assistants_====,== ==_chatbots_====, and== ==_large language models_====.==
- ==It is crafted for fast and easy creation of advanced technologies for== ==**Speech**== ==and== ==**Text**== ==Processing.==
## ==🌐 Vision==
- ==With the rise of== [==deep learning==](https://www.deeplearningbook.org/)==, once-distant domains like speech processing and NLP are now very close. A well-designed neural network and large datasets are all you need.==
- ==We think it is now time for a== ==**holistic toolkit**== ==that, mimicking the human brain, jointly supports diverse technologies for complex Conversational AI systems.==
- ==This spans== ==_speech recognition_====,== ==_speaker recognition_====,== ==_speech enhancement_====,== ==_speech separation_====,== ==_language modeling_====,== ==_dialogue_====, and beyond.==
## ==📚 Training Recipes==
- ==We share over 200 competitive training== [==recipes==](https://github.com/speechbrain/speechbrain/tree/develop/recipes) ==on more than 40 datasets supporting 20 speech and text processing tasks (see below).==
- ==We support both training from scratch and fine-tuning pretrained models such as== [==Whisper==](https://huggingface.co/openai/whisper-large)==,== [==Wav2Vec2==](https://huggingface.co/docs/transformers/model_doc/wav2vec2)==,== [==WavLM==](https://huggingface.co/docs/transformers/model_doc/wavlm)==,== [==Hubert==](https://huggingface.co/docs/transformers/model_doc/hubert)==,== [==GPT2==](https://huggingface.co/gpt2)==,== [==Llama2==](https://huggingface.co/docs/transformers/model_doc/llama2)==, and beyond. The models on== [==HuggingFace==](https://huggingface.co/) ==can be easily plugged in and fine-tuned.==
- ==For any task, you train the model using these commands:==
==python train.py hparams/train.yaml==
- ==The hyperparameters are encapsulated in a YAML file, while the training process is orchestrated through a Python script.==
- ==We maintained a consistent code structure across different tasks.==
- ==For better replicability, training logs and checkpoints are hosted on Dropbox.==
## ==Pretrained Models and Inference==
- ==Access over 100 pretrained models hosted on== [==HuggingFace==](https://huggingface.co/speechbrain)==.==
- ==Each model comes with a user-friendly interface for seamless inference. For example, transcribing speech using a pretrained model requires just three lines of code:==
==from speechbrain.pretrained import EncoderDecoderASR  
asr_model = EncoderDecoderASR.from_hparams(source="speechbrain/asr-conformer-transformerlm-librispeech", savedir="pretrained_models/asr-transformer-transformerlm-librispeech")  
asr_model.transcribe_file("speechbrain/asr-conformer-transformerlm-librispeech/example.wav")  
==
## ==Documentation==
- ==We are deeply dedicated to promoting inclusivity and education.==
- ==We have authored over 30== [==tutorials==](https://speechbrain.github.io/) ==on Google Colab that not only describe how SpeechBrain works but also help users familiarize themselves with Conversational AI.==
- ==Every class or function has clear explanations and examples that you can run. Check out the== [==documentation==](https://speechbrain.readthedocs.io/en/latest/index.html) ==for more details 📚.==
## ==🎯 Use Cases==
- ==🚀== ==**Research Acceleration**====: Speeding up academic and industrial research. You can develop and integrate new models effortlessly, comparing their performance against our baselines.==
- ==⚡️== ==**Rapid Prototyping**====: Ideal for quick prototyping in time-sensitive projects.==
- ==🎓== ==**Educational Tool**====: SpeechBrain's simplicity makes it a valuable educational resource. It is used by institutions like== [==Mila==](https://mila.quebec/en/)==,== [==Concordia University==](https://www.concordia.ca/)==,== [==Avignon University==](https://univ-avignon.fr/en/)==, and many others for student training.==
## ==🚀 Quick Start==
==To get started with SpeechBrain, follow these simple steps:==
## ==🛠️ Installation==
### ==Install via PyPI==
1. ==Install SpeechBrain using PyPI:==
2. ==Access SpeechBrain in your Python code:==
### ==Install from GitHub==
==This installation is recommended for users who wish to conduct experiments and customize the toolkit according to their needs.==
1. ==Clone the GitHub repository and install the requirements:==
    
    ==git clone https://github.com/speechbrain/speechbrain.git  
    cd speechbrain  
    pip install -r requirements.txt  
    pip install --editable .  
    ==
    
2. ==Access SpeechBrain in your Python code:==
==Any modifications made to the== ==`speechbrain`== ==package will be automatically reflected, thanks to the== ==`--editable`== ==flag.==
## ==✔️ Test Installation==
==Ensure your installation is correct by running the following commands:==
==pytest tests  
pytest --doctest-modules speechbrain  
==
## ==🏃‍♂️ Running an Experiment==
==In SpeechBrain, you can train a model for any task using the following steps:==
==cd recipes/<dataset>/<task>/  
python experiment.py params.yaml  
==
==The results will be saved in the== ==`output_folder`== ==specified in the YAML file.==
## ==📘 Learning SpeechBrain==
- ==**Website:**== ==Explore general information on the== [==official website==](https://speechbrain.github.io/)==.==
- ==**Tutorials:**== ==Start with== [==basic tutorials==](https://speechbrain.github.io/tutorial_basics.html) ==covering fundamental functionalities. Find advanced tutorials and topics in the Tutorials menu on the== [==SpeechBrain website==](https://speechbrain.github.io/)==.==
- ==**Documentation:**== ==Detailed information on the SpeechBrain API, contribution guidelines, and code is available in the== [==documentation==](https://speechbrain.readthedocs.io/en/latest/index.html)==.==
## ==🔧 Supported Technologies==
- ==SpeechBrain is a versatile framework designed for implementing a wide range of technologies within the field of Conversational AI.==
- ==It excels not only in individual task implementations but also in combining various technologies into complex pipelines.==
## ==🎙️ Speech/Audio Processing==
==Tasks==
==Datasets==
==Technologies/Models==
==Speech Recognition==
[==AISHELL-1==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/AISHELL-1)==,== [==CommonVoice==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/CommonVoice)==,== [==DVoice==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/DVoice)==,== [==KsponSpeech==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/KsponSpeech)==,== [==LibriSpeech==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/LibriSpeech)==,== [==MEDIA==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/MEDIA)==,== [==RescueSpeech==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/RescueSpeech)==,== [==Switchboard==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/Switchboard)==,== [==TIMIT==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/TIMIT)==,== [==Tedlium2==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/Tedlium2)==,== [==Voicebank==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/Voicebank)
[==CTC==](https://www.cs.toronto.edu/~graves/icml_2006.pdf)==,== [==Tranducers==](https://arxiv.org/pdf/1211.3711.pdf?origin=publication_detail)==,== [==Transformers==](https://arxiv.org/abs/1706.03762)==,== [==Seq2Seq==](http://zhaoshuaijiang.com/file/Hybrid_CTC_Attention_Architecture_for_End-to-End_Speech_Recognition.pdf)==,== [==Beamsearch techniques for CTC==](https://arxiv.org/pdf/1911.01629.pdf)==,==[==seq2seq==](https://arxiv.org/abs/1904.02619.pdf)==,==[==transducers==](https://www.merl.com/publications/docs/TR2017-190.pdf)==),== [==Rescoring==](https://arxiv.org/pdf/1612.02695.pdf)==,== [==Conformer==](https://arxiv.org/abs/2005.08100)==,== [==Branchformer==](https://arxiv.org/abs/2207.02971)==,== [==Hyperconformer==](https://arxiv.org/abs/2305.18281)==,== [==Kaldi2-FST==](https://github.com/k2-fsa/k2)
==Speaker Recognition==
[==VoxCeleb==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/VoxCeleb)
[==ECAPA-TDNN==](https://arxiv.org/abs/2005.07143)==,== [==ResNET==](https://arxiv.org/pdf/1910.12592.pdf)==,== [==Xvectors==](https://www.danielpovey.com/files/2018_icassp_xvectors.pdf)==,== [==PLDA==](https://ieeexplore.ieee.org/document/6639151)==,== [==Score Normalization==](https://www.sciencedirect.com/science/article/abs/pii/S1051200499903603)
==Speech Separation==
[==WSJ0Mix==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/WSJ0Mix)==,== [==LibriMix==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/LibriMix)==,== [==WHAM!==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/WHAMandWHAMR)==,== [==WHAMR!==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/WHAMandWHAMR)==,== [==Aishell1Mix==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/Aishell1Mix)==,== [==BinauralWSJ0Mix==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/BinauralWSJ0Mix)
[==SepFormer==](https://arxiv.org/abs/2010.13154)==,== [==RESepFormer==](https://arxiv.org/abs/2206.09507)==,== [==SkiM==](https://arxiv.org/abs/2201.10800)==,== [==DualPath RNN==](https://arxiv.org/abs/1910.06379)==,== [==ConvTasNET==](https://arxiv.org/abs/1809.07454)
==Speech Enhancement==
[==DNS==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/DNS)==,== [==Voicebank==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/Voicebank)
[==SepFormer==](https://arxiv.org/abs/2010.13154)==,== [==MetricGAN==](https://arxiv.org/abs/1905.04874)==,== [==MetricGAN-U==](https://arxiv.org/abs/2110.05866)==,== [==SEGAN==](https://arxiv.org/abs/1703.09452)==,== [==spectral masking==](http://staff.ustc.edu.cn/~jundu/Publications/publications/Trans2015_Xu.pdf)==,== [==time masking==](http://staff.ustc.edu.cn/~jundu/Publications/publications/Trans2015_Xu.pdf)
==Text-to-Speech==
[==LJSpeech==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/LJSpeech)==,== [==LibriTTS==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/LibriTTS)
[==Tacotron2==](https://arxiv.org/abs/1712.05884)==,== [==Zero-Shot Multi-Speaker Tacotron2==](https://arxiv.org/abs/2112.02418)==,== [==FastSpeech2==](https://arxiv.org/abs/2006.04558)
==Vocoding==
[==LJSpeech==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/LJSpeech)==,== [==LibriTTS==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/LibriTTS)
[==HiFiGAN==](https://arxiv.org/abs/2010.05646)==,== [==DiffWave==](https://arxiv.org/abs/2009.09761)
==Spoken Language Understanding==
[==MEDIA==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/MEDIA)==,== [==SLURP==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/SLURP)==,== [==Fluent Speech Commands==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/fluent-speech-commands)==,== [==Timers-and-Such==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/timers-and-such)
[==Direct SLU==](https://arxiv.org/abs/2104.01604)==,== [==Decoupled SLU==](https://arxiv.org/abs/2104.01604)==,== [==Multistage SLU==](https://arxiv.org/abs/2104.01604)
==Speech-to-Speech Translation==
[==CVSS==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/CVSS)
[==Discrete Hubert==](https://arxiv.org/pdf/2106.07447.pdf)==,== [==HiFiGAN==](https://arxiv.org/abs/2010.05646)==,== [==wav2vec2==](https://arxiv.org/abs/2006.11477)
==Speech Translation==
[==Fisher CallHome (Spanish)==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/Fisher-Callhome-Spanish)==,== [==IWSLT22(lowresource)==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/IWSLT22_lowresource)
[==wav2vec2==](https://arxiv.org/abs/2006.11477)
==Emotion Classification==
[==IEMOCAP==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/IEMOCAP)==,== [==ZaionEmotionDataset==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/ZaionEmotionDataset)
[==ECAPA-TDNN==](https://arxiv.org/abs/2005.07143)==,== [==wav2vec2==](https://arxiv.org/abs/2006.11477)==,== [==Emotion Diarization==](https://arxiv.org/abs/2306.12991)
==Language Identification==
[==VoxLingua107==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/VoxLingua107)==,== [==CommonLanguage==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/CommonLanguage)
[==ECAPA-TDNN==](https://arxiv.org/abs/2005.07143)
==Voice Activity Detection==
[==LibriParty==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/LibriParty)
[==CRDNN==](https://arxiv.org/abs/2106.04624)
==Sound Classification==
[==ESC50==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/ESC50)==,== [==UrbanSound==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/UrbanSound8k)
[==CNN14==](https://github.com/ranchlai/sound_classification)==,== [==ECAPA-TDNN==](https://arxiv.org/abs/2005.07143)
==Self-Supervised Learning==
[==CommonVoice==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/CommonVoice)==,== [==LibriSpeech==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/LibriSpeech)
[==wav2vec2==](https://arxiv.org/abs/2006.11477)
==Interpretabiliy==
[==ESC50==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/ESC50)
[==Learning-to-Interpret (L2I)==](https://proceedings.neurips.cc/paper_files/paper/2022/file/e53280d73dd5389e820f4a6250365b0e-Paper-Conference.pdf)==,== [==Non-Negative Matrix Factorization (NMF)==](https://proceedings.neurips.cc/paper_files/paper/2022/file/e53280d73dd5389e820f4a6250365b0e-Paper-Conference.pdf)==,== [==PIQ==](https://arxiv.org/abs/2303.12659)
==Speech Generation==
[==AudioMNIST==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/AudioMNIST)
[==Diffusion==](https://arxiv.org/abs/2006.11239)==,== [==Latent Diffusion==](https://arxiv.org/abs/2112.10752)
==Metric Learning==
[==REAL-M==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/REAL-M/sisnr-estimation)==,== [==Voicebank==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/Voicebank)
[==Blind SNR-Estimation==](https://arxiv.org/abs/2002.08909)==,== [==PESQ Learning==](https://arxiv.org/abs/2110.05866)
==Allignment==
[==TIMIT==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/TIMIT)
[==CTC==](https://www.cs.toronto.edu/~graves/icml_2006.pdf)==,== [==Viterbi==](https://www.cs.cmu.edu/~cga/behavior/rabiner1.pdf)==,== [==Forward Forward==](https://www.cs.cmu.edu/~cga/behavior/rabiner1.pdf)
==Diarization==
[==AMI==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/AMI)
[==ECAPA-TDNN==](https://arxiv.org/abs/2005.07143)==,== [==X-vectors==](https://www.danielpovey.com/files/2018_icassp_xvectors.pdf)==,== [==Spectral Clustering==](http://www.ifp.illinois.edu/~hning2/papers/Ning_spectral.pdf)
## ==📝 Text Processing==
==Tasks==
==Datasets==
==Technologies/Models==
==Language Modeling==
[==CommonVoice==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/CommonVoice)==,== [==LibriSpeech==](https://github.com/speechbrain/speechbrain/tree/unstable-v0.6/recipes/LibriSpeech)
[==n-grams==](https://web.stanford.edu/~jurafsky/slp3/3.pdf)==,== [==RNNLM==](https://www.fit.vutbr.cz/research/groups/speech/publi/2010/mikolov_interspeech2010_IS100722.pdf)==,== [==TransformerLM==](https://arxiv.org/abs/1706.03762)
==Response Generation==
[==MultiWOZ==](https://github.com/speechbrain/speechbrain/tree/unstable-v0.6/recipes/MultiWOZ/response_generation)
[==GPT2==](https://d4mucfpksywv.cloudfront.net/better-language-models/language_models_are_unsupervised_multitask_learners.pdf)==,== [==Llama2==](https://arxiv.org/abs/2307.09288)
==Grapheme-to-Phoneme==
[==LibriSpeech==](https://github.com/speechbrain/speechbrain/tree/develop/recipes/LibriSpeech)
[==RNN==](https://arxiv.org/abs/2207.13703)==,== [==Transformer==](https://arxiv.org/abs/2207.13703)==,== [==Curriculum Learning==](https://arxiv.org/abs/2207.13703)==,== [==Homograph loss==](https://arxiv.org/abs/2207.13703)
## ==🔍 Additional Features==
==SpeechBrain includes a range of native functionalities that enhance the development of Conversational AI technologies. Here are some examples:==
- ==**Training Orchestration:**== ==The== ==`Brain`== ==class serves as a fully customizable tool for managing training and evaluation loops over data. It simplifies training loops while providing the flexibility to override any part of the process.==
- ==**Hyperparameter Management:**== ==A YAML-based hyperparameter file specifies all hyperparameters, from individual numbers (e.g., learning rate) to complete objects (e.g., custom models). This elegant solution drastically simplifies the training script.==
- ==**Dynamic Dataloader:**== ==Enables flexible and efficient data reading.==
- ==**GPU Training:**== ==Supports single and multi-GPU training, including distributed training.==
- ==**Dynamic Batching:**== ==On-the-fly dynamic batching enhances the efficient processing of variable-length signals.==
- ==**Mixed-Precision Training:**== ==Accelerates training through mixed-precision techniques.==
- ==**Efficient Data Reading:**== ==Reads large datasets efficiently from a shared Network File System (NFS) via== [==WebDataset==](https://github.com/webdataset/webdataset)==.==
- ==**Hugging Face Integration:**== ==Interfaces seamlessly with== [==HuggingFace==](https://huggingface.co/speechbrain) ==for popular models such as wav2vec2 and Hubert.==
- ==**Orion Integration:**== ==Interfaces with== [==Orion==](https://github.com/Epistimio/orion) ==for hyperparameter tuning.==
- ==**Speech Augmentation Techniques:**== ==Includes SpecAugment, Noise, Reverberation, and more.==
- ==**Data Preparation Scripts:**== ==Includes scripts for preparing data for supported datasets.==
==SpeechBrain is rapidly evolving, with ongoing efforts to support a growing array of technologies in the future.==
## ==📊 Performance==
- ==SpeechBrain integrates a variety of technologies, including those that achieves competitive or state-of-the-art performance.==
- ==For a comprehensive overview of the achieved performance across different tasks, datasets, and technologies, please visit== [==here==](https://github.com/speechbrain/speechbrain/blob/develop/PERFORMANCE.md)==.==
## ==📜 License==
- ==SpeechBrain is released under the== [==Apache License, version 2.0==](https://www.apache.org/licenses/LICENSE-2.0)==, a popular BSD-like license.==
- ==You are free to redistribute SpeechBrain for both free and commercial purposes, with the condition of retaining license headers. Unlike the GPL, the Apache License is not viral, meaning you are not obligated to release modifications to the source code.==
## ==🔮Future Plans==
==We have ambitious plans for the future, with a focus on the following priorities:==
- ==**Scale Up:**== ==Our aim is to provide comprehensive recipes and technologies for training massive models on extensive datasets.==
- ==**Scale Down:**== ==While scaling up delivers unprecedented performance, we recognize the challenges of deploying large models in production scenarios. We are focusing on real-time, streamable, and small-footprint Conversational AI.==
## ==🤝 Contributing==
- ==SpeechBrain is a community-driven project, led by a core team with the support of numerous international collaborators.==
- ==We welcome contributions and ideas from the community. For more information, check== [==here==](https://speechbrain.github.io/contributing.html)==.==
## ==🙏 Sponsors==
- ==SpeechBrain is an academically driven project and relies on the passion and enthusiasm of its contributors.==
- ==As we cannot rely on the resources of a large company, we deeply appreciate any form of support, including donations or collaboration with the core team.==
- ==If you're interested in sponsoring SpeechBrain, please reach out to us at== <EMAIL>==.==
- ==A heartfelt thank you to all our sponsors, including the current ones:==
[![](https://camo.githubusercontent.com/7b332c00f1a373bfde84c8f053b975cc7b7439171c1af83244f7af5e4b2ee52c/68747470733a2f2f68756767696e67666163652e636f2f66726f6e742f6173736574732f68756767696e67666163655f6c6f676f2e737667)](https://camo.githubusercontent.com/7b332c00f1a373bfde84c8f053b975cc7b7439171c1af83244f7af5e4b2ee52c/68747470733a2f2f68756767696e67666163652e636f2f66726f6e742f6173736574732f68756767696e67666163655f6c6f676f2e737667)
[![](https://camo.githubusercontent.com/99d3944baf0f821ca2317f19374d067814a6ab6ad5e8b1c5cded545d30533d8e/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f73706f6e736f72732f6c6f676f5f76642e706e67)](https://camo.githubusercontent.com/99d3944baf0f821ca2317f19374d067814a6ab6ad5e8b1c5cded545d30533d8e/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f73706f6e736f72732f6c6f676f5f76642e706e67)
[![](https://camo.githubusercontent.com/8c7e9720fcd234c40318935a221ea4b10ab0d8b014347bff73ec26924b4a64ad/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f73706f6e736f72732f6c6f676f5f6e6c652e706e67)](https://camo.githubusercontent.com/8c7e9720fcd234c40318935a221ea4b10ab0d8b014347bff73ec26924b4a64ad/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f73706f6e736f72732f6c6f676f5f6e6c652e706e67)
[![](https://camo.githubusercontent.com/5d236774d7f1c6aa45d42e306da1c26bdde4529986c3a840ceb27ad24eb03836/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f73706f6e736f72732f6c6f676f5f6f76682e706e67)](https://camo.githubusercontent.com/5d236774d7f1c6aa45d42e306da1c26bdde4529986c3a840ceb27ad24eb03836/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f73706f6e736f72732f6c6f676f5f6f76682e706e67)
[![](https://camo.githubusercontent.com/199fcb8225c8f4283be509ea05d591879825cb423bde89ea932d85cd9d1e7f90/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f73706f6e736f72732f6c6f676f5f626164752e706e67)](https://camo.githubusercontent.com/199fcb8225c8f4283be509ea05d591879825cb423bde89ea932d85cd9d1e7f90/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f73706f6e736f72732f6c6f676f5f626164752e706e67)
[![](https://camo.githubusercontent.com/dc9cce3e840ee6a7cbeafdadf4a9573cf9e293b9581bed4571f7b8b7efe32dbd/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f73706f6e736f72732f73616d73756e675f6f6666696369616c2e706e67)](https://camo.githubusercontent.com/dc9cce3e840ee6a7cbeafdadf4a9573cf9e293b9581bed4571f7b8b7efe32dbd/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f73706f6e736f72732f73616d73756e675f6f6666696369616c2e706e67)
[![](https://camo.githubusercontent.com/9e66ca4401689b4e8fd5de6a7c70233beebc087fe7b499d5ed2a7ab4f7eb6f36/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f73706f6e736f72732f6c6f676f5f6d696c615f736d616c6c2e706e67)](https://camo.githubusercontent.com/9e66ca4401689b4e8fd5de6a7c70233beebc087fe7b499d5ed2a7ab4f7eb6f36/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f73706f6e736f72732f6c6f676f5f6d696c615f736d616c6c2e706e67)
[![](https://camo.githubusercontent.com/ce6c5b0aa3dc997ba870aba270aa5cac0cbbd87e9a1c07860531d56df5c18d90/68747470733a2f2f7777772e636f6e636f726469612e63612f636f6e74656e742f64616d2f636f6d6d6f6e2f6c6f676f732f436f6e636f726469612d6c6f676f2e6a706567)](https://camo.githubusercontent.com/ce6c5b0aa3dc997ba870aba270aa5cac0cbbd87e9a1c07860531d56df5c18d90/68747470733a2f2f7777772e636f6e636f726469612e63612f636f6e74656e742f64616d2f636f6d6d6f6e2f6c6f676f732f436f6e636f726469612d6c6f676f2e6a706567)
[![](https://camo.githubusercontent.com/e869e8b5763c07743bea26efc25b17360c807bdb6afe49b057c0942bb3256482/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f706172746e6572732f6c6f676f5f6c69612e706e67)](https://camo.githubusercontent.com/e869e8b5763c07743bea26efc25b17360c807bdb6afe49b057c0942bb3256482/68747470733a2f2f737065656368627261696e2e6769746875622e696f2f696d672f706172746e6572732f6c6f676f5f6c69612e706e67)
## ==📖 Citing SpeechBrain==
==If you use SpeechBrain in your research or business, please cite it using the following BibTeX entry:==
==@misc{speechbrain,  
title={{SpeechBrain}: A General-Purpose Speech Toolkit},  
author={Mirco Ravanelli and Titouan Parcollet and Peter Plantinga and Aku Rouhe and Samuele Cornell and Loren Lugosch and Cem Subakan and Nauman Dawalatabad and Abdelwahab Heba and Jianyuan Zhong and Ju-Chieh Chou and Sung-Lin Yeh and Szu-Wei Fu and Chien-Feng Liao and Elena Rastorgueva and François Grondin and William Aris and Hwidong Na and Yan Gao and Renato De Mori and Yoshua Bengio},  
year={2021},  
eprint={2106.04624},  
archivePrefix={arXiv},  
primaryClass={eess.AS},  
note={arXiv:2106.04624}  
}  
==