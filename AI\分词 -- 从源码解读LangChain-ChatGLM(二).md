---
Updated: 2023-07-23T19:07
tags:
  - AI->-LangChain
  - AI->-Programming
Created: 2023-07-23T19:08
---
[![](https://pic1.zhimg.com/v2-6da6b7aa57a360f52fc2f2bb2ac33646_720w.jpg?source=172ae18b)](https://pic1.zhimg.com/v2-6da6b7aa57a360f52fc2f2bb2ac33646_720w.jpg?source=172ae18b)
---
![[v2-6da6b7aa57a360f52fc2f2bb2ac33646_1440w.jpg]]
本系列的目的之一是沿着LangChain流程，对其中的文本信息建模思想和方法进行梳理和学习。对于每个子任务，我会先梳理Langchain中该任务的实现方法，试图以此了解NLP中该任务的研究现状和思路。针对一些典型情况（中文/英文，通用方法/特定任务），我会用语料做一些实验，希望能得到些微灵感。
## 一、分词（Text Splitting）in LangChain
分词是将长文本分解为以字词句等更小粒度分析单元的方法。本文主要涉及LangChain内置的几种分词方法。LangChain 的 TextSplitter 接口有两个主要参数：
- chunk_size : 文本分割的滑窗长度
- chunk_overlap：重叠滑窗长度
```Plain
class TextSplitter(BaseDocumentTransformer, ABC):
    """Interface for splitting text into chunks."""
    def __init__(
        self,
        chunk_size: int = 4000,
        chunk_overlap: int = 200,
        length_function: Callable[[str], int] = len,
    )
```
### 1. CharacterTextSplitter：
“CharacTextSplitter” 调用 Python 自带的 split( ) 方法，可以用自定义分隔符进行文本切分。
```Plain
class CharacterTextSplitter(TextSplitter):
    """Implementation of splitting text that looks at characters."""
    def __init__(self, separator: str = "\n\n", **kwargs: Any):
        """Create a new TextSplitter."""
        super().__init__(**kwargs)
        self._separator = separator
    def split_text(self, text: str) -> List[str]:
        """Split incoming text and return chunks."""
        # First we naively split the large input into a bunch of smaller ones.
        if self._separator:
            splits = text.split(self._separator)
        else:
            splits = list(text)
        return self._merge_splits(splits, self._separator)
```
基于“CharacTextSplitter”，LangChain自带对一些脚本语言分词的支持，例如：
```Plain
class MarkdownTextSplitter(RecursiveCharacterTextSplitter):
    """Attempts to split the text along Markdown-formatted headings."""
    def __init__(self, **kwargs: Any):
        """Initialize a MarkdownTextSplitter."""
        separators = [
            # First, try to split along Markdown headings (starting with level 2)
            "\n## ",
            "\n### ",
            "\n#### ",
            "\n##### ",
            "\n###### ",
            # Note the alternative syntax for headings (below) is not handled here
            # Heading level 2
            # ---------------
            # End of code block
            "```\n\n",
            # Horizontal lines
            "\n\n***\n\n",
            "\n\n---\n\n",
            "\n\n___\n\n",
            # Note that this splitter doesn't handle horizontal lines defined
            # by *three or more* of ***, ---, or ___, but this is not handled
            "\n\n",
            "\n",
            " ",
            "",
        ]
        super().__init__(separators=separators, **kwargs)

class LatexTextSplitter(RecursiveCharacterTextSplitter):
    """Attempts to split the text along Latex-formatted layout elements."""
    def __init__(self, **kwargs: Any):
        """Initialize a LatexTextSplitter."""
        separators = [
            # First, try to split along Latex sections
            "\n\\chapter{",
            "\n\\section{",
            "\n\\subsection{",
            "\n\\subsubsection{",
            # Now split by environments
            "\n\\begin{enumerate}",
            "\n\\begin{itemize}",
            "\n\\begin{description}",
            "\n\\begin{list}",
            "\n\\begin{quote}",
            "\n\\begin{quotation}",
            "\n\\begin{verse}",
            "\n\\begin{verbatim}",
            ## Now split by math environments
            "\n\\begin{align}",
            "$$",
            "$",
            # Now split by the normal type of lines
            " ",
            "",
        ]
        super().__init__(separators=separators, **kwargs)

class PythonCodeTextSplitter(RecursiveCharacterTextSplitter):
    """Attempts to split the text along Python syntax."""
    def __init__(self, **kwargs: Any):
        """Initialize a PythonCodeTextSplitter."""
        separators = [
            # First, try to split along class definitions
            "\nclass ",
            "\ndef ",
            "\n\tdef ",
            # Now split by the normal type of lines
            "\n\n",
            "\n",
            " ",
            "",
        ]
        super().__init__(separators=separators, **kwargs)
```
### 2. TokenTextSplitter
TokenTextSplitter 是一种字节对编码（Byte Pair Encoder，BPE）方法，而BPE是一种自然语言处理领域中被经常使用的数据压缩算法。TokenTextSplitter 来自 [tiktoken](https://link.zhihu.com/?target=https%3A//github.com/openai/tiktoken) 库，tiktoken 库最先被用在 GPT-2 中，并随之被开源。我们首先介绍 LangChain 中内置的 TokenTextSplitter 实现和 TokenTextSplitter 中可以调用的 tokenizer 方法。
```Plain
class TokenTextSplitter(TextSplitter):
    """Implementation of splitting text that looks at tokens."""
    def __init__(
        self,
        encoding_name: str = "gpt2",
        model_name: Optional[str] = None,
        allowed_special: Union[Literal["all"], AbstractSet[str]] = set(),
        disallowed_special: Union[Literal["all"], Collection[str]] = "all",
        **kwargs: Any,
    ):
        """Create a new TextSplitter."""
        super().__init__(**kwargs)
        if model_name is not None:
            enc = tiktoken.encoding_for_model(model_name)
        else:
            enc = tiktoken.get_encoding(encoding_name)
        self._tokenizer = enc
        self._allowed_special = allowed_special
        self._disallowed_special = disallowed_special
```
可以看到LangChain 的 TokenTextSplitter 方法有两个主要参数：
- encoding_name & model_name : 使用的编码
- allowed_special & disallowed_special : 特殊token的使用许可
TokenTextSplitter 主要可以通过两种方法初始化 encoder：
```Plain
# 以下两种方式的结果一致
encoding = tiktoken.get_encoding("cl100k_base")
encoding = tiktoken.encoding_for_model("gpt-4")
encoding = tiktoken.get_encoding("cl100k_base")
print(encoding.encode("tiktoken is great!"))
# [83, 1609, 5963, 374, 2294, 0]
print(encoding.decode([83, 1609, 5963, 374, 2294, 0]))
# "tiktoken is great!"
print([encoding.decode_single_token_bytes(token) for token in [83, 1609, 5963, 374, 2294, 0]])
# [b't', b'ik', b'token', b' is', b' great', b'!']
```
编码和使用该编码的模型对应关系如下，我们也可以用内置方法[训练自己的encoder](https://link.zhihu.com/?target=https%3A//github.com/openai/tiktoken%23what-is-bpe-anyway):
|   |   |
|---|---|
|Encoding name|OpenAI models|
|cl100k_base|gpt-4, gpt-3.5-turbo, text-embedding-ada-002|
|p50k_base|Codex models, text-davinci-002, text-davinci-003|
|r50k_base (or gpt2)|GPT-3 models like davinci|
下面介绍allowed_special & disallowed_special。在 tiktoken 的注释里这样写道：
```Plain
# special_tokens: A dictionary mapping special token strings to their token values.
```
我的理解是我们可以创建一些特殊字符和编码的映射，特殊的字符可以表示序列上下文信息，可以对应特殊的任务(mask 与补全任务)。我可以联想到的特殊字符有 transformer 结构中的各种位置编码(positional encoding)，但由于 tiktoken 的文档和注释没有介绍这个部分，我简单了解了一些[NLP中的特殊编码](https://zhuanlan.zhihu.com/p/361169990)，例如 bert 中的 [cls]， [sep]， [unk]， [pad]， [mask]。
### 3. RecursiveCharacterTextSplitter
顾名思义，一种重叠滑窗分句方法。
```Plain
class RecursiveCharacterTextSplitter(TextSplitter):
    """Implementation of splitting text that looks at characters.
    Recursively tries to split by different characters to find one
    that works.
    """
    def __init__(self, separators: Optional[List[str]] = None, **kwargs: Any):
        """Create a new TextSplitter."""
        super().__init__(**kwargs)
        self._separators = separators or ["\n\n", "\n", " ", ""]
    def split_text(self, text: str) -> List[str]:
        """Split incoming text and return chunks."""
        final_chunks = []
        # Get appropriate separator to use
        separator = self._separators[-1]
        for _s in self._separators:
            if _s == "":
                separator = _s
                break
            if _s in text:
                separator = _s
                break
        # Now that we have the separator, split the text
        if separator:
            splits = text.split(separator)
        else:
            splits = list(text)
        # Now go merging things, recursively splitting longer texts.
        _good_splits = []
        for s in splits:
            if self._length_function(s) < self._chunk_size:
                _good_splits.append(s)
            else:
                if _good_splits:
                    merged_text = self._merge_splits(_good_splits, separator)
                    final_chunks.extend(merged_text)
                    _good_splits = []
                other_info = self.split_text(s)
                final_chunks.extend(other_info)
        if _good_splits:
            merged_text = self._merge_splits(_good_splits, separator)
            final_chunks.extend(merged_text)
        return final_chunks
```
### 4. NLTKTextSplitter
本文不介绍 LangChain 内置的 nltk 方法，转而梳理一下 nltk 中实现的多种分词 & 预处理方法。
### 分成句 & 分成词
```Plain
from nltk.tokenize import sent_tokenize, word_tokenize
print("en_doc: ", en_doc)
print("sent_tokenize(en_doc): ", sent_tokenize(en_doc))
print("word_tokenize(en_doc): ", word_tokenize(en_doc))
# en_doc:  ChatGPT is a new artificial intelligence technology driven Natural language processing tool launched by OpenAI, an American artificial intelligence research laboratory. It uses the Transformer neural network architecture and also the GPT-3.5 architecture. It is a model used to process sequence data. It has the ability of language understanding and text generation. In particular, it will train models by connecting a large number of corpora, which contain dialogues in the real world, This enables ChatGPT to have the ability to learn about astronomy and geography, as well as interact based on the context of the chat, enabling communication with almost identical chat scenes to real humans.
# sent_tokenize(en_doc):  ['ChatGPT is a new artificial intelligence technology driven Natural language processing tool launched by OpenAI, an American artificial intelligence research laboratory.', 'It uses the Transformer neural network architecture and also the GPT-3.5 architecture.', 'It is a model used to process sequence data.', 'It has the ability of language understanding and text generation.', 'In particular, it will train models by connecting a large number of corpora, which contain dialogues in the real world, This enables ChatGPT to have the ability to learn about astronomy and geography, as well as interact based on the context of the chat, enabling communication with almost identical chat scenes to real humans.']
# word_tokenize(en_doc):  ['ChatGPT', 'is', 'a', 'new', 'artificial', 'intelligence', 'technology', 'driven', 'Natural', 'language', 'processing', 'tool', 'launched', 'by', 'OpenAI', ',', 'an', 'American', 'artificial', 'intelligence', 'research', 'laboratory', '.', 'It', 'uses', 'the', 'Transformer', 'neural', 'network', 'architecture', 'and', 'also', 'the', 'GPT-3.5', 'architecture', '.', 'It', 'is', 'a', 'model', 'used', 'to', 'process', 'sequence', 'data', '.', 'It', 'has', 'the', 'ability', 'of', 'language', 'understanding', 'and', 'text', 'generation', '.', 'In', 'particular', ',', 'it', 'will', 'train', 'models', 'by', 'connecting', 'a', 'large', 'number', 'of', 'corpora', ',', 'which', 'contain', 'dialogues', 'in', 'the', 'real', 'world', ',', 'This', 'enables', 'ChatGPT', 'to', 'have', 'the', 'ability', 'to', 'learn', 'about', 'astronomy', 'and', 'geography', ',', 'as', 'well', 'as', 'interact', 'based', 'on', 'the', 'context', 'of', 'the', 'chat', ',', 'enabling', 'communication', 'with', 'almost', 'identical', 'chat', 'scenes', 'to', 'real', 'humans', '.']
```
### 基于过滤词的分词
```Plain
import nltk
nltk.download("stopwords")
from nltk.corpus import stopwords
stop_words = set(stopwords.words("english"))
# 下载并打印内置 stopwords
print(stop_words)
'''
{'haven', "shouldn't", "mustn't", 'during', 'own', 'myself',
"should've", 'under', 'himself', 'and', 't', "isn't", 'off',
'on', "wasn't", "wouldn't", 'wasn', 'doesn', 'it', "that'll",
'we', 'mustn', 'the', 'against', 'both', 'some', 'i', 'these',
'll', 'again', 'why', 'so', 'up', 'having', "aren't", 'that',
'not', 'your', 'then', 'how', 'such', "won't", 'has', "mightn't",
'itself', 'being', 'below', "haven't", 'themselves', 'between',
"you've", 'its', 'only', 'ours', 'ma', 'ain', 'ourselves', 'have',
'does', 'there', 'or', "doesn't", 'into', 'theirs', "it's", "you're",
'hers', 'hasn', 're', 'once', 'out', 've', 'been', 'as', 'more',
'mightn', 'isn', "you'll", 'because', 'do', 'whom', 'wouldn', 'weren',
'him', 'this', "needn't", 'am', 'most', "don't", 'shouldn', 'me', 'to',
'their', 'you', 'after', 'same', 'just', 'nor', 'any', "shan't", 'by',
"couldn't", 'had', 'now', "you'd", 'through', "didn't", 'than', 'aren',
'his', 'what', 'them', 'will', 'shan', 'is', 'until', 'o', 'didn', 'an',
'who', 'they', 'no', 'at', 'can', 'those', 'won', 'yourselves', 'my',
"she's", 'are', 'too', 'don', 'other', 'few', 'hadn', 'when', 'doing',
'couldn', 'further', 'her', 'a', 'which', 'y', 'in', 'but', 'all',
'above', 'she', 'was', 'very', 'over', 'while', 'here', 'from', 's',
"hasn't", 'with', "weren't", 'needn', 'about', 'herself', 'our',
"hadn't", 'did', 'if', 'he', 'where', 'm', 'for', 'should', 'before',
'yourself', 'down', 'yours', 'each', 'of', 'd', 'be', 'were'}
'''
```
### 词干提取 & 时态/语态/词性还原
为了应对部分语言中的词性和时态变化，nltk.stem 提供了一个词干提取接口，并内置了[多种词干提取算法](https://link.zhihu.com/?target=https%3A//github.com/nltk/nltk/tree/develop/nltk/stem)。其中还有词性还原算法 [WordNetLemmatizer](https://link.zhihu.com/?target=https%3A//github.com/nltk/nltk/blob/582e6e35f0e6c984b44ec49dcb8846d9c011d0a8/nltk/stem/wordnet.py)
本文对其中的三种词干提取算法进行测试，分别是：“PorterStemmer“，”LancasterStemmer” 和“SnowballStemmer”算法。
```Plain
import nltk
nltk.download('wordnet')
from nltk.stem import WordNetLemmatizer
from nltk.stem import PorterStemmer, LancasterStemmer, SnowballStemmer, WordNetLemmatizer
from nltk.tokenize import word_tokenize
import pandas as pd
stemmer_p = PorterStemmer()
stemmer_l = LancasterStemmer()
stemmer_s = SnowballStemmer("english")
words = word_tokenize(en_doc)
Lemmatizer = WordNetLemmatizer()
lemmatizer_word = [Lemmatizer.lemmatize(word) for word in words]
p_word = [stemmer_p.stem(word) for word in words]
l_word = [stemmer_l.stem(word) for word in words]
s_word = [stemmer_s.stem(word) for word in words]
data = {"raw word": [], "PorterStemmer_result": [], "LancasterStemmer_result": [], "SnowballStemmer_result": [], "Lemmatizer_word_result": []}
for i in range(15):
    if words[i] != p_word[i] or words[i] != l_word[i] or words[i] != s_word[i]:
        data["raw_word"].append(words[i])
        data["Lemmatizer_word_result"].append(lemmatizer_word[i])
        data["PorterStemmer_result"].append(p_word[i])
        data["LancasterStemmer_result"].append(l_word[i])
        data["SnowballStemmer_result"].append(s_word[i])
table = pd.DataFrame(data, columns=["raw_word", "PorterStemmer_result", "LancasterStemmer_result", "SnowballStemmer_result", "Lemmatizer_word_result"])
print(table.to_string())
'''
       raw_word PorterStemmer_result LancasterStemmer_result SnowballStemmer_result Lemmatizer_word_result
0       ChatGPT              chatgpt                 chatgpt                chatgpt                ChatGPT
1    artificial             artifici                     art               artifici             artificial
2  intelligence             intellig                intellig               intellig           intelligence
3    technology            technolog               technolog              technolog             technology
4        driven               driven                    driv                 driven                 driven
5       Natural                natur                     nat                  natur                Natural
6      language              languag                   langu                languag               language
7    processing              process                 process                process             processing
8      launched               launch                  launch                 launch               launched
9        OpenAI               openai                   opena                 openai                 OpenAI
'''
```
可以看到，三种词干提取方法效果很一般： PorterStemmer 和 SnowballStemmer 无法识别过去被动语态单词 “driven”；LancasterStemmer 方法把 “artificial”和 “Natural”裁剪得快要只剩下前缀了；WordNetLemmatizer 方法干脆没有做任何的修改，我们再做一轮补充实验：
```Plain
en_doc = "His friends weren't listening to what he said."
'''
    raw_word PorterStemmer_result LancasterStemmer_result SnowballStemmer_result Lemmatizer_word_result
0        His                   hi                     his                    his                    His
1    friends               friend                  friend                 friend                 friend
2       were                 were                     wer                   were                   were
3        n't                  n't                     n't                    n't                    n't
4  listening               listen                    list                 listen              listening
5         to                   to                      to                     to                     to
6       what                 what                    what                   what                   what
7         he                   he                      he                     he                     he
'''
```
WordNetLemmatizer 方法只改了个附属后缀。忍无可忍的我翻看了这方法的源码，猜测可能是个启发式算法，不想读辣。下面展示一个该方法的正确打开方式：
```Plain
from nltk.stem import WordNetLemmatizer
lemmatizer = WordNetLemmatizer()
# 默认其为名词
print(lemmatizer.lemmatize("better"))
# 如果需要得到更精确的词元，需要告诉 WordNetLemmatizer 你感兴趣的词性是什么。
print(lemmatizer.lemmatize("better", pos="a"))
print(lemmatizer.lemmatize("good", pos="a"))
print(lemmatizer.lemmatize("goods", pos="a"))
print(lemmatizer.lemmatize("goods", pos="n"))
print(lemmatizer.lemmatize("goodness", pos="n"))
print(lemmatizer.lemmatize("best", pos="a"))
'''
better
good
good
goods
good
goodness
best
'''
```
### 标记词性
```Plain
import nltk
from nltk.tokenize import word_tokenize
nltk.download('averaged_perceptron_tagger')
for line in nltk.pos_tag(word_tokenize((en_doc))[:5]):
    print(line)
'''
('ChatGPT', 'NNP')
('is', 'VBZ')
('a', 'DT')
('new', 'JJ')
('artificial', 'NN')
'''
# 打印文档可以查看词性对应关系,本文中仅展示部分
nltk.download('tagsets')
print(nltk.help.upenn_tagset())
'''
JJ: adjective or numeral, ordinal
    third ill-mannered pre-war regrettable oiled calamitous first separable
    ectoplasmic battery-powered participatory fourth still-to-be-named
    multilingual multi-disciplinary ...
JJR: adjective, comparative
    bleaker braver breezier briefer brighter brisker broader bumper busier
    calmer cheaper choosier cleaner clearer closer colder commoner costlier
    cozier creamier crunchier cuter ...
NN: noun, common, singular or mass
    common-carrier cabbage knuckle-duster Casino afghan shed thermostat
    investment slide humour falloff slick wind hyena override subhumanity
    machinist ...
'''
```
上述代码展示了用一个 pre-trained 词性分类器 [Greedy Averaged Perceptron tagger](https://link.zhihu.com/?target=https%3A//explosion.ai/blog/part-of-speech-pos-tagger-in-python) 对文本中单词进行词性标记的过程和结果。令人感兴趣的是，我们可以使用自己的文本-词性对训练自己的词性分类器，代码如下：
```Plain
from nltk.tag.perceptron import PerceptronTagger
# 初始化Tagger
tagger = PerceptronTagger(load=False)
# 加载训练集数据和标签
tagger.train([[('today', 'NN'), ('is', 'VBZ'), ('good', 'JJ'), ('day', 'NN')],
              [('yes', 'NNS'), ('it', 'PRP'), ('beautiful', 'JJ')]])
tagger.tag(['today', 'is', 'a', 'beautiful', 'day'])
# 训练
pretrain = PerceptronTagger()
data = {"word": [], "category": []}
for word, category in pretrain.tag('The quick brown fox jumps over the lazy dog'.split()):
    data["word"].append(word)
    data["category"].append(category)
table = pd.DataFrame(data, columns=["word", "category"])
print(table)
'''
    word category
0    The       DT
1  quick       JJ
2  brown       NN
3    fox       NN
4  jumps      VBZ
5   over       IN
6    the       DT
7   lazy       JJ
8    dog       NN
'''
```
### 基于词性标记的语法树构建
除此之外，NLTK提供了一个基于词性的正则解析器RegexpParser，可以通过正则表达式匹配特定标记的词块。下面的代码演示了主语-谓语-宾语结构的语法树 和 冠词-形容词-名词构成的短语块。
```Plain
grammer = 'NP:{<DT>*<JJ>*<NN>+}'
cp = nltk.RegexpParser(grammer)
tree = cp.parse(sentence)
tree.draw()
```
### 基于词性标记的命名实体识别 Using Named Entity Recognition (NER)
```Plain
import nltk
nltk.download('maxent_ne_chunker')
nltk.download('words')
sentence = pretrain.tag(en_doc.split())[:10]
entity = nltk.chunk.ne_chunk(sentence)
print(entity)
'''
(S
  (ORGANIZATION ChatGPT/NNP)
  is/VBZ
  a/DT
  new/JJ
  artificial/JJ
  intelligence/NN
  technology/NN
  driven/RB
  Natural/NNP
  language/NN)
'''
```
### 5. SpacyTextSplitter
SpacyTextSplitter 是 LangChain-ChatGLM-6B 中的默认分词器，代码如下：
```Plain
# code is in langchain-ChatGLM\chains\text_load.py
text_splitter = SpacyTextSplitter(pipeline='zh_core_web_sm',chunk_size=1000,chunk_overlap=200)
class SpacyTextSplitter(TextSplitter):
    """Implementation of splitting text that looks at sentences using Spacy."""
    def __init__(
        self, separator: str = "\n\n", pipeline: str = "en_core_web_sm", **kwargs: Any
    ):
        """Initialize the spacy text splitter."""
        super().__init__(**kwargs)
        self._tokenizer = spacy.load(pipeline)
        self._separator = separator
    def split_text(self, text: str) -> List[str]:
        """Split incoming text and return chunks."""
        splits = (str(s) for s in self._tokenizer(text).sents)
        return self._merge_splits(splits, self._separator)
```
可以看到 chunk_size， chunk_overlap，separator 都是熟悉的参数，本文着重关注 pipeline这个参数，以及大致了解一下Spacy。
```Plain
import spacy
nlp = spacy.load("en_core_web_sm")
for line in nlp.pipeline:
    print(line)
'''
('tok2vec', <spacy.pipeline.tok2vec.Tok2Vec object at 0x00000193337478E0>)
('tagger', <spacy.pipeline.tagger.Tagger object at 0x0000019333747DC0>)
('parser', <spacy.pipeline.dep_parser.DependencyParser object at 0x00000193338C8200>)
('attribute_ruler', <spacy.pipeline.attributeruler.AttributeRuler object at 0x00000193339680C0>)
('lemmatizer', <spacy.lang.en.lemmatizer.EnglishLemmatizer object at 0x00000193339433C0>)
('ner', <spacy.pipeline.ner.EntityRecognizer object at 0x00000193338C80B0>)
'''
```
对于一个Spacy数据处理方法对象 “nlp”，我们可以关注以下重点：
- Spacy支持提取丰富的语言特征。
- Spacy 使用的语言模型是预先训练的统计模型，例如"en_core_web_sm"。针对不同语言的预训练模型和训练自己模型的方法详见[Models & Languages · spaCy Usage Documentation](https://link.zhihu.com/?target=https%3A//spacy.io/usage/models)，使用基于 transformer 的更好的预训练模型方法详见[Embeddings, Transformers and Transfer Learning · spaCy Usage Documentation](https://link.zhihu.com/?target=https%3A//spacy.io/usage/embeddings-transformers)
- Pipeline 存储了 nlp 对象对文档执行操作的序列，一个 blank pipeline 等同于一个tokenizer。我们可以在参数中使用 “able”&“disable”关键字来部分执行这些操作，也可以自定义操作，具体方法见[Language Processing Pipelines · spaCy Usage Documentation](https://link.zhihu.com/?target=https%3A//spacy.io/usage/processing-pipelines).
## 二、分词 in LangChain - ChatGLM-6B
有了上文的介绍，我们终于可以回归标题，看一下Langchain-ChatGLM中的分词源码了。很简单，就是一个使用
```Plain
#初始化text_splitter
text_splitter = SpacyTextSplitter(pipeline='zh_core_web_sm',chunk_size=1000,chunk_overlap=200)
```
## 三、总结
从LangChain源码出发，本文涉及到了最简单的 split() 分词法，并重点梳理了 LangChain 调用的 nltk 和 Spacy 库中的自然语言处理和分析功能。
时间精力原因，以下重要内容我将继续学习梳理：
1. 待研究的分词算法
- 中文分词算法。本文主要梳理英文分词方法，原因与笔者手里的项目有关。值得关注的中文分词项目包括但不限于：
    
    [Hanlp](https://link.zhihu.com/?target=https%3A//github.com/hankcs/HanLP) [Stanford 分词](https://link.zhihu.com/?target=https%3A//github.com/stanfordnlp/CoreNLP) [ansj 分词器](https://link.zhihu.com/?target=https%3A//github.com/NLPchina/ansj_seg) [哈工大 LTP](https://link.zhihu.com/?target=https%3A//github.com/HIT-SCIR/ltp) [KCWS分词器](https://link.zhihu.com/?target=https%3A//github.com/koth/kcws) [jieba](https://link.zhihu.com/?target=https%3A//github.com/yanyiwu/cppjieba) [IK](https://link.zhihu.com/?target=https%3A//github.com/wks/ik-analyzer) [清华大学THULAC](https://link.zhihu.com/?target=https%3A//github.com/thunlp/THULAC) [ICTCLAS](https://link.zhihu.com/?target=https%3A//github.com/thunlp/THULAC)
    
- 字节对编码(_BPE_, Byte Pair Encoder)及其衍生算法：WordPiece，ULM和开源子词工具包 SentencePiece
- 基于词典和马尔科夫的分词方法：正向最大匹配思想MM、逆向最大匹配算法RMM和双向最大匹配法(Bi-directction Matching method,BM)
- 基于统计的分词方法：n-gram和其他贝叶斯方法
2. 训练、扩充、合并和裁剪词表
## 四、部分参考文献