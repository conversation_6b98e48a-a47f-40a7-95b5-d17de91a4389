---
Updated: 2024-08-30T16:10
tags:
  - AI->-Tools
URL: https://github.com/j-palindrome/obsidian-notion-sync
Created: 2024-08-30T15:04
---
[![](https://opengraph.githubassets.com/7d51ff94d5ea8da745a6f417586f5d4e769c8f6de5de663a52c3628599895b9e/j-palindrome/obsidian-notion-sync)](https://opengraph.githubassets.com/7d51ff94d5ea8da745a6f417586f5d4e769c8f6de5de663a52c3628599895b9e/j-palindrome/obsidian-notion-sync)
[==Open in github.dev==](https://github.dev/) [==Open in a new github.dev tab==](https://github.dev/) [==Open in codespace==](https://github.com/codespaces/new/j-palindrome/obsidian-notion-sync?resume=1)
[==**1**==](https://github.com/j-palindrome/obsidian-notion-sync/branches) [==Branch==](https://github.com/j-palindrome/obsidian-notion-sync/branches)
[==**1**==](https://github.com/j-palindrome/obsidian-notion-sync/tags) [==Tags==](https://github.com/j-palindrome/obsidian-notion-sync/tags)
==t==
## ==Add file==
## ==Add file==
## ==Folders and files==
==Name==
==Name==
==Last commit message==
==Last commit date==
## ==Latest commit==
[![](https://github.githubassets.com/images/gravatars/gravatar-user-420.png?size=40)](https://github.githubassets.com/images/gravatars/gravatar-user-420.png?size=40)
==Joshua Tazman Reinier==
[==add license==](https://github.com/j-palindrome/obsidian-notion-sync/commit/2e06a941de56e93bff231015bdee26b63cf49bb2)
==Dec 25, 2023==
[==2e06a94==](https://github.com/j-palindrome/obsidian-notion-sync/commit/2e06a941de56e93bff231015bdee26b63cf49bb2) ==· Dec 25, 2023==
## ==History==
[==4 Commits==](https://github.com/j-palindrome/obsidian-notion-sync/commits/master/)
[==src==](https://github.com/j-palindrome/obsidian-notion-sync/tree/master/src)
[==src==](https://github.com/j-palindrome/obsidian-notion-sync/tree/master/src)
[==update readme==](https://github.com/j-palindrome/obsidian-notion-sync/commit/e919e38119d97f98ffcd41a835e5c293a59fe261)
==Dec 25, 2023==
[==.gitignore==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/.gitignore)
[==.gitignore==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/.gitignore)
[==initial==](https://github.com/j-palindrome/obsidian-notion-sync/commit/92b8274e8cad1bed12d883b084fd71a56919a69d)
==Nov 11, 2023==
[==CHANGELOG.md==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/CHANGELOG.md)
[==CHANGELOG.md==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/CHANGELOG.md)
[==initial==](https://github.com/j-palindrome/obsidian-notion-sync/commit/92b8274e8cad1bed12d883b084fd71a56919a69d)
==Nov 11, 2023==
[==LICENSE==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/LICENSE)
[==LICENSE==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/LICENSE)
[==add license==](https://github.com/j-palindrome/obsidian-notion-sync/commit/2e06a941de56e93bff231015bdee26b63cf49bb2)
==Dec 25, 2023==
[==README.md==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/README.md)
[==README.md==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/README.md)
[==update readme==](https://github.com/j-palindrome/obsidian-notion-sync/commit/e919e38119d97f98ffcd41a835e5c293a59fe261)
==Dec 25, 2023==
[==manifest.json==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/manifest.json)
[==manifest.json==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/manifest.json)
[==update readme==](https://github.com/j-palindrome/obsidian-notion-sync/commit/e919e38119d97f98ffcd41a835e5c293a59fe261)
==Dec 25, 2023==
[==notion-sync.code-workspace==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/notion-sync.code-workspace)
[==notion-sync.code-workspace==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/notion-sync.code-workspace)
[==initial==](https://github.com/j-palindrome/obsidian-notion-sync/commit/92b8274e8cad1bed12d883b084fd71a56919a69d)
==Nov 11, 2023==
[==package.json==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/package.json)
[==package.json==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/package.json)
[==initial==](https://github.com/j-palindrome/obsidian-notion-sync/commit/92b8274e8cad1bed12d883b084fd71a56919a69d)
==Nov 11, 2023==
[==tsconfig.json==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/tsconfig.json)
[==tsconfig.json==](https://github.com/j-palindrome/obsidian-notion-sync/blob/master/tsconfig.json)
[==initial==](https://github.com/j-palindrome/obsidian-notion-sync/commit/92b8274e8cad1bed12d883b084fd71a56919a69d)
==Nov 11, 2023==
- [==README==](https://github.com/j-palindrome/obsidian-notion-sync#)
- [==MIT license==](https://github.com/j-palindrome/obsidian-notion-sync#)
# ==Notion Sync==
==Notion Sync allows you to easily two-way sync an Obsidian folder with a Notion database, keeping file properties in sync.==
==Notion is a great database platform, and nothing in Obsidian can fully replace its ease of use. Notion Sync exists to seamlessly link Obsidian with Notion, enabling Notion's searching, filtering, and interface to be used with Obsidian file properties.==
==**NOTE:**== ==This plugin is only for syncing== ==**properties**== ==between Obsidian and Notion. File content will not be changed.==
# ==Requirements==
==This plugin requires the Dataview plugin to function. Thanks Dataview!==
# ==Link a database==
1. ==Create an access token for Notion Sync by visiting== [==My Integrations==](https://www.notion.so/my-integrations) ==and selecting "New Integration." You can name it anything — we suggest "Notion Sync." An integration secret will be generated, which gives permission for Notion Sync to access databases you add.==
2. ==In the next page under "Internal Integration Secret," click "Show," then "Copy."==
3. ==Paste the integration secret into the Notion Sync "Notion Integration Secret" setting.==
4. ==Back in Notion, click on a database's options (the three dots icon in the top-right) and go to "Add connections" under the "Connections" section. Search for "Notion Sync" (or whatever you named the integration) and to connect it to the database.==
5. ==In Obsidian, reopen the Settings window and the database's name will appear in Notion Sync settings. Select a path to an empty folder you would like to sync to. Once selected, Notion Sync will create a folder there if it doesn't exist already.==
6. ==In the Obsidian Command Palette, use "Notion Sync: Download all files" to download the database to Obsidian.==
# ==Sync a database==
- ==Use "Notion Sync: Sync" to sync any changes since the last time the command was called. The most recent version (Obsidian or Notion) will be synced. If any files have been modified in== ==**both**== ==Obsidian and Notion since the last sync, you will have the option to choose to upload or download them.==
- ==Use "Notion Sync: Download/Upload all files" to perform a clean upload/download of every file in the databases you've added.==
- ==Use "Notion Sync: Download/Upload this file" to sync a single file.==
# ==Internet Requests==
==The sync uses the following API routes in Notion:==
- ==`GET`====,== ==`POST`====, and== ==`PATCH`== ==to== [==https://api.notion.com/v1/pages==](https://developers.notion.com/reference/post-page) ==to get, set, and create pages.==
- ==`POST`== ==to== [==https://api.notion.com/v1/databases/{database_id}/query==](https://developers.notion.com/reference/post-database-query) ==to search available pages.==
- ==`GET`== ==to== [==https://api.notion.com/v1/users/{user_id}==](https://developers.notion.com/reference/get-user) ==to get names of users, when the "User" property exists in pages.==
# ==Acknowledgements==
==This plugin uses the excellent Notion API to sync files, as well as the Dataview plugin to query relevant metadata.==
## ==About==
==Two-way Notion sync for Obsidian.==
### ==Resources==
[==Readme==](https://github.com/j-palindrome/obsidian-notion-sync#readme-ov-file)
### ==License==
[==MIT license==](https://github.com/j-palindrome/obsidian-notion-sync#MIT-1-ov-file)
[==Activity==](https://github.com/j-palindrome/obsidian-notion-sync/activity)
### ==Stars==
[==**6**==](https://github.com/j-palindrome/obsidian-notion-sync/stargazers) [==stars==](https://github.com/j-palindrome/obsidian-notion-sync/stargazers)
### ==Watchers==
[==**3**==](https://github.com/j-palindrome/obsidian-notion-sync/watchers) [==watching==](https://github.com/j-palindrome/obsidian-notion-sync/watchers)
### ==Forks==
[==**0**==](https://github.com/j-palindrome/obsidian-notion-sync/forks) [==forks==](https://github.com/j-palindrome/obsidian-notion-sync/forks)
[==Report repository==](https://github.com/contact/report-content?content_url=https%3A%2F%2Fgithub.com%2Fj-palindrome%2Fobsidian-notion-sync&report=j-palindrome+%28user%29)
## [==Releases 1==](https://github.com/j-palindrome/obsidian-notion-sync/releases)
[==1.0.0 Latest==](https://github.com/j-palindrome/obsidian-notion-sync/releases/tag/1.0.0)
[==Dec 25, 2023==](https://github.com/j-palindrome/obsidian-notion-sync/releases/tag/1.0.0)
## [==Packages==](https://github.com/users/j-palindrome/packages?repo_name=obsidian-notion-sync)
==No packages published==
## ==Languages==
- [==TypeScript 95.1%==](https://github.com/j-palindrome/obsidian-notion-sync/search?l=typescript)
- [==JavaScript 4.7%==](https://github.com/j-palindrome/obsidian-notion-sync/search?l=javascript)
- [==CSS 0.2%==](https://github.com/j-palindrome/obsidian-notion-sync/search?l=css)