---
Updated: 2024-07-23T09:55
tags:
  - AI->-Agent
  - AI->-<PERSON>lit
  - AI->-Programming
  - AI->-VectorDB
Created: 2024-07-23T09:54
---
[![](https://miro.medium.com/v2/resize:fit:1200/1*2ePkbvke5ObCuJu-g2pzsg.png)](https://miro.medium.com/v2/resize:fit:1200/1*2ePkbvke5ObCuJu-g2pzsg.png)
[![](https://miro.medium.com/v2/resize:fill:55:55/1*mGw0B5wtYwJDrrVhxaL2NA.jpeg)](https://miro.medium.com/v2/resize:fill:55:55/1*mGw0B5wtYwJDrrVhxaL2NA.jpeg)
[==Karthik Rajan==](https://medium.com/@karthik.codex?source=post_page-----61ad3759f06f--------------------------------)
==11 min read==
==Jul 15, 2024==
[![](https://miro.medium.com/v2/resize:fit:875/1*2ePkbvke5ObCuJu-g2pzsg.png)](https://miro.medium.com/v2/resize:fit:875/1*2ePkbvke5ObCuJu-g2pzsg.png)
==Graphical abstract of the integration and key components==
==Retrieval-augmented generation (RAG) is a powerful tool that equips large language models (LLMs) with the ability to access real-world data for more informed responses. This is achieved by integrating the models with a vector database for real-time learning and adaptation. This feature makes RAG a preferred choice for applications such as chatbots and virtual assistants where the demand for accurate and sensible responses in real-time is high. An advanced variant of this, known as Graph Retrieval-Augmented Generation (GraphRAG), merges the benefits of graph-based knowledge retrieval with LLMs, further enhancing the capabilities in the field of natural language processing. Unlike traditional RAG methods that rely on vector similarity searches, GraphRAG constructs a structured knowledge graph from raw text, capturing entities, relationships, and key claims. This can enhance LLMs’ ability to understand and synthesize complex datasets and their relationships, yielding more accurate and contextually grounded responses.==
[![](https://miro.medium.com/v2/resize:fit:875/1*zhdPp80vsgQcl6c0Pn_AMA.png)](https://miro.medium.com/v2/resize:fit:875/1*zhdPp80vsgQcl6c0Pn_AMA.png)
==Extracted from a paper by Markus Beuhler from MIT (link== [==here==](https://arxiv.org/pdf/2403.11996)==)==
==AutoGen is a tool by Microsoft that streamlines the development of intricate applications based on multi-agent LLMs, by automating and optimizing workflows that were once complicated and required significant manual effort. Picture AutoGen as a platform where you can interact with multiple GPTs instead of just one. Each of these GPTs acts as an individual “agent”, playing a unique part in a comprehensive operation. Combining GraphRAG’s retrieval strengths with AutoGen AI agents’ conversational and task-oriented functionalities results in robust AI assistants capable of efficiently handling detailed queries, generating and executing codes, creating multi-page scientific reports, and conducting data analysis. Furthermore, offline local LLMs, such as those from Ollama or LM Studio, ensure cost-effective and secure data processing. Local LLMs eliminate the high costs and privacy risks associated with online LLMs, keeping sensitive data within the organization and reducing operational expenses.==
==This article will guide you on constructing a multi-agent AI application with GraphRAG retrieval system, which operates entirely on your local machine and is available at no charge. Here are the key components of this application:==
1. ==GraphRAG’s knowledge search methods are integrated with an AutoGen agent via function calling.==
2. ==GraphRAG (local & global search) is configured to support local models from Ollama for inference and embedding.==
3. ==AutoGen was extended to support function calling with non-OpenAI LLMs from Ollama via the Lite-LLM proxy server.==
4. ==Chainlit UI to handle continuous conversations, multi-threading, and user input settings.==
==Given my background in material science and computational modeling, I naturally wanted to test this application by constructing knowledge graphs from documentations of ABAQUS, an FEA engineering software, and some technical data sheets of carbon fibers and polymers. The overall accuracy of using the local LLMs is questionable, considering the complexity of this dataset. Future articles will explore learnings from benchmark studies of using different models for embedding and inference. Nevertheless, I am eager to build more complex knowledge graphs from scientific journals and data in this field, test advanced engineering code generation tasks, and utilize a conversational assistant to brainstorm scientific topics within my expertise. The application looks like this.==
[![](https://miro.medium.com/v2/resize:fit:875/1*Emn2loXpcEHw67-mYtASzg.jpeg)](https://miro.medium.com/v2/resize:fit:875/1*Emn2loXpcEHw67-mYtASzg.jpeg)
==Main application UI with example queries. The last two have the same query, but the first is a global search, while the second is a local one.==
[![](https://miro.medium.com/v2/resize:fit:529/1*0Wp0YSLDQsp05W_lycl8vQ.png)](https://miro.medium.com/v2/resize:fit:529/1*0Wp0YSLDQsp05W_lycl8vQ.png)
==Widget settings to switch between local and global search, set community levels, and generation length.==
==The development was done in a Linux environment using the Windows Subsystem for Linux (WSL) and Visual Studio Code on a Windows 11 PC with an i9 13th Gen processor, 64 GB RAM, and 24 GB Nvidia RTX 4090. For the best experience in developing and testing this app, it is recommended to use a Linux distribution or WSL. I have not tested this on a native Windows environment. For guidelines on installing WSL and setting up Python and Conda environments, please refer to this article (==[==here==](https://robkerr.ai/fine-tuning-llms-using-a-local-gpu-on-windows/)==). Additional references and relevant information are provided at the end of this article.==
==Here is the== [==link==](https://github.com/karthik-codex/autogen_graphRAG) ==to the source code repository. Now, let’s get started!!==
# ==Install model dependencies & clone repository.==
## ==Install language models from Ollama for inference and embedding==
==ollama pull mistral==
==ollama pull nomic-embed-text==
==ollama pull llama3==
==ollama serve==
## ==**Create a conda environment and install these dependencies**==
==conda create -n RAG_agents python=3.12====  
  
====conda activate RAG_agents==
==pip install 'litellm[proxy]'==
==pip install ollama==
==pip install pyautogen "pyautogen[retrievechat]"==
==pip install graphrag==
==pip install tiktoken==
==pip install chainlit==
==git clone https://github.com/karthik-codex/autogen_graphRAG.git==
==pip install marker-pdf==
==conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia==
==You will find the following files in my GitHub repository.==
1. ==`/requirements.txt`====—== ==_Contains a list of all the above packages_==
2. ==`/utils/settings.yaml`====—== ==_Contains the LLM config for using Mistral 7B and Nomic-Text-Embedding from Ollama for GraphRAG offline embedding and indexing._== ==_You will use this file to replace the one created when you initialize GraphRAG in your working directory for the first time._==
3. ==`/utils/chainlit_agents.py`====—== ==_Contains class definitions that include AutoGen’s assistant and user proxy agents. This allows multiple agents to be tracked and their messages displayed in the UI. (Shout out to the Chainlit team for building the_== [==_template_==](https://github.com/Chainlit/cookbook/blob/main/pyautogen)==_)._==
4. ==`/utils/embedding.py`====—====_Contains the modified embedding functions for GraphRAG embedding for local search query using Ollama. You will use this file to replace the one inside GraphRAG package (more info below)_==
5. ==`utils/openai_embeddings_llm.py`====—C_ontains the modified embedding functions for GraphRAG indexing and embedding using Ollama. You will use this file to replace the one inside GraphRAG package (more info below)._==
6. ==`/appUI.py`====—====_Contains the main asynchronous functions to set up agents, define GraphRAG search functions, track and handle messages, and display them inside Chainlit UI._==
7. ==`/utils/pdf_to_markdown.py`== ==—== ==_Bonus file containing functions to convert PDF files to markdown files for GraphRAG ingestion._==
# ==Create a GraphRAG knowledge base.==
## ==**Initialize GraphRAG in the root folder of the repository**==
==mkdir -p ./input==
==python -m graphrag.index --init --root .==
==mv ./utils/settings.yaml ./==
## ==**Configure GraphRAG settings to support local models from Ollama**==
==Below is a snippet from== ==`settings.yaml`== ==illustrating the configuration of LLMs for creating indexes and embeddings. GraphRAG requires a 32k context length for indexing, making Mistral the chosen model. For embeddings, Nomic-embed-text is selected, although you can experiment with other embeddings from Ollama. No need to set== ==`${GRAPHRAG_API_KEY}`====, as access is not required to these local models’ endpoints.==
==encoding_model: cl100k_base====  
  
====skip_workflows: []====  
  
====llm:====  
  
====api_key: ${GRAPHRAG_API_KEY}====  
  
====type: openai_chat====  
  
====model: mistral====  
  
====model_supports_json: true====  
  
====api_base: http://localhost:11434/v1====  
  
====.====  
  
====.====  
  
====.====  
  
====embeddings:====  
  
====async_mode: threaded====  
  
====llm:====  
  
====api_key: ${GRAPHRAG_API_KEY}====  
  
====type: openai_embedding====  
  
====model: nomic_embed_text====  
  
====api_base: http://localhost:11434/api====  
  
====.====  
  
====.====  
  
====.====  
  
====input:====  
  
====type: file====  
  
====file_type: text====  
  
====base_dir: "input"====  
  
====file_encoding: utf-8====  
  
====file_pattern: ".*\\.md$"==
==You can specify the folder containing the input files in the “input” folder in the root directory. Both text and markdown files can be used. You can use the== ==`/utils/pdf_to_markdown.py`== ==to convert your PDFs to markdown files that are then placed inside the “input” folder. Handling multiple file formats has not been figured out, but it is a solvable issue.==
==Before running GraphRAG to index, create embeddings, and perform local queries, you must modify the Python files== ==`openai_embeddings_llm.py`== ==and== ==`embedding.py`====located within the GraphRAG package. Without this modification, GraphRAG will throw an error when creating embeddings, as it won't recognize "nomic-embed-text" as a valid embedding model from Ollama. In my setup, these files are located at== ==`/home/<USER>/miniconda3/envs/RAG_agents/lib/python3.12/site-packages/graphrag/llm/openai/openai_embeddings_llm.py`====and== ==`/home/<USER>/miniconda3/envs/RAG_agents/lib/python3.12/site-packages/graphrag/query/llm/oai/embedding.py`==
==You can locate these files using the command== ==`sudo find / -name openai_embeddings_llm.py`====.==
## ==Create embeddings and knowledge graphs.==
==Lastly, we create the embeddings and test the knowledge graph using the global or local search method. After completing the embedding process, you can find the output artifacts (.parquet files) and reports (.json and .logs) in the “output” folder of your GraphRAG working directory, which is the root folder in this instance.==
==python -m graphrag.index --root .==
==python -m graphrag.query --root . --method global "===="==
## ==Start the Lite-LLM server and run app from terminal==
==Below is the command to initialize the server before running the app. I chose Llama3:8b to test this app. You can use larger models if your hardware permits. More information on Lite-LLM can be found at this== [==link==](https://microsoft.github.io/autogen-for-net/articles/Function-call-with-ollama-and-litellm.html)==. Now you are ready to run the application from another terminal. Make sure you are in the right conda environment.==
==litellm --model ollama_chat/llama3==
==chainlit run appUI.py==
# ==Breakdown: Core components of appUI.py==
## ==Import python libraries==
==import autogen====  
  
====from rich import print====  
  
====import chainlit as cl====  
  
====from typing_extensions import Annotated====  
  
====from chainlit.input_widget import (====  
  
====Select, Slider, Switch)====  
  
====from autogen import AssistantAgent, UserProxyAgent====  
  
====from utils.chainlit_agents import ChainlitUserProxyAgent, ChainlitAssistantAgent====  
  
====from graphrag.query.cli import run_global_search, run_local_search==
==You will notice two classes being imported from== ==_chainlit_agents_====. These wrapper classes for AutoGen agents enable Chainlit to track their conversations and handle termination or other user inputs. You can read more about this== [==here==](https://medium.com/@antoineross/autogen-web-application-using-chainlit-8c5ebf5a4e75)==.==
## ==Configure AutoGen agents==
==The AutoGen agents utilize models from Ollama via the Lite-LLM proxy server. This is necessary because AutoGen does not support function calling through non-OpenAI inference models. The proxy server enables using Ollama models for function calling and code execution.==
==llm_config_autogen = {====  
  
===="seed": 40,====  
  
===="temperature": 0,====  
  
===="config_list": [{"model": "litellm",====  
  
===="base_url": "http://0.0.0.0:4000/",====  
  
===='api_key': 'ollama'},====  
  
====],====  
  
===="timeout": 60000,====  
  
====}==
## ==Instantiate agents and input user settings at the start of the chat==
==I created three Chainlit widgets (switch, select, and slider) as user settings to select the GraphRAG search type, community level, and content generation type. When turned ON, the switch widget uses the GraphRAG local search method for querying. The select options for content generation include “prioritized list,” “single paragraph,” “multiple paragraphs,” and “multiple-page report.” The slider widget selects the community generation level with options 0, 1, and 2. You can read more about the GraphRAG communities== [==here==](https://mlnotes.substack.com/p/graphrag-combining-knowledge-graphs)==.==
==@cl.on_chat_start====  
  
====async def on_chat_start():====  
  
====try:====  
  
====settings = await cl.ChatSettings(====  
  
====[====  
  
====Switch(id="Search_type", label="(GraphRAG) Local Search", initial=True),====  
  
====Select(====  
  
====id="Gen_type",====  
  
====label="(GraphRAG) Content Type",====  
  
====values=["prioritized list", "single paragraph", "multiple paragraphs", "multiple-page report"],====  
  
====initial_index=1,====  
  
====),====  
  
====Slider(====  
  
====id="Community",====  
  
====label="(GraphRAG) Community Level",====  
  
====initial=0,====  
  
====min=0,====  
  
====max=2,====  
  
====step=1,====  
  
====),==
```plain
        \]  
    ).send()
response\_type = settings\["Gen\_type"\]  
community = settings\["Community"\]  
local\_search = settings\["Search\_type"\]
    cl.user\_session.set("Gen\_type", response\_type)  
cl.user\_session.set("Community", community)  
cl.user\_session.set("Search\_type", local\_search)
retriever   = AssistantAgent(  
   name="Retriever",   
   llm\_config=llm\_config\_autogen,   
   system\_message="""Only execute the function query\_graphRAG to look for context.   
                Output 'TERMINATE' when an answer has been provided.""",  
   max\_consecutive\_auto\_reply=1,  
   human\_input\_mode="NEVER",   
   description="Retriever Agent"  
)
user\_proxy = ChainlitUserProxyAgent(  
    name="User\_Proxy",  
    human\_input\_mode="ALWAYS",  
    llm\_config=llm\_config\_autogen,  
    is\_termination\_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),  
    code\_execution\_config=False,  
    system\_message='''A human admin. Interact with the retriever to provide any context''',  
    description="User Proxy Agent"  
)
    print("Set agents.")
cl.user\_session.set("Query Agent", user\_proxy)  
cl.user\_session.set("Retriever", retriever)
msg = cl.Message(content=f"""Hello! What task would you like to get done today?        
                 """,   
                 author="User\_Proxy")  
await msg.send()
print("Message sent.")
  except Exception as e:  
print("Error: ", e)  
pass
```
==I chose not to use the Chainlit wrapper class for the retriever assistant agent. This allowed me to disable tracking of the retriever’s output and directly capture the response from the GraphRAG function. The reason is that when the response passes through the retriever, the text loses its formatting, including spaces and paragraph indents. This issue was especially noticeable when generating multi-page reports with main and sub-headings. I could preserve the original formatting by bypassing the Chainlit wrapper and directly retrieving the output from the GraphRAG function. You will see how I achieved this below.==
## ==Update changes in input settings==
==This function detects any changes made to the select, switch, and slider widgets from settings so it can reflect those changes in the subsequent queries.==
==@cl.on_settings_update====  
  
====async def setup_agent(settings):====  
  
====response_type = settings["Gen_type"]====  
  
====community = settings["Community"]====  
  
====local_search = settings["Search_type"]====  
  
====cl.user_session.set("Gen_type", response_type)====  
  
====cl.user_session.set("Community", community)====  
  
====cl.user_session.set("Search_type", local_search)====  
  
====print("on_settings_update", settings)==
## ==Update UI with incoming messages from agents and the user.==
==This is the core part of the application that creates a group chat with two agents, defines a function “state_transition” to manage the conversation sequence, and the asynchronous RAG query function.==
==You will notice== ==`INPUT_DIR ,ROOT_DIR,`== ==`RESPONSE_TYPE,`== ==`COMMUNTIY`== ==parameters that are passed into the local and global search GraphRAG query functions based on the bool parameter====`LOCAL_SEARCH`====. The====`ROOT_DIR,`== ==is set to== ==`’.’`== ==— pay attention to this in case you initialized GraphRAG in a different directory.==
==The asynchronous function “query_graphRAG” calls the GraphRAG global or local search method. You will notice the line== ==`await cl.Message(content=result.response).send()`== ==inside the== ==`async def query_graphRAG`== ==function that directly retrieves the output from the RAG query and that preserves the text formatting of the retrieved content.==
==@cl.on_message====  
  
====async def run_conversation(message: cl.Message):====  
  
====print("Running conversation")====  
  
====CONTEXT = message.content==
```plain
MAX\_ITER = 10  
INPUT\_DIR = None  
ROOT\_DIR = '.'  
RESPONSE\_TYPE = cl.user\_session.get("Gen\_type")  
COMMUNITY = cl.user\_session.get("Community")  
LOCAL\_SEARCH = cl.user\_session.get("Search\_type")
print("Setting groupchat")
retriever   = cl.user\_session.get("Retriever")  
user\_proxy  = cl.user\_session.get("Query Agent")
def state\_transition(last\_speaker, groupchat):  
    messages = groupchat.messages  
    if last\_speaker is user\_proxy:  
        return retriever  
    if last\_speaker is retriever:  
        if messages\[-1\]\["content"\].lower() not in \['math\_expert','physics\_expert'\]:  
            return user\_proxy  
        else:  
            if messages\[-1\]\["content"\].lower() == 'math\_expert':  
                return user\_proxy  
            else:  
                return user\_proxy  
    else:  
        pass  
        return None
async def query\_graphRAG( question: Annotated\[str, 'Query string containing information that you want from RAG search'\] ) -> str:  
    if LOCAL\_SEARCH:  
        result = run\_local\_search(INPUT\_DIR, ROOT\_DIR, COMMUNITY ,RESPONSE\_TYPE, question)  
    else:  
        result = run\_global\_search(INPUT\_DIR, ROOT\_DIR, COMMUNITY ,RESPONSE\_TYPE, question)  
    await cl.Message(content=result).send()  
    return result
for caller in \[retriever\]:  
    d\_retrieve\_content = caller.register\_for\_llm(  
        description="retrieve content for code generation and question answering.", api\_style="function"  
    )(query\_graphRAG)
for agents in \[user\_proxy, retriever\]:  
    agents.register\_for\_execution()(d\_retrieve\_content)
groupchat = autogen.GroupChat(  
    agents=\[user\_proxy, retriever\],  
    messages=\[\],  
    max\_round=MAX\_ITER,  
    speaker\_selection\_method=state\_transition,  
    allow\_repeat\_speaker=True,  
)  
manager = autogen.GroupChatManager(groupchat=groupchat,  
                                   llm\_config=llm\_config\_autogen,   
                                   is\_termination\_msg=lambda x: x.get("content", "") and x.get("content", "").rstrip().endswith("TERMINATE"),  
                                   code\_execution\_config=False,  
                                   )    

if len(groupchat.messages) == 0:   
  await cl.make\_async(user\_proxy.initiate\_chat)( manager, message=CONTEXT, )  
elif len(groupchat.messages) < MAX\_ITER:  
  await cl.make\_async(user\_proxy.send)( manager, message=CONTEXT, )  
elif len(groupchat.messages) == MAX\_ITER:    
  await cl.make\_async(user\_proxy.send)( manager, message="exit", )
```
==For this application, we only need two agents. You can add/modify agents and configure the “state_transition” function to orchestrate speaker selection in conversations for more complex workflows.==
# ==Final Message==
==This is my first venture into AI agents, LLMs, and RAGs, and I dove straight into creating this implementation over the past few weeks, bypassing a lot of basics. While this implementation is imperfect, it is an excellent template for developing more complex applications. It lays a solid foundation for integrating multiple functions and coding agents and should enable you to build sophisticated workflows, customize agent interactions, and enhance functionality as needed.==
==**About Me**====: I am a lead modeling engineer at Eaton Research Labs, Southfield, MI, USA. I explore, develop tools, and write about things at the intersection of computational mechanics, material science, engineering, language models, and generative AI.==
==If you want to stay updated, follow me on my socials below.==
==Socials:== [==LinkedIn==](https://www.linkedin.com/in/karthik-rajan-venkatesan/)==,== [==GitHub==](https://github.com/karthik-codex)==,== [==Source Code==](https://github.com/karthik-codex/autogen_graphRAG.git)
# ==Some Useful References==
1. [==https://medium.com/@datadrifters/autogen-litellm-and-open-source-llms-c4c6bc8fa9c5==](https://medium.com/@datadrifters/autogen-litellm-and-open-source-llms-c4c6bc8fa9c5)
2. [==https://medium.com/@rajib76.gcp/memory-default-compute-fallback-5ff4287d47e6==](https://medium.com/@rajib76.gcp/memory-default-compute-fallback-5ff4287d47e6)
3. [==https://medium.com/generative-ai/graphrag-the-rag-approach-by-microsoft-e1abc7eb9fba==](https://medium.com/generative-ai/graphrag-the-rag-approach-by-microsoft-e1abc7eb9fba)
4. [==https://docs.chainlit.io/get-started/overview==](https://docs.chainlit.io/get-started/overview)
5. [==https://medium.com/@antoineross/autogen-web-application-using-chainlit-8c5ebf5a4e75==](https://medium.com/@antoineross/autogen-web-application-using-chainlit-8c5ebf5a4e75)