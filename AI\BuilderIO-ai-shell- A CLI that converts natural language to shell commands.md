---
Updated: 2024-08-10T14:20
tags:
  - AI->-Tools
URL: https://github.com/BuilderIO/ai-shell
Created: 2024-08-10T12:26
---
Memo
```JavaScript
\#Under utility project created one key for aishell
********************************************************************************************************************************************************************
https://api.openai.com/v1
\#make it able to support ollma
OpenAI API 端点 (http://************:8234/v1) <- without /
model : llama3.1:latest
\#create llama3.1 instance as fake gpt-3.5-turbo
ollama show llama3.1:latest --modelfile > /tmp/llama3.1.modelfile
sudo mv /tmp/llama3.1.modelfile /databank/ollama/
cd /databank/ollama/
ollama create gpt-3.5-turbo --file llama3.1.modelfile
```
[![](https://opengraph.githubassets.com/d8fec7edc525b9ee9ad96b2e70fe3c53c031d722c883023ab192525e688ee8cc/BuilderIO/ai-shell)](https://opengraph.githubassets.com/d8fec7edc525b9ee9ad96b2e70fe3c53c031d722c883023ab192525e688ee8cc/BuilderIO/ai-shell)
### ==A CLI that converts natural language to shell commands.==
[![](https://camo.githubusercontent.com/6a45cee4909566e34b90bcb8385d58cd17710ac3651b66acad385d46bd4c3d22/68747470733a2f2f696d672e736869656c64732e696f2f6e706d2f762f406275696c6465722e696f2f61692d7368656c6c)](https://camo.githubusercontent.com/6a45cee4909566e34b90bcb8385d58cd17710ac3651b66acad385d46bd4c3d22/68747470733a2f2f696d672e736869656c64732e696f2f6e706d2f762f406275696c6465722e696f2f61692d7368656c6c)
[![](https://user-images.githubusercontent.com/844291/*********-773845e7-4c9f-44a5-909c-02802b5e49f6.gif)](https://user-images.githubusercontent.com/844291/*********-773845e7-4c9f-44a5-909c-02802b5e49f6.gif)
==Inspired by the== [==GitHub Copilot X CLI==](https://githubnext.com/projects/copilot-cli)==, but open source for everyone.==
# ==AI Shell==
## ==Setup==
==The minimum supported version of Node.js is v14==
1. ==Install== ==_ai shell_====:==
    
    ==npm install -g @builder.io/ai-shell==
    
2. ==Retrieve your API key from== [==OpenAI==](https://platform.openai.com/account/api-keys)
    
    ==Note: If you haven't already, you'll have to create an account and set up billing.==
    
3. ==Set the key so ai-shell can use it:==
    
    ==ai config set OPENAI_KEY=<your token>==
    
    ==This will create a== ==`.ai-shell`== ==file in your home directory.==
    
## ==Usage==
==ai <prompt>==
==For example:==
==ai list all log files==
==Then you will get an output like this, where you can choose to run the suggested command, revise the command via a prompt, or cancel:==
==◇ Your script:  
│  
│ find . -name "*.log"  
│  
◇ Explanation:  
│  
│ 1. Searches for all files with the extension ".log" in the current directory and any subdirectories.  
│  
◆ Run this script?  
│ ● ✅ Yes (Lets go!)  
│ ○ 📝 Revise  
│ ○ ❌ Cancel  
└  
==
### ==Special characters==
==Note that some shells handle certain characters like the== ==`?`== ==or== ==`*`== ==or things that look like file paths specially. If you are getting strange behaviors, you can wrap the prompt in quotes to avoid issues, like below:==
==ai 'what is my ip address'==
### ==Chat mode==
[![](https://user-images.githubusercontent.com/844291/232889699-e13fb3fe-1659-4583-80ee-6c58d1bcbd06.gif)](https://user-images.githubusercontent.com/844291/232889699-e13fb3fe-1659-4583-80ee-6c58d1bcbd06.gif)
==ai chat==
==With this mode, you can engage in a conversation with the AI and receive helpful responses in a natural, conversational manner directly through the CLI:==
==┌ Starting new conversation  
│  
◇ You:  
│ how do I serve a redirect in express  
│  
◇ AI Shell:  
In Express, you can use the `redirect()` method to serve a redirect. The `redirect()` method takes one argument, which is the URL that you want to redirect to.  
Here's an example:  
\`\`\`js  
app.get('/oldurl', (req, res) => {  
res.redirect('/newurl');  
});  
\`\`\`  
==
### ==Silent mode (skip explanations)==
==You can disable and skip the explanation section by using the flag== ==`-s`== ==or== ==`--silent`==
==ai -s list all log files==
==or save the option as a preference using this command:==
==ai config set SILENT_MODE=true==
### ==Custom API endpoint==
==You can custom OpenAI API endpoint to set OPENAI_API_ENDPOINT（default:== ==`https://api.openai.com/v1`====）==
==ai config set OPENAI_API_ENDPOINT=<your proxy endpoint>==
### ==Set Language==
[![](https://user-images.githubusercontent.com/1784873/235330029-0a3b394c-d797-41d6-8717-9a6b487f1ae8.gif)](https://user-images.githubusercontent.com/1784873/235330029-0a3b394c-d797-41d6-8717-9a6b487f1ae8.gif)
==The AI Shell's default language is English, but you can easily switch to your preferred language by using the corresponding language keys, as shown below:==
==Language==
==Key==
==English==
==en==
==Simplified Chinese==
==zh-Hans==
==Traditional Chinese==
==zh-Hant==
==Spanish==
==es==
==Japanese==
==jp==
==Korean==
==ko==
==French==
==fr==
==German==
==de==
==Russian==
==ru==
==Ukrainian==
==uk==
==Vietnamese==
==vi==
==Arabic==
==ar==
==Portuguese==
==pt==
==Turkish==
==tr==
==For instance, if you want to switch to Simplified Chinese, you can do so by setting the LANGUAGE value to zh-Hans:==
==ai config set LANGUAGE=zh-Hans==
==This will set your language to Simplified Chinese.==
### ==Config UI==
==To use a more visual interface to view and set config options you can type:==
==ai config==
==To get an interactive UI like below:==
==◆ Set config:  
│ ○ OpenAI Key  
│ ○ OpenAI API Endpoint  
│ ○ Silent Mode  
│ ● Model (gpt-3.5-turbo)  
│ ○ Language  
│ ○ Cancel  
└  
==
### ==Upgrading==
==Check the installed version with:==
==ai --version==
==If it's not the== [==latest version==](https://github.com/BuilderIO/ai-shell/tags)==, run:==
==npm update -g @builder.io/ai-shell==
==Or just use AI shell:==
==ai update==
## ==Common Issues==
### ==429 error==
==Some users are reporting a 429 from OpenAI. This is due to incorrect billing setup or excessive quota usage. Please follow== [==this guide==](https://help.openai.com/en/articles/6891831-error-code-429-you-exceeded-your-current-quota-please-check-your-plan-and-billing-details) ==to fix it.==
==You can activate billing at== [==this link==](https://platform.openai.com/account/billing/overview)==. Make sure to add a payment method if not under an active grant from OpenAI.==
## ==Motivation==
==I am not a bash wizard, and am dying for access to the copilot CLI, and got impatient.==
## ==Contributing==
==If you want to help fix a bug or implement a feature in== [==Issues==](https://github.com/BuilderIO/ai-shell/issues) ==(tip: look out for the== ==`help wanted`== ==label), checkout the== [==Contribution Guide==](https://github.com/BuilderIO/ai-shell/blob/main/CONTRIBUTING.md) ==to learn how to setup the project.==
## ==Credit==
- ==Thanks to GitHub Copilot for their amazing tools and the idea for this==
- ==Thanks to Hassan and his work on== [==aicommits==](https://github.com/Nutlope/aicommits) ==which inspired the workflow and some parts of the code and flows==
## ==Community==
==Come join the== [==Builder.io discord==](https://discord.gg/EMx6e58xnw) ==and chat with us in the \#ai-shell room==
[![](https://user-images.githubusercontent.com/844291/230786555-a58479e4-75f3-4222-a6eb-74c5af953eac.png)](https://user-images.githubusercontent.com/844291/230786555-a58479e4-75f3-4222-a6eb-74c5af953eac.png)