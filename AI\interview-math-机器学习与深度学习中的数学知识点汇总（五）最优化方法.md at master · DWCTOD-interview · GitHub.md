---
Updated: 2023-10-26T00:15
tags:
  - AI->-Model
Created: 2023-10-22T23:19
---
来源：公众号SIGAI
本文仅用于学习交流分享，如有侵权请联系删除
前面已经说过，最优化方法是机器学习的灵魂，用于确定模型的参数或预测结果。不幸的是，工科专业一般没有学过这门课。不过只要你理解了微积分和线性代数，并不难推导出这些算法。下面列出常用的最优化方法知识点：
**梯度下降法**。最简单的优化算法，但却很有用，尤其在深度学习中。
**随机梯度下降法**。在深度学习中的重要性妇孺皆知。
**最速下降法**。梯度下降法的改进型，是理解梯度提升等算法的基础。
**梯度下降法的改进型**。如AdaGrad，AdaDelta，Adam等，使用深度学习开源库的时候经常会看到这些名字。
**牛顿法**。二阶优化算法的典型代表，只是在深度学习中用的少。在logistic回归等算法的训练中会用到它。
**拟牛顿法**。牛顿法的改进，在条件随机场等模型的训练中会用到L-BFGS等算法。
**坐标下降法**。在logistic回归等模型的训练中会用到它，不难理解。
**凸优化**。最优化中的核心概念之一，如果一个问题被证明为凸优化问题，恭喜你，它基本上可以较好的解决。
**拉格朗日乘数法**。在各种算分的推导中经常使用，如主成分分析，线性判别分析等，如果不熟练掌握它，你将非常艰难。
**KKT条件**。拉格朗日乘数法扩展到带不等式约束后的版本，在SVM的推导中将会使用。
**拉格朗日对偶**。不太好理解的知识点，在SVM的推导中经常用到，不过套公式并不难。
**多目标优化**。一般很少使用，在多目标NAS中会使用它，如帕累托最优等概念。
**变分法**。用于求解泛函的极值，在某些理论推导中会用到它，如通过变分法可以证明在均值和方差一定的情况下，正态分布的熵最大。变分推断中也会用到此概念。如果熟练的掌握了微积分，推导出欧拉-拉格朗日方程并不困难。
参考书目：
最优化方法可以参考下面两本经典教材：
![[68747470733a2f2f6d6d62697a2e717069632e636e2f6d6d62697a5f706e672f3735446b4a6e546841436d6330713135494f6d6a47536252446a4c6673625663714a54474b764e496e446a6f4c41627049797133696266637959586161454a7a636e424564454853586d756a4d70384e45675a596d52512f3634303f77785f666d743d706e672674703d7765627026777866726f6d3d352677785f6c617a793d312677785f636f3d31]]