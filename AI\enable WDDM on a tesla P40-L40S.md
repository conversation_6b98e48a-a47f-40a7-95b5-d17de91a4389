---
DocFlag:
  - Reference
  - Tested
  - ToBeTested
Updated: 2024-04-17T18:34
tags:
  - AI->-Infra
Created: 2024-04-17T18:34
---
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.ef2a25b20778e90482a382854fe332f8.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.ef2a25b20778e90482a382854fe332f8.jpeg)
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/pfp.thumb.gif.6c910f697b8a791c95d6d6c8dfef1da7.gif)](https://pbs-prod.linustechtips.com/monthly_2023_03/pfp.thumb.gif.6c910f697b8a791c95d6d6c8dfef1da7.gif)
==Seems it is unable to switch Tesla P40 WDDM mode from TCC mode by this way, but you may try vGPU or moditfy Windows registry.==
==Here is link for the later：==[==https://blog.csdn.net/qq_45673245/article/details/128555342==](https://blog.csdn.net/qq_45673245/article/details/128555342)
==Before you doing this please back up registry if it is necessary, assume your OS is Windows10 or Windows11.==
==Main steps:==
==1. Open== ==**regedit**== ==as administrator(default).==
==2. Navigate to== ==**Computer**====****\====**HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}**==
==3. Check the subfolder such as "0001", "0002" that== ==**DriverDesc**== ==is== ==**NVIDIA Tesla P40**====, make sure you are operation here.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.a1904719d6357631fb9b24b992f09a6e.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.a1904719d6357631fb9b24b992f09a6e.jpeg)
==4. modify: "====**A**====**dap**====**terType**====", to "====**1**===="====  
  
==    ==modify: "====**FeatureScore**====", from "====**CF**====" to "====**D1**====" (hex)====  
  
==    ==new: new->DWORD(32bit)，"====**GridLicensedFeatures**====" ，to "====**7**===="  (force to enable Grid driver)====  
  
==    ==refresh (F5)====  
  
==    ==delete: "====**AdapterType**===="====  
  
==    ==new: new->DWORD(32bit), "====**EnableMsHybrid**===="，to "====**1**===="==
==5. Look for the folder that== ==**DriverDesc**== ==is iGPU or your display GPU, reference step 3, and navigate to here.==
    ==new: new->DWORD(32bit), “====**EnableMsHybrid**====”，to "====**2**===="==
    ==refresh (F5)==
    ==reboot computer==
    ==(if it is success, you will find the P40 in task manager after reboot. or in CMD type== ==**nvidia-smi**== ==that P40 has switched to WDDM)==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.f9ea2b18b2f669257630c9c019a671b9.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.f9ea2b18b2f669257630c9c019a671b9.jpeg)
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.85cc8588de09e04141b68836052889a8.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.85cc8588de09e04141b68836052889a8.jpeg)
==6. Open Windows settings menu and look for== ==**Graphics settings**====, if you want to P40 for games, add the game.exe to the list and choose High performance GPU. (maybe you need't do this, I just followed the step1~5 and it works well, my OS is Win10 22h2)==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.d9d212931ba1a3bb700ea9229939c43b.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.d9d212931ba1a3bb700ea9229939c43b.jpeg)
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.a1838200112ecbfb18755246df70a984.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.a1838200112ecbfb18755246df70a984.jpeg)
==Hope you are doing well.==
==Read more==
- [==Prev==](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/#comments)
- [==1==](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/#comments)
- [==2==](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/page/2/#comments)
- [==Next==](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/page/2/#comments)
- [==Page 1 of 2==](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/#)
==I need to change these two to 0 ( -dm 0 and gom = 0 ) so I can enable WDDM on a tesla P40, tesla p4 . can it be done?== 
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.153d979e6e32f27cc4b41d865b64bc64.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.153d979e6e32f27cc4b41d865b64bc64.jpeg)
==**Edited November 27, 2023 by RPK_R5**====  
  
====Further information==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Solution**==
==Seems it is unable to switch Tesla P40 WDDM mode from TCC mode by this way, but you may try vGPU or moditfy Windows registry.==
==Here is link for the later：==[==https://blog.csdn.net/qq_45673245/article/details/128555342==](https://blog.csdn.net/qq_45673245/article/details/128555342)
==Before you doing this please back up registry if it is necessary, assume your OS is Windows10 or Windows11.==
==Main steps:==
==1. Open== ==**regedit**== ==as administrator(default).==
==2. Navigate to== ==**Computer**====****\====**HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}**==
==3. Check the subfolder such as "0001", "0002" that== ==**DriverDesc**== ==is== ==**NVIDIA Tesla P40**====, make sure you are operation here.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.a1904719d6357631fb9b24b992f09a6e.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.a1904719d6357631fb9b24b992f09a6e.jpeg)
==4. modify: "====**A**====**dap**====**terType**====", to "====**1**===="====  
  
==    ==modify: "====**FeatureScore**====", from "====**CF**====" to "====**D1**====" (hex)====  
  
==    ==new: new->DWORD(32bit)，"====**GridLicensedFeatures**====" ，to "====**7**===="  (force to enable Grid driver)====  
  
==    ==refresh (F5)====  
  
==    ==delete: "====**AdapterType**===="====  
  
==    ==new: new->DWORD(32bit), "====**EnableMsHybrid**===="，to "====**1**===="==
==5. Look for the folder that== ==**DriverDesc**== ==is iGPU or your display GPU, reference step 3, and navigate to here.==
    ==new: new->DWORD(32bit), “====**EnableMsHybrid**====”，to "====**2**===="==
    ==refresh (F5)==
    ==reboot computer==
    ==(if it is success, you will find the P40 in task manager after reboot. or in CMD type== ==**nvidia-smi**== ==that P40 has switched to WDDM)==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.f9ea2b18b2f669257630c9c019a671b9.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.f9ea2b18b2f669257630c9c019a671b9.jpeg)
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.85cc8588de09e04141b68836052889a8.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.85cc8588de09e04141b68836052889a8.jpeg)
==6. Open Windows settings menu and look for== ==**Graphics settings**====, if you want to P40 for games, add the game.exe to the list and choose High performance GPU. (maybe you need't do this, I just followed the step1~5 and it works well, my OS is Win10 22h2)==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.d9d212931ba1a3bb700ea9229939c43b.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.d9d212931ba1a3bb700ea9229939c43b.jpeg)
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.a1838200112ecbfb18755246df70a984.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.a1838200112ecbfb18755246df70a984.jpeg)
==Hope you are doing well.==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Author**==
==On 3/29/2023 at 5:25 PM,== [==AntiaLake==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1080693) ==said:==
==Seems it is unable to switch Tesla P40 WDDM mode from TCC mode by this way, but you may try vGPU or moditfy Windows registry.==
==Here is link for the later：==[==https://blog.csdn.net/qq_45673245/article/details/128555342==](https://blog.csdn.net/qq_45673245/article/details/128555342)
==Before you doing this please back up registry if it is necessary, assume your OS is Windows10 or Windows11.==
==Main steps:==
==1. Open== ==**regedit**== ==as administrator(default).==
==2. Navigate to== ==**Computer**====****\====**HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}**==
==3. Check the subfolder such as "0001", "0002" that== ==**DriverDesc**== ==is== ==**NVIDIA Tesla P40**====, make sure you are operation here.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.a1904719d6357631fb9b24b992f09a6e.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.a1904719d6357631fb9b24b992f09a6e.jpeg)
==4. modify: "====**A**====**dap**====**terType**====", to "====**1**===="====  
  
==    ==modify: "====**FeatureScore**====", from "====**CF**====" to "====**D1**====" (hex)====  
  
==    ==new: new->DWORD(32bit)，"====**GridLicensedFeatures**====" ，to "====**7**===="  (force to enable Grid driver)====  
  
==    ==refresh (F5)====  
  
==    ==delete: "====**AdapterType**===="====  
  
==    ==new: new->DWORD(32bit), "====**EnableMsHybrid**===="，to "====**1**===="==
==5. Look for the folder that== ==**DriverDesc**== ==is iGPU or your display GPU, reference step 3, and navigate to here.==
    ==new: new->DWORD(32bit), “====**EnableMsHybrid**====”，to "====**2**===="==
    ==refresh (F5)==
    ==reboot computer==
    ==(if it is success, you will find the P40 in task manager after reboot. or in CMD type== ==**nvidia-smi**== ==that P40 has switched to WDDM)==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.f9ea2b18b2f669257630c9c019a671b9.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.f9ea2b18b2f669257630c9c019a671b9.jpeg)
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.85cc8588de09e04141b68836052889a8.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.85cc8588de09e04141b68836052889a8.jpeg)
==6. Open Windows settings menu and look for== ==**Graphics settings**====, if you want to P40 for games, add the game.exe to the list and choose High performance GPU. (maybe you need't do this, I just followed the step1~5 and it works well, my OS is Win10 22h2)==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.d9d212931ba1a3bb700ea9229939c43b.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.d9d212931ba1a3bb700ea9229939c43b.jpeg)
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.a1838200112ecbfb18755246df70a984.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.a1838200112ecbfb18755246df70a984.jpeg)
==Hope you are doing well.==
==Expand==
==Holly shit you answered exactly what I wanted, thank you very much hope you are doing great as well== ==. Does it appear in Nvidia control panel as well? Difference to my setup though is that I use a Quadro k4200 as display out and I have set that as integrated graphics will this make a difference.====  
  
====reference post here:== 
[![](https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/72x72/1f604.png)](https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/72x72/1f604.png)
==And why does the Tesla M40 not require this setup and can get switched from NVidia smi?== 
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
==On 3/29/2023 at 8:59 PM,== [==RPK_R5==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1077592) ==said:==
==Holly shit you answered exactly what I wanted, thank you very much hope you are doing great as well== ==. Does it appear in Nvidia control panel as well? Difference to my setup though is that I use a Quadro k4200 as display out and I have set that as integrated graphics will this make a difference.====  
  
====reference post here:== 
[![](https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/72x72/1f604.png)](https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/72x72/1f604.png)
==And why does the Tesla M40 not require this setup and can get switched from NVidia smi?== 
==Expand==
==All right.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.5666192d16e6965b71b810204edf24b8.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.5666192d16e6965b71b810204edf24b8.jpeg)
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.f03be41761890cbd86391e00b6f16ae5.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.f03be41761890cbd86391e00b6f16ae5.jpeg)
==This is intel HD Graphics 530 + P40's control panel, it is no problem.==
==I've tried Quadro P600 + Tesla P40 on another PC, P600 for display output, just install P600's driver, and P40 also be drived, both appeared in NVIDIA control panel, but P40 was TCC mode and invisible in task manager unless apply the reference solution.== 
==Quadro K4200 has not ended support, so as P40.  I guess it will work well with P40 by theory, the critical is if public driver exists, or it may cause conflict when you install drivers by sequence.==
==The last question, the difference of WDDM switching solution between M40 and P40,  due to NVIDIA's policy and it's history,  I just make a speculation, they want to differentiate the market strictly and do not wish personal users to utilize their older commercial-side productions. M40, P40, RTX 3090, RTX 4090 also have 24GB VRAM, NVIDIA definitely hope you purchase RTX 4090, it's faster, and also profitable for them, rather than an obsoleted computing card of data center. If business users have requirements, they provide vGPU service== [==https://www.nvidia.com/en-us/data-center/virtual-solutions/==](https://www.nvidia.com/en-us/data-center/virtual-solutions/)==, and it is not for free. IT IS ALL FOR MONEY.==
- [==RPK_R5==](https://linustechtips.com/profile/1077592-rpk_r5/)
- ==[== ==1](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/?do=showReactionsComment&comment=15868157&reaction=4 "See who reacted "Informative"")==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Author**==
==On 3/29/2023 at 11:26 PM,== [==AntiaLake==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1080693) ==said:==
==All right.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.5666192d16e6965b71b810204edf24b8.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.5666192d16e6965b71b810204edf24b8.jpeg)
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.f03be41761890cbd86391e00b6f16ae5.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.f03be41761890cbd86391e00b6f16ae5.jpeg)
==This is intel HD Graphics 530 + P40's control panel, it is no problem.==
==I've tried Quadro P600 + Tesla P40 on another PC, P600 for display output, just install P600's driver, and P40 also be drived, both appeared in NVIDIA control panel, but P40 was TCC mode and invisible in task manager unless apply the reference solution.== 
==Quadro K4200 have't end of support, so as P40.  I guess it will work well with P40 by theory, the critical is if public driver exists, or it may cause conflict when you install drivers by sequence.==
==The last question, the difference of WDDM swithing solution between M40 and P40,  due to NVIDIA's policy and it's history,  I just make a speculation, they want to differentiate the market strictly and do not wish personal users utilize their older commercial-side productions. M40, P40, RTX 3090, RTX 4090 also have 24GB VRAM, NVIDIA definitely hope you purchase RTX 4090, it's faster, and also profitable for them, rather than an obsoleted computing card of data center. If business users have requirements, they provide vGPU service== [==https://www.nvidia.com/en-us/data-center/virtual-solutions/==](https://www.nvidia.com/en-us/data-center/virtual-solutions/)==, and it is not for free. IT IS ALL FOR MONEY.==
==Expand==
==Wow amazing, thank you very much, i will replace my M40 to a p40 thanks to you now ^^. How do you know so much.==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
==On 3/29/2023 at 11:41 PM,== [==RPK_R5==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1077592) ==said:==
==Wow amazing, thank you very much, i will replace my M40 to a p40 thanks to you now ^^. How do you know so much.==
==Thanks to the author of the original post and the first man who found this solution. :)==
- [==RPK_R5==](https://linustechtips.com/profile/1077592-rpk_r5/)
- ==[== ==1](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/?do=showReactionsComment&comment=15868274&reaction=1 "See who reacted "Like"")==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Author**==
==On 3/30/2023 at 12:12 AM,== [==AntiaLake==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1080693) ==said:==
==Thanks to the author of the original post and the first man who found this solution. :)==
==Well I cant thank you enough. I will probably make a video for both the M40 and P40 so people can do this without looking blindly. I will reference this forum.==
==Do you know if you can have GTX/RTX card work with QUADRO/TESLA work on the same machine by any chance?==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
==On 3/30/2023 at 12:15 AM,== [==RPK_R5==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1077592) ==said:==
==Well I cant thank you enough. I will probably make a video for both the M40 and P40 so people can do this without looking blindly. I will reference this forum.==
==Do you know if you can have GTX/RTX card work with QUADRO/TESLA work on the same machine by any chance?==
==I have not tried this configuration, but this vlogger was succeed,== [==https://www.bilibili.com/video/BV13W4y1s7so==](https://www.bilibili.com/video/BV13W4y1s7so) ==, GTX 750 + Tesla P40, although tesla P40 was recognised for T4, in GPU-Z the key parameters were right. I guess the main reason is GTX 750's specific driver version 526.86 contain Tesla T4's driver, then WDDM mode could be enabled by modify Windows registry,  you can have a try.==
- [==RPK_R5==](https://linustechtips.com/profile/1077592-rpk_r5/)
- ==[== ==1](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/?do=showReactionsComment&comment=15868534&reaction=4 "See who reacted "Informative"")==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Author**==
==On 3/30/2023 at 2:43 AM,== [==AntiaLake==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1080693) ==said:==
==I have not tried this configuration, but this vlogger was succeed,== [==https://www.bilibili.com/video/BV13W4y1s7so==](https://www.bilibili.com/video/BV13W4y1s7so) ==, GTX 750 + Tesla P40, although tesla P40 was recognised for T4, in GPU-Z the key parameters were right. I guess the main reason is GTX 750's specific driver version 526.86 contain Tesla T4's driver, then WDDM mode could be enabled by modify Windows registry,  you can have a try.==
==IV heard that you can install the tesla drivers first normal instalation process and then the gtx or RTX instalation and it will work== 
==Ref post :==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
==On 3/30/2023 at 5:23 AM,== [==RPK_R5==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1077592) ==said:==
==IV heard that you can install the tesla drivers first normal instalation process and then the gtx or RTX instalation and it will work== 
==Ref post :==
==Expand==
==According to the vlogger's video, I've tried 3060 Ti + P40 in recent hours, both work well, the driver version is 531.41 nsd, the latest.== 
==In order to reduce unexpected failures, please uninstall the older GPU drivers, and make it clean. the video shows that vlogger employ Display Driver Uninstaller (DDU) for work.==
==Main steps:==
==1. Install GTX/RTX driver first, the driver program will extract many files to a folder as you know, then access the folder and look for== ==**{your driver extracting**== ==**folder}\Display.Driver\nv_dispsig.inf**====, if P40 and your GTX/RTX GPU's name both under the item "[Strings]", it proves that the driver compatible for two GPUs.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.dff2a5472a706cb3f97e2d73dcf90fca.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.dff2a5472a706cb3f97e2d73dcf90fca.jpeg)
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.d2eb965b4074ec6f33600df7cd1cbad0.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.d2eb965b4074ec6f33600df7cd1cbad0.jpeg)
==2. If no error reported, you will find them in device manager - display adapters, no warning sign, working properly. But P40 works on TCC mode by default.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.png.cb4232ed8f526ab0df0734ad887ef368.png)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.png.cb4232ed8f526ab0df0734ad887ef368.png)
==3. Modify Windows registry, similar to the previous post.==
==Navigate to== ==**Computer**====****\====**HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}**== ==and search the subfolder which right item== ==**DriverDesc**== ==is== ==**NVIDIA Tesla P40**====, we will modify and add item here.==
    ==modify: "====**A**====**dap**====**terType**====", to "====**1**===="====  
  
====new: new->DWORD(32-bit) Value，"====**GridLicensedFeatures**====" ，to "====**7**===="  (force to enable Grid driver)====  
  
====refresh (F5)====  
  
====new: new->DWORD(32-bit) Value, "====**EnableMsHybrid**===="，to "====**1**===="==
    ==navigate to display GPU subfolder==
    ==new: new->DWORD(32-bit) Value, “====**EnableMsHybrid**====”，to "====**2**===="==
    ==refresh (F5)==
    ==reboot computer==
==(I guess the value== ==**EnableMsHybrid**== ==is related to Windows scheduling priority)==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.2e401caf22b84248a9b3f2956b9b168f.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.2e401caf22b84248a9b3f2956b9b168f.jpeg)
==Open Windows settings menu and look for== ==**Graphics settings**====, if you want to P40 for games, add the game.exe to the list and choose High performance GPU(however 3060 Ti has higher performance than P40)==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.6620771d220bb1c4ed107c33160ee65d.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.6620771d220bb1c4ed107c33160ee65d.jpeg)
- [==RPK_R5==](https://linustechtips.com/profile/1077592-rpk_r5/)
- ==[== ==1](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/?do=showReactionsComment&comment=15869933&reaction=2 "See who reacted "Agree"")==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Author**==
==On 3/30/2023 at 11:04 PM,== [==AntiaLake==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1080693) ==said:==
==According to the vlogger's video, I've tried 3060 Ti + P40 in recent hours, both work well, the driver version is 531.41 nsd, the latest.== 
==In order to reduce unexpected failures, please uninstall the older GPU drivers, and make it clean. the video shows that vlogger employ Display Driver Uninstaller (DDU) for work.==
==Main steps:==
==1. Install GTX/RTX driver first, the driver program will extract many files to a folder as you know, then access the folder and look for== ==**{your driver extracting**== ==**folder}\Display.Driver\nv_dispsig.inf**====, if P40 and your GTX/RTX GPU's name both under the item "[Strings]", it proves that the driver compatible for two GPUs.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.dff2a5472a706cb3f97e2d73dcf90fca.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.dff2a5472a706cb3f97e2d73dcf90fca.jpeg)
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.d2eb965b4074ec6f33600df7cd1cbad0.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.d2eb965b4074ec6f33600df7cd1cbad0.jpeg)
==2. If no error reported, you will find them in device manager - display adapters, no warning sign, working properly. But P40 works on TCC mode by default.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.png.cb4232ed8f526ab0df0734ad887ef368.png)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.png.cb4232ed8f526ab0df0734ad887ef368.png)
==3. Modify Windows registry, similar to the previous post.==
==Navigate to== ==**Computer**====****\====**HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}**== ==and search the subfolder which right item== ==**DriverDesc**== ==is== ==**NVIDIA Tesla P40**====, we will modify and add item here.==
    ==modify: "====**A**====**dap**====**terType**====", to "====**1**===="====  
  
====new: new->DWORD(32-bit) Value，"====**GridLicensedFeatures**====" ，to "====**7**===="  (force to enable Grid driver)====  
  
====refresh (F5)====  
  
====new: new->DWORD(32-bit) Value, "====**EnableMsHybrid**===="，to "====**1**===="==
    ==navigate to display GPU subfolder==
    ==new: new->DWORD(32-bit) Value, “====**EnableMsHybrid**====”，to "====**2**===="==
    ==refresh (F5)==
    ==reboot computer==
==(I guess the value== ==**EnableMsHybrid**== ==is related to Windows scheduling priority)==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.2e401caf22b84248a9b3f2956b9b168f.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.2e401caf22b84248a9b3f2956b9b168f.jpeg)
==Open Windows settings menu and look for== ==**Graphics settings**====, if you want to P40 for games, add the game.exe to the list and choose High performance GPU(however 3060 Ti has higher performance than P40)==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.6620771d220bb1c4ed107c33160ee65d.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.6620771d220bb1c4ed107c33160ee65d.jpeg)
==Expand==
==I know about this but if it's not supported by the same driver you basically have no chance of driving both?== 
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
==On 3/31/2023 at 2:50 AM,== [==RPK_R5==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1077592) ==said:==
==I know about this but if it's not supported by the same driver you basically have no chance of driving both?== 
==I think it is possible, the driver can be forced to install in theory, but it may not be displayed in the control panel.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.417fbd3f330fdcb9dee6404c5dc4b0ac.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.417fbd3f330fdcb9dee6404c5dc4b0ac.jpeg)
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Author**==
==On 3/31/2023 at 3:18 AM,== [==AntiaLake==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1080693) ==said:==
==I think it is possible, the driver can be forced to install in theory, but it may not be displayed in the control panel.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.417fbd3f330fdcb9dee6404c5dc4b0ac.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.jpeg.417fbd3f330fdcb9dee6404c5dc4b0ac.jpeg)
==Expand==
==How do you force it to install? If I install one driver on top of the other it's going to remove the previous one. And you mean it's doesn't show up in Nvidia control panel, does it show up in devices and Nvidia smi?== 
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
==On 3/31/2023 at 3:25 AM,== [==RPK_R5==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1077592) ==said:==
==How do you force it to install? If I install one driver on top of the other it's going to remove the previous one. And you mean it's doesn't show up in Nvidia control panel, does it show up in devices and Nvidia smi?== 
==Disable one GPU and manually install the driver on the another. If I remember correctly, they both show up normally in device manager and nvidia-smi, I'll try again.==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Author**==
==On 3/31/2023 at 4:18 AM,== [==AntiaLake==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1080693) ==said:==
==Disable one GPU and manually install the driver on the another. If I remember correctly, they both show up normally in device manager and nvidia-smi, I'll try again.==
==Ow nice thanks for that as well. I'm sorry for dragging this post for so long, you just know so much and I'm new to this stuff so I'm trying to extract as much as possible.== 
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
==On 3/31/2023 at 6:06 AM,== [==RPK_R5==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1077592) ==said:==
==Ow nice thanks for that as well. I'm sorry for dragging this post for so long, you just know so much and I'm new to this stuff so I'm trying to extract as much as possible.== 
==I've tried different driver which is independent for each card, 472.12 for 3060 Ti, 472.98 for P40,  but unfortunately the P40 can only works on TCC mode, modify registry is invalid and cause disappearing of P40 in nvidia-smi and NVIDIA control panel.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.68f4ce707cd093865466896c120f497f.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.68f4ce707cd093865466896c120f497f.jpeg)
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Author**==
==On 3/31/2023 at 4:50 PM,== [==AntiaLake==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1080693) ==said:==
==I've tried different driver which is independent for each card, 472.12 for 3060 Ti, 472.98 for P40,  but unfortunately the P40 can only works on TCC mode, modify registry is invalid and cause disappearing of P40 in nvidia-smi and NVIDIA control panel.==
[![](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.68f4ce707cd093865466896c120f497f.jpeg)](https://pbs-prod.linustechtips.com/monthly_2023_03/image.thumb.jpeg.68f4ce707cd093865466896c120f497f.jpeg)
==Expand==
==What if you install the P40 drivers first, do the reg trick and then install the other driver, not sure what sequence of installation and modding you followed. Also found out that some studio drivers for GTX cards support Tesla and Quadro cards.==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
==On 3/31/2023 at 7:58 PM,== [==RPK_R5==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1077592) ==said:==
==What if you install the P40 drivers first, do the reg trick and then install the other driver, not sure what sequence of installation and modding you followed. Also found out that some studio drivers for GTX cards support Tesla and Quadro cards.==
==Yeh I tried, it's almost useless, if you install P40's driver first and then do reg hack, set== ==**A**====**dap**====**terType**== ==to 1 and so on, because P40 has no output connector, it became black screen, but I logged to the PC by remote desktop and found P40 had switched to WDDM, and another card was Microsoft basic dispay adapter. If I install that unrecognised GPU's driver by manual operation, and P40 will disappear later.==
==So, the conclusion is only one card can be set to WDDM mode in independent driver version, no matter how you modify the registry. It's driver's limitation and a higher problem.==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Author**==
==On 3/31/2023 at 11:48 PM,== [==AntiaLake==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1080693) ==said:==
==Yeh I tried, it's almost useless, if you install P40's driver first and then do reg hack, set== ==**A**====**dap**====**terType**== ==to 1 and so on, because P40 has no output connector, it became black screen, but I logged to the PC by remote desktop and found P40 had switched to WDDM, and another card was Microsoft basic dispay adapter. If I install that unrecognised GPU's driver by manual operation, and P40 will disappear later.==
==So, the conclusion is only one card can be set to WDDM mode in independent driver version, no matter how you modify the registry. It's driver's limitation and a higher problem.==
==Alright, conclusions have been made, thank you for your input. I won't annoy you any longer thank you very much ^^==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
==On 4/1/2023 at 12:03 AM,== [==RPK_R5==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1077592) ==said:==
==Alright, conclusions have been made, thank you for your input. I won't annoy you any longer thank you very much ^^==
==Not at all.==
==If you want to completely solve this problem, soldering display output connector on P40's PCB is a solution, but it may costs a lot.==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Author**==
==On 4/1/2023 at 4:37 PM,== [==AntiaLake==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1080693) ==said:==
==Not at all.==
==If you want to completely solve this problem, soldering display output connector on P40's PCB is a solution, but it may costs a lot.==
==I've seen people try this before with m40 teslas and it didn't exactly go very well, you need to flash a new vbios as well wich I don't know if it's possible. Aslo I kinda don't want to experiment on my 200 dollar piece of equipment that keeps my computer running. I know the traces are there and I have a soldering station that can do that kind of soldering but I'm not willing to go that far.== 
==**Edited April 1, 2023 by RPK_R5**==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
==On 4/1/2023 at 4:59 PM,== [==RPK_R5==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1077592) ==said:==
==I've seen people try this before with m40 teslas and it didn't exactly go very well, you need to flash a new vbios as well wich I don't know if it's possible. Aslo I kinda don't want to experiment on my 200 dollar piece of equipment that keeps my computer running. I know the traces are there and I have a soldering station that can do that kind of soldering but I'm not willing to go that far.== 
==Yes it's not worth of doing that. I purchase P40 mainly for Stable Diffusion, but actually it is much slower than 3060 Ti except for VRAM capacity, it's lack of fp16 and tensor core support,  and mainstream optimization for 20/30/40 series can reduce VRAM occupation. Therefore, the advantage of its 24GB VRAM is not so obvious.==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Author**==
==On 4/1/2023 at 7:10 PM,== [==AntiaLake==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1080693) ==said:==
==Yes it's not worth of doing that. I purchase P40 mainly for Stable Diffusion, but actually it is much slower than 3060 Ti except for VRAM capacity, it's lack of fp16 and tensor core support,  and mainstream optimization for 20/30/40 series can reduce VRAM occupation. Therefore, the advantage of its 24GB VRAM is not so obvious.==
==That's ok they are old cards, I think using them for gaming is not a bad idea. If you are selling any of them let me know.==
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
==On 4/1/2023 at 7:50 PM,== [==RPK_R5==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1077592) ==said:==
==That's ok they are old cards, I think using them for gaming is not a bad idea. If you are selling any of them let me know.==
==Luckily, it supports dx12.1, higher than dx12.0. Some games require dx12.1 features, running on dx12.0 card may report some errors.== 
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- ==**Author**==
==On 4/1/2023 at 8:31 PM,== [==AntiaLake==](https://linustechtips.com/?app=core&module=members&controller=profile&id=1080693) ==said:==
==Luckily, it supports dx12.1, higher than dx12.0. Some games require dx12.1 features, running on dx12.0 card may report some errors.== 
==Even the M40 supports that.== 
### ==Link to comment==
### ==Share on other sites==
### ==Link to post==
### ==Share on other sites==
- [==Prev==](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/#comments)
- [==1==](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/#comments)
- [==2==](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/page/2/#comments)
- [==Next==](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/page/2/#comments)
- [==Page 1 of 2==](https://linustechtips.com/topic/1496913-can-i-enable-wddm-on-a-tesla-p40/#)
## ==Create an account or sign in to comment==
==You need to be a member in order to leave a comment==
## ==Sign in==
==Already have an account? Sign in here.==
[==Sign In Now==](https://linustechtips.com/login/?ref=aHR0cHM6Ly9saW51c3RlY2h0aXBzLmNvbS90b3BpYy8xNDk2OTEzLWNhbi1pLWVuYWJsZS13ZGRtLW9uLWEtdGVzbGEtcDQwLyNyZXBseUZvcm0=)