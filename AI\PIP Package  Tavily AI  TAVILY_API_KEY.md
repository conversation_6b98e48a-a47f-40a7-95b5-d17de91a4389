---
Updated: 2024-07-17T17:09
tags:
  - AI->-Programming
  - AI->-RAG
  - AI->-Tools
URL: https://docs.tavily.com/docs/gpt-researcher/pip-package
Created: 2024-07-17T17:09
---
==🌟== ==**Exciting News!**== ==Now, you can integrate== ==`gpt-researcher`== ==with your apps seamlessly!==
## ==Steps to Install GPT Researcher 🛠️==
==Follow these easy steps to get started:==
1. ==**Pre-requisite**====: Ensure Python 3.10+ is installed on your machine 💻==
2. ==**Install gpt-researcher**====: Grab the official package from== [==PyPi==](https://pypi.org/project/gpt-researcher/)==.==
    
    ==pip install gpt-researcher==
    
3. ==**Environment Variables:**== ==Create a .env file with your OpenAI API key or simply export it==
    
    ==export OPENAI_API_KEY={Your OpenAI API Key here}==
    
    ==export TAVILY_API_KEY={Your Tavily API Key here}==
    
4. ==**Start using GPT Researcher in your own codebase**==
## ==Example Usage 📝==
```plain
from gpt_researcher import GPTResearcherimport asynciofrom gpt_researcher import GPTResearcherimport asyncioasync def get_report(query: str, report_type: str) -> str:    researcher = GPTResearcher(query, report_type)    research_result = await researcher.conduct_research()    report = await researcher.write_report()    return reportif __name__ == "__main__":    query = "what team may win the NBA finals?"    report_type = "research_report"    report = asyncio.run(get_report(query, report_type))    print(report)
```
## ==Specific Examples 🌐==
### ==Example 1: Research Report 📚==
```plain
query = "Latest developments in renewable energy technologies"report_type = "research_report"
```
### ==Example 2: Resource Report 📋==
```plain
query = "List of top AI conferences in 2023"report_type = "resource_report"
```
### ==Example 3: Outline Report 📝==
```plain
query = "Outline for an article on the impact of AI in education"report_type = "outline_report"
```
## ==Integration with Web Frameworks 🌍==
### ==FastAPI Example==
```plain
from fastapi import FastAPIfrom gpt_researcher import GPTResearcherimport asyncioapp = FastAPI()@app.get("/report/{report_type}")async def get_report(query: str, report_type: str) -> dict:    researcher = GPTResearcher(query, report_type)    research_result = await researcher.conduct_research()    report = await researcher.write_report()    return {"report": report}# Run the server# uvicorn main:app --reload
```
### ==Flask Example==
==**Pre-requisite**====: Install flask with the async extra.==
```plain
pip install 'flask[async]'
from flask import Flask, requestfrom gpt_researcher import GPTResearcherapp = Flask(__name__)@app.route('/report/<report_type>', methods=['GET'])async def get_report(report_type):    query = request.args.get('query')    researcher = GPTResearcher(query, report_type)    research_result = await researcher.conduct_research()    report = await researcher.write_report()    return report# Run the server# flask run
```
==**Run the server**==
```plain
flask run
```
==**Example Request**==
```plain
curl -X GET "http://localhost:5000/report/research_report?query=what team may win the nba finals?"
```
==**Note**====: The above code snippets are just examples. You can customize them as per your requirements.==