---
Updated: 2023-09-29T12:12
tags:
  - AI->-Programming
  - AI->-Voice
Created: 2023-09-23T02:39
---
![[pyannote-audio]]
Pane width
Use a value between 19% and 30%
## Files
notebookpyannotequestionsteststutorialsAMI-diarization-setupadapting_pretrained_pipeline.ipynbadd_your_own_model.ipynbadd_your_own_task.ipynbapplying_a_model.ipynbapplying_a_pipeline.ipynboverlapped_speech_detection.ipynbspeaker_verification.ipynbtraining_a_model.ipynbtraining_with_cli.mdvoice_activity_detection.ipynb.gitattributes.pre-commit-config.yamlCHANGELOG.mdFAQ.mdLICENSEMANIFEST.inREADME.mdenvironment.yamlrequirements.txtsetup.cfgsetup.pyversion.txt
[Documentation](https://docs.github.com/repositories/working-with-files/using-files/navigating-code-on-github) • [Share feedback](https://github.com/orgs/community/discussions/54546)
## Breadcrumbs
1. [pyannote-audio](https://github.com/pyannote/pyannote-audio/tree/develop)
2. /[tutorials](https://github.com/pyannote/pyannote-audio/tree/develop/tutorials)
/
```Plain
import torch
from pyannote.audio.pipelines.speaker_verification import PretrainedSpeakerEmbedding
model = PretrainedSpeakerEmbedding(
    "speechbrain/spkrec-ecapa-voxceleb",
    device=torch.device("cuda"))
from pyannote.audio import Audio
from pyannote.core import Segment
audio = Audio(sample_rate=16000, mono="downmix")
# extract embedding for a speaker speaking between t=3s and t=6s
speaker1 = Segment(3., 6.)
waveform1, sample_rate = audio.crop("audio.wav", speaker1)
embedding1 = model(waveform1[None])
# extract embedding for a speaker speaking between t=7s and t=12s
speaker2 = Segment(7., 12.)
waveform2, sample_rate = audio.crop("audio.wav", speaker2)
embedding2 = model(waveform2[None])
# compare embeddings using "cosine" distance
from scipy.spatial.distance import cdist
distance = cdist(embedding1, embedding2, metric="cosine")
```