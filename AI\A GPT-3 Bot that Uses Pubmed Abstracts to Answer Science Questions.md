---
Updated: 2023-10-14T16:54
tags:
  - AI->-<PERSON><PERSON><PERSON><PERSON>
  - <PERSON>->-Programming
Created: 2023-10-14T16:54
---
[![](https://miro.medium.com/v2/resize:fit:720/1*i89Kl8CfI9piBYxjBhBr5g.png)](https://miro.medium.com/v2/resize:fit:720/1*i89Kl8CfI9piBYxjBhBr5g.png)
## ==Here is a web app project that explores Pubmed’s API, asynchronous calls with JavaScript’s fetch(), filtering GPT-3’s outputs with token probabilities, and chatbot GUI design====  
  
====这是一个 Web 应用程序项目，探索了 Pubmed 的 API、使用 JavaScript 的 fetch（） 进行异步调用、使用令牌概率过滤 GPT-3 的输出以及聊天机器人 GUI 设计==
==[==
[![](https://miro.medium.com/v2/resize:fill:44:44/1*_D6QHa8RBbslpMmy3jOGtw.jpeg)](https://miro.medium.com/v2/resize:fill:44:44/1*_D6QHa8RBbslpMmy3jOGtw.jpeg)
==](https://pub.towardsai.net/@lucianosphere?source=post_page-----3f3b84f87e28--------------------------------)[==
[![](https://miro.medium.com/v2/resize:fill:24:24/1*JyIThO-cLjlChQLb6kSlVQ.png)](https://miro.medium.com/v2/resize:fill:24:24/1*JyIThO-cLjlChQLb6kSlVQ.png)
==](https://medium.com/towards-artificial-intelligence?source=post_page-----3f3b84f87e28--------------------------------)==
==**By feeding GPT-3 with Pubmed abstracts, this GPT-3-powered chatbot can use the information contained in it to answer the user’s questions in a better way, also assisted by token probabilities to filter answers that might be wrong. A nice GUI and details on the kinds of things you can learn by working on this project make up for a complete guide and tutorial.**====  
  
====**通过向 GPT-3 提供 Pubmed 摘要，这个由 GPT-3 驱动的聊天机器人可以使用其中包含的信息以更好的方式回答用户的问题，还可以通过令牌概率来过滤可能错误的答案。一个漂亮的GUI和关于你可以通过这个项目学习的东西的细节构成了一个完整的指南和教程。**==
[![](https://miro.medium.com/v2/resize:fit:700/1*i89Kl8CfI9piBYxjBhBr5g.png)](https://miro.medium.com/v2/resize:fit:700/1*i89Kl8CfI9piBYxjBhBr5g.png)
==Photo composed by the author from Dall-E 2 generations and custom additions.====  
  
====照片由作者从Dall-E 2代和自定义添加中组成。==
==The need for control over the material that modern language models like ChatGPT and GPT-3 produce are becoming more and more important, as they can produce incredibly persuasive material even containing incorrect information. Without control, they may potentially create false information or hazardous content. Thus, it is essential that applications utilizing these language models make an effort to verify the accuracy of the data produced by these AI systems in order to stop the spread of inaccurate, deceptive, or dangerous information.====  
  
====控制像 ChatGPT 和 GPT-3 这样的现代语言模型产生的材料的需求变得越来越重要，因为它们可以产生令人难以置信的有说服力的材料，即使包含不正确的信息。如果没有控制，他们可能会创建虚假信息或危险内容。因此，利用这些语言模型的应用程序必须努力验证这些人工智能系统生成的数据的准确性，以阻止不准确、欺骗性或危险信息的传播。==
==I showed you recently that GPT-3 accompanies its next generations with a set of probabilities that measure how likely each token (akin to a syllable or word) is, and showed you that sometimes these probabilities help to detect the factual accuracy of the generated content:====  
  
====我最近向您展示了 GPT-3 伴随着它的下一代，其中包含一组概率来衡量每个标记（类似于音节或单词）的可能性，并向您展示有时这些概率有助于检测生成内容的事实准确性：==
==I also showed you how to easily pass information to GPT-3 for it to respond based on it by using few-shot learning. In particular, I showed you how to use your custom-created texts or articles retrieved on the fly from Wikipedia articles:====  
  
====我还向您展示了如何轻松地将信息传递给 GPT-3，以便它通过使用少镜头学习来基于它做出响应。特别是，我向您展示了如何使用从维基百科文章中即时检索的自定义创建的文本或文章：==
==As my expertise in creating GPT-3-based chatbots increases, I’m setting these mechanisms together to create “wiser” chatbots. But as I explained when closing== [==this other article==](https://lucianosphere.medium.com/why-you-should-and-how-you-can-inform-your-chatbots-with-custom-data-or-wikipedia-access-500995dc87f3)==, for hard science questions, it would be desirable to consult a specialized database. For example, Pubmed for questions revolving around biology, biochemistry, medicine, etc., as I have used here.====  
  
====随着我在创建基于 GPT-3 的聊天机器人方面的专业知识增加，我正在将这些机制放在一起以创建“更明智”的聊天机器人。但正如我在关闭另一篇文章时所解释的那样，对于硬科学问题，最好咨询专门的数据库。例如，Pubmed围绕生物学，生物化学，医学等的问题，正如我在这里使用的那样。==
==For this new project, I have created a new chatbot, also based in GPT-3 and entirely web-based, as always in my projects, that employs texts from Pubmed abstracts to assist its “thinking” when answering your questions. Besides, it filters its responses depending on the token probabilities returned by GPT-3. And for this project, I spent time improving the GUI so that the chatbot looks nicer and more professional.====  
  
====对于这个新项目，我创建了一个新的聊天机器人，也基于 GPT-3，并且完全基于 Web，就像在我的项目中一样，它使用 Pubmed 摘要中的文本来帮助它在回答您的问题时进行“思考”。此外，它还根据 GPT-3 返回的令牌概率过滤其响应。对于这个项目，我花时间改进了GUI，使聊天机器人看起来更好，更专业。==
## ==Introducing my “PubmedGPT”====  
  
====介绍我的“PubmedGPT”==
==PubMed is a free online database of biomedical literature maintained by the National Institutes of Health in the U.S. It collects all papers that have undergone peer review in various fields of research, mainly those revolving around the life sciences. This is all very valuable information that hasn’t been used to train GPT-3, because it is too technical. But as technical as it is, it might be useful to know for people working in the natural sciences.====  
  
====PubMed是由美国国立卫生研究院维护的免费在线生物医学文献数据库。它收集了在各个研究领域经过同行评审的所有论文，主要是围绕生命科学的论文。这些都是非常有价值的信息，尚未用于训练 GPT-3，因为它太技术性了。但是，尽管技术性很强，但对于从事自然科学工作的人来说，了解这些信息可能是有用的。==
==A chatbot that has access to PubMed should be able to give more accurate information and go further into technical and scientific aspects than regular GPT-3. Accessing Pubmed is rather easy because it features tools to programmatically search for relevant articles and extract their abstracts.====  
  
====与常规 GPT-3 相比，可以访问 PubMed 的聊天机器人应该能够提供更准确的信息并进一步进入技术和科学方面。访问 Pubmed 相当容易，因为它具有以编程方式搜索相关文章并提取其摘要的工具。==
==My new chatbot, which I have called PubmedGPT, replies first using GPT-3 only and then using abstracts taken from Pubmed, not only giving you a global reply but also listing links that you can follow to know more.====  
  
====我的新聊天机器人，我称之为 PubmedGPT，首先仅使用 GPT-3 进行回复，然后使用取自 Pubmed 的摘要，不仅为您提供全局回复，还列出了您可以关注以了解更多信息的链接。==
==The web app is free to access, but you need an API key. Here it is in action; in the next section, I’ll explain how it works, and later on, you have a link to go use it for free with your own OpenAI API key:====  
  
====Web 应用程序可免费访问，但您需要 API 密钥。在这里，它正在行动;在下一节中，我将解释它是如何工作的，稍后，你有一个链接，可以使用自己的OpenAI API密钥免费使用它：==
[![](https://miro.medium.com/v2/resize:fit:700/1*PbfQZLJesLxIvQ3US6wfbw.png)](https://miro.medium.com/v2/resize:fit:700/1*PbfQZLJesLxIvQ3US6wfbw.png)
## ==How PubmedGPT works PubmedGPT 的工作原理==
## ==Pubmed and its free APIs====  
  
====Pubmed 及其免费 API==
==Pubmed is part of the== [==U.S. National Library of Medicine==](https://www.nlm.nih.gov/) ==and the== [==National Center for Biotechnology Information==](https://www.ncbi.nlm.nih.gov/) ==(NCBI), which offer a series of public APIs branded together as the Entrez Programming Utilities. These utilities allow access to all Entrez databases, including PubMed, of interest here, as well as PMC, Gene, Nuccore, and Protein databases. Each Entrez program accepts a fixed URL syntax for search, link, and retrieval operations.====  
  
====Pubmed是美国国家医学图书馆和国家生物技术信息中心（NCBI）的一部分，该中心提供一系列公共API，共同称为Entrez Programming Utilities。这些实用程序允许访问所有Entrez数据库，包括PubMed，以及PMC，Gene，Nuccore和Protein数据库。每个 Entrez 程序都接受用于搜索、链接和检索操作的固定 URL 语法。==
==The core API calls we need to search Pubmed for some keywords is this:====  
  
====我们需要在 Pubmed 中搜索一些关键字的核心 API 调用是这样的：==
==https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pubmed&retmode=json&retmax=5&term=keywords...==
==Notice that: 请注意：==
- ==I’m asking for up to 5 results only (remember that the more text you send to GPT-3, the more tokens and credits you consume)====  
      
    ====我只要求最多 5 个结果（请记住，您发送给 GPT-3 的文本越多，您消耗的代币和积分就越多）==
- ==I’m requesting to get the results as a JSON, whose structure will naturally facilitate the use of the retrieved information.====  
      
    ====我请求以 JSON 的形式获取结果，其结构自然会促进检索到的信息的使用。==
==When doing the first tests, it’s handy to call this API right from the browser. Here’s how Firefox formats, for example, a search for “photosynthesis”:====  
  
====在进行第一次测试时，直接从浏览器调用此 API 很方便。以下是Firefox的格式，例如，搜索“光合作用”：==
[![](https://miro.medium.com/v2/resize:fit:700/1*YSH35omcP5gDFGlccbZhpQ.png)](https://miro.medium.com/v2/resize:fit:700/1*YSH35omcP5gDFGlccbZhpQ.png)
==You clearly see the Pubmed IDs for the 5 retrieved articles in the array called== ==_idlist_====.====  
  
====您可以清楚地看到名为 idlist 的数组中检索到 5 篇文章的 Pubmed ID。==
==One important point is what keywords to use for the search. A possibility would be to send the whole question as asked by the user; however, to simplify the search, I first strip the question off stopwords, using the same script I used in my previous article on== [==feeding GPT-3 with Wikipedia articles==](https://towardsdatascience.com/a-question-answering-bot-powered-by-wikipedia-coupled-to-gpt-3-56889b5976d7)==.====  
  
====重要的一点是使用哪些关键字进行搜索。一种可能性是按照用户的要求发送整个问题;但是，为了简化搜索，我首先将问题从停用词中删除，使用我在上一篇文章中使用的相同脚本，即用维基百科文章为 GPT-3 提供。==
==You should also know that you can modify the search to indicate what kinds of articles to search, how to sort the list, etc. For example, my app uses these modifiers at the end of the search: [EPDATE][REVIEW][ABSTRACT] to indicate that I want Entrez to search the keywords inside the abstracts of reviews only and that I want the results to be sorted by date.====  
  
====您还应该知道，您可以修改搜索以指示要搜索的文章类型，如何对列表进行排序等。例如，我的应用程序在搜索结束时使用这些修饰符：[EPDATE][REVIEW][ABSTRACT]，以指示我希望 Entrez 仅搜索评论摘要中的关键字，并且我希望结果按日期排序。==
==Next, once we have the list of relevant articles, we need to use a different API call to get their texts. In this case, it will look something like this:====  
  
====接下来，一旦我们有了相关文章的列表，我们需要使用不同的 API 调用来获取它们的文本。在这种情况下，它将如下所示：==
==https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pubmed&retmode=text&rettype=abstract&id=pubmedid==
==Notice that here we are asking for a string. JSON would probably work, but by already getting a string we make things simpler.====  
  
====请注意，这里我们要求一个字符串。JSON可能会起作用，但是通过已经获得一个字符串，我们使事情变得更简单。==
## ==Fetching abstracts and GPT-3’s replies asynchronously====  
  
====异步获取摘要和 GPT-3 的回复==
==PubmedGPT takes each abstract, appends the user’s question, and then sends this to GPT-3 through its API to the same endpoint that you’d normally use to complete texts, here, in particular, using== ==_text-davinci-003_====.====  
  
====PubmedGPT 获取每个摘要，附加用户的问题，然后通过其 API 将其发送到 GPT-3 到您通常用于完成文本的同一端点，特别是在这里，使用 text-davinci-003。==
==To make the API calls to Entrez or GPT-3 from the JavaScript code, we use the== ==**fetch()**== ==function, much like explained here:====  
  
====为了从 JavaScript 代码对 Entrez 或 GPT-3 进行 API 调用，我们使用 fetch（） 函数，就像这里解释的那样：==
==One complication that arises, though, is that this API calls through== ==**fetch()**== ==are asynchronous, meaning they will not block the execution of the rest of the code while they are waiting for responses from the servers. Thus, you cannot ask Entrez for the abstract of an article right after asking for the list of articles or make a call GPT-3 right after calling Entrez to get the abstract. Instead, you have to wait for each API call to finish before you make the next call.====  
  
====但是，出现的一个复杂问题是，通过 fetch（） 进行的此 API 调用是异步的，这意味着它们在等待服务器响应时不会阻止其余代码的执行。因此，您不能在询问文章列表后立即向 Entrez 索要文章摘要，也不能在致电 Entrez 获取摘要后立即致电 GPT-3。相反，您必须等待每个 API 调用完成，然后才能进行下一次调用。==
==To handle this,== ==**fetch()**== ==returns a promise that will resolve with the response once it is received. This allows other code to continue to execute while the fetch request is in progress. You have to insert subsequent API calls nested inside the response blocks.====  
  
====为了解决这个问题，fetch（） 返回一个承诺，一旦收到响应，该承诺就会通过响应解决。这允许其他代码在提取请求正在进行时继续执行。您必须插入嵌套在响应块中的后续 API 调用。==
==As an example, this is a simplified form of how calling GPT-3 to respond to a question using information from an abstract is nested inside the call to Entrez that retrieves the abstract in the first place:====  
  
====例如，这是一种简化的形式，说明调用 GPT-3 以使用摘要中的信息回答问题的方式嵌套在对 Entrez 的调用中，该调用首先检索摘要：==
==fetch("https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pubmed&retmode=text&rettype=abstract&id=" + abstractid====  
  
====).then(response => response.text())====  
  
====.then(abstract => {====  
  
====console.log(abstract);====  
  
====var theprompt = abstract + "\n\n" + userQuestion====  
  
====fetch( `https://api.openai.com/v1/completions`,====  
  
===={====  
  
====body: JSON.stringify({"model": "text-davinci-003", "prompt": theprompt, "temperature": 0, "max_tokens": 2000, "logprobs": 1}),====  
  
====method: "POST",====  
  
====headers: {====  
  
===="content-type": "application/json",====  
  
====Authorization: "Bearer " + apikey,====  
  
====},====  
  
====}====  
  
====).then((response) => {====  
  
====if (response.ok) {====  
  
====response.json().then((json) => {====  
  
====console.log(json);====  
  
====var textout = json.choices[0].text.trim()==
```plain
          });  
      } else {  
              
      }  
  });  
.catch(error => {  
       
});
```
## ==Filtering answers 筛选答案==
==We saw previously that, although not fully reliable, the token probabilities provided by GPT-3 do help somewhat in identifying text generations with incorrect information (==[==this article==](https://towardsdatascience.com/exploring-token-probabilities-as-a-means-to-filter-gpt-3s-answers-3e7dfc9ca0c)==).====  
  
====我们之前看到，虽然不完全可靠，但 GPT-3 提供的令牌概率确实有助于识别具有错误信息的文本生成（本文）。==
==But how, exactly, do we filter the generated text?====  
  
====但是，我们究竟如何过滤生成的文本呢？==
==My app iterates through the token log probabilities contained in the JSON returned by GPT-3’s API. I explored different ways to filter out the bad content: averaging the probabilities, counting how many tokens score under a threshold, etc. In the end, I converged to counting the number of tokens with log probability under -1.5, then dividing this by the number of tokens that composes the whole generation, and then discarding the generated text only if that number is above 0.2.====  
  
====我的应用循环访问 GPT-3 的 API 返回的 JSON 中包含的令牌日志概率。我探索了过滤掉不良内容的不同方法：平均概率，计算有多少代币得分低于阈值等。最后，我收敛到计算对数概率低于 -1.5 的标记数量，然后将其除以构成整个生成的标记数量，然后仅在该数字高于 0.2 时才丢弃生成的文本。==
==In other words, the app is only showing the generated text when it contains less than 20% bad tokens, these being defined as having log probability under -1.5. Of course, these thresholds are a bit arbitrary; I set them by trial and error, but they are not infallible.====  
  
====换句话说，应用仅在包含少于 20% 的错误令牌时显示生成的文本，这些标记被定义为对数概率低于 -1.5。当然，这些阈值有点武断;我通过反复试验来设置它们，但它们并非万无一失。==
==Here you have how exactly this is achieved:====  
  
====在这里，您可以确切地实现这一点：==
==var logprobs = json.choices[0].logprobs.token_logprobs;====  
  
====var nBadAnswers= 0;====  
  
====for (var i=0;i<logprobs.length; i++) {====  
  
====if (logprobs[i] < -1.5) {nBadAnswers++}====  
  
====}====  
  
====if (nBadAnswers/logprobs.length < 0.2) {==
==}else{==
==}==
## ==A nice GUI thanks to some CSS====  
  
====一个不错的GUI，感谢一些CSS==
==I was never very much into CSS, but for a client, I had to learn at least some minimal styling scripting to make the layout friendly. You saw above how PubmedGPT looks on a laptop’s screen, and here you can see how the same page looks on a smartphone:====  
  
====我从来都不太喜欢CSS，但对于客户来说，我必须至少学习一些最小的样式脚本才能使布局友好。您在上面看到了PubmedGPT在笔记本电脑屏幕上的外观，在这里您可以看到同一页面在智能手机上的外观：==
[![](https://miro.medium.com/v2/resize:fit:700/1*Vu0-Je74cSoW08v0CDwCUA.png)](https://miro.medium.com/v2/resize:fit:700/1*Vu0-Je74cSoW08v0CDwCUA.png)
==Honestly, CSS is, to me, quite boring. But, I admit it, it’s also necessary. Here are a couple of resources I used to learn the basics -and then it’s all just asking Google or ChatGPT:====  
  
====老实说，CSS对我来说很无聊。但是，我承认，这也是必要的。以下是我用来学习基础知识的一些资源 - 然后只是问谷歌或ChatGPT：==
==Then in my final CSS code (which you can consult in the source code), there are no special tricks. For example, this is how I formatted the== ==_Submit_== ==and== ==_Refresh_== ==buttons:====  
  
====然后在我的最终CSS代码中（您可以在源代码中查阅），没有特殊的技巧。例如，这是我格式化“提交”和“刷新”按钮的方式：==
==button[type="buttons"] {====  
  
====margin-left: 10px;====  
  
====padding: 10px;====  
  
====border: none;====  
  
====background-color: \#0000FF;====  
  
====color: \#fff;====  
  
====cursor: pointer;====  
  
====border-radius: 4px;====  
  
====width: 100px;====  
  
====}==
## ==The link to PubmedGPT 指向 PubmedGPT 的链接==
==Here’s the link to my PubmedGPT app, which you can use right away. Disclaimer: of course, I’m not responsible for the information provided by the chatbot and for any actions you take based on this information.====  
  
====这是我的PubmedGPT应用程序的链接，您可以立即使用它。免责声明：当然，我不对聊天机器人提供的信息以及您根据此信息采取的任何行动负责。==
==As in all my other examples, to run this, you need to get an API key from OpenAI here:== [==https://beta.openai.com/account/api-keys==](https://beta.openai.com/account/api-keys)==. Normally, OpenAI gives you some free tokens to use, and then you can purchase more.====  
  
====与我所有其他示例一样，要运行它，您需要在此处从 OpenAI 获取一个 API 密钥：https://beta.openai.com/account/api-keys。通常，OpenAI会为您提供一些免费的代币供您使用，然后您可以购买更多代币。==
==(Note that what you actually see when accessing my app may change a bit compared to the pics above because I keep making some edits and improvements here and there from time to time.)====  
  
====（请注意，与上面的图片相比，您在访问我的应用程序时实际看到的内容可能会有所变化，因为我不时在这里和那里进行一些编辑和改进。==
## ==Related articles 相关文章==
[==_**www.lucianoabriata.com**_==](https://www.lucianoabriata.com/) ==_I write and photoshoot about everything that lies in my broad sphere of interests: nature, science, technology, programming, etc._== [==_**Become a Medium member**_==](https://lucianosphere.medium.com/membership) ==_to access all its stories (affiliate links of the platform for which I get small revenues without cost to you) and_== [==_**subscribe to get my new stories**_==](https://lucianosphere.medium.com/subscribe) ==_**by email**_====_. To_== ==_**consult about small jobs,**_== ==_check my_== [==_**services page here**_==](https://lucianoabriata.altervista.org/services/index.html)==_. You can_== [==_**contact me here**_==](https://lucianoabriata.altervista.org/office/contact.html)==_**.**_====  
  
====www.lucianoabriata.com，我写作和拍摄我广泛兴趣范围内的一切：自然、科学、技术、编程等。成为Medium会员以访问其所有故事（平台的会员链接，我免费获得少量收入）并订阅以通过电子邮件获取我的新故事。要咨询小型工作，请在此处查看我的服务页面。你可以在这里联系我。==