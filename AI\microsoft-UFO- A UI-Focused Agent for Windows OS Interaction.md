---
Updated: 2024-02-16T18:24
tags:
  - AI->-Programming
  - AI->Automation
URL: https://github.com/microsoft/UFO
Created: 2024-02-16T14:39
---
[![](https://opengraph.githubassets.com/a5bab44f753a252009bade370206e4d2d68c3befc705b7d108ad488b9614858a/microsoft/UFO)](https://opengraph.githubassets.com/a5bab44f753a252009bade370206e4d2d68c3befc705b7d108ad488b9614858a/microsoft/UFO)
## ==**UFO**== ==: A== ==**U**====I-====**F**====ocused Agent for Windows== ==**O**====S Interaction==
[![](https://camo.githubusercontent.com/27bb2b00c3750235d095eb7d9f13025fe9485e7ffc6dcd6952771b2873005209/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f507974686f6e2d3337373641423f266c6f676f3d707974686f6e266c6f676f436f6c6f723d77686974652d626c7565266c6162656c3d332e3130253230253743253230332e3131)](https://camo.githubusercontent.com/27bb2b00c3750235d095eb7d9f13025fe9485e7ffc6dcd6952771b2873005209/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f507974686f6e2d3337373641423f266c6f676f3d707974686f6e266c6f676f436f6c6f723d77686974652d626c7565266c6162656c3d332e3130253230253743253230332e3131)
[![](https://camo.githubusercontent.com/a4426cbe5c21edb002526331c7a8fbfa089e84a550567b02a0d829a98b136ad0/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4c6963656e73652d4d49542d79656c6c6f772e737667)](https://camo.githubusercontent.com/a4426cbe5c21edb002526331c7a8fbfa089e84a550567b02a0d829a98b136ad0/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f4c6963656e73652d4d49542d79656c6c6f772e737667)
[![](https://camo.githubusercontent.com/8531ea80bc5e0ac96a01c1f2e18f168ca543ffd837522065bcf93f238774d4b8/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f636f6e747269627574696f6e732d77656c636f6d652d627269676874677265656e2e7376673f7374796c653d666c6174)](https://camo.githubusercontent.com/8531ea80bc5e0ac96a01c1f2e18f168ca543ffd837522065bcf93f238774d4b8/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f636f6e747269627574696f6e732d77656c636f6d652d627269676874677265656e2e7376673f7374796c653d666c6174)
==**UFO**== ==is a== ==**UI-Focused**== ==dual-agent framework to fulfill user requests on== ==**Windows OS**== ==by seamlessly navigating and operating within individual or spanning multiple applications.==
## ==🕌 Framework==
==**UFO**== ==operates as a dual-agent framework, encompassing:==
[![](https://github.com/microsoft/UFO/raw/main/assets/ufo_blue.png)](https://github.com/microsoft/UFO/raw/main/assets/ufo_blue.png)
- ==**AppAgent 🤖**====, tasked with choosing an application for fulfilling user requests. This agent may also switch to a different application when a request spans multiple applications, and the task is partially completed in the preceding application.==
- ==**ActAgent 👾**====, responsible for iteratively executing actions on the selected applications until the task is successfully concluded within a specific application.==
- ==**Control Interaction 🎮**====, is tasked with translating actions from AppAgent and ActAgent into interactions with the application and its UI controls. It's essential that the targeted controls are compatible with the Windows== ==**UI Automation**== ==API.==
==Both agents leverage the multi-modal capabilities of GPT-Vision to comprehend the application UI and fulfill the user's request. For more details, please consult our== [==technical report==](https://arxiv.org/abs/2402.07939)==.==
## ==🆕 News==
- ==📅 2024-02-14: Our== [==technical report==](https://arxiv.org/abs/2402.07939) ==is online!==
- ==📅 2024-02-10: UFO is released on GitHub🎈. Happy Chinese New year🐉!==
## ==💥 Highlights==
- ==**First Windows Agent**== ==- UFO is the pioneering agent framework capable of translating user requests in natural language into actionable operations on Windows OS.==
- ==**Interactive Mode**== ==- UFO facilitates multiple sub-requests from users within the same session, enabling the completion of complex tasks seamlessly.==
- ==**Action Safeguard**== ==- UFO incorporates safeguards to prompt user confirmation for sensitive actions, enhancing security and preventing inadvertent operations.==
- ==**Easy Extension**== ==- UFO offers extensibility, allowing for the integration of additional functionalities and control types to tackle diverse and intricate tasks with ease.==
## ==✨ Getting Started==
### ==🛠️ Step 1: Installation==
==UFO requires== ==**Python >= 3.10**== ==running on== ==**Windows OS >= 10**====. It can be installed by running the following command:==
# ==[optional to create conda environment]==
# ==conda create -n ufo python=3.10==
# ==conda activate ufo==
# ==clone the repository==
==git clone https://github.com/microsoft/UFO.git  
cd UFO  
==
# ==install the requirements==
==pip install -r requirements.txt==
### ==⚙️ Step 2: Configure the LLMs==
==Before running UFO, you need to provide your LLM configurations. Taking OpenAI as an example, you can configure== ==`ufo/config/config.yaml`== ==file as follows.==
### ==OpenAI==
```plain
API_TYPE: "openai" 
OPENAI_API_BASE: "https://api.openai.com/v1/chat/completions" # The base URL for the OpenAI API
OPENAI_API_KEY: "YOUR_API_KEY"  # Set the value to the openai key for the llm model
OPENAI_API_MODEL: "GPTV_MODEL_NAME"  # The only OpenAI model by now that accepts visual input
```
### ==Azure OpenAI (AOAI)==
```plain
API_TYPE: "aoai" 
OPENAI_API_BASE: "YOUR_ENDPOINT" # The AOAI API address. Format: https://{your-resource-name}.openai.azure.com/openai/deployments/{deployment-id}/completions?api-version={api-version}
OPENAI_API_KEY: "YOUR_API_KEY"  # Set the value to the openai key for the llm model
OPENAI_API_MODEL: "GPTV_MODEL_NAME"  # The only OpenAI model by now that accepts visual input
```
### ==🎉 Step 3: Start UFO==
### ==⌨️ You can execute the following on your Windows command Line (CLI):==
# ==assume you are in the cloned UFO folder==
==python -m ufo --task <your_task_name>==
==This will start the UFO process and you can interact with it through the command line interface. If everything goes well, you will see the following message:==
==Welcome to use UFO🛸, A UI-focused Agent for Windows OS Interaction.  
_ _ _____ ___  
| | | || ___| / _ \  
| | | || |_ | | | |  
| |_| || _| | |_| |  
\___/ |_| \___/  
Please enter your request to be completed🛸:  
==
### ==⚠️Reminder:==
- ==Before UFO executing your request, please make sure the targeted applications are active on the system.==
- ==The GPT-V accepts screenshots of your desktop and application GUI as input. Please ensure that no sensitive or confidential information is visible or captured during the execution process. For further information, refer to== [==DISCLAIMER.md==](https://github.com/microsoft/UFO/blob/main/DISCLAIMER.md)==.==
### ==Step 4 🎥: Execution Logs==
==You can find the screenshots taken and request & response logs in the following folder:==
```plain
./ufo/logs/<your_task_name>/
```
==You may use them to debug, replay, or analyze the agent output.==
## ==❓Get help==
- ==❔GitHub Issues (prefered)==
- ==For other communications, please contact== <EMAIL>
## ==🎬 Demo Examples==
==We present two demo videos that complete user request on Windows OS using UFO. For more case study, please consult our== [==technical report==](https://arxiv.org/abs/2402.07939)==.==
### ==1️⃣🗑️ Example 1: Deleting all notes on a PowerPoint presentation.==
==In this example, we will demonstrate how to efficiently use UFO to delete all notes on a PowerPoint presentation with just a few simple steps. Explore this functionality to enhance your productivity and work smarter, not harder!==
==ufo_delete_note.mp4==
### ==2️⃣📧 Example 2: Composing an email using text from multiple sources.==
==In this example, we will demonstrate how to utilize UFO to extract text from Word documents, describe an image, compose an email, and send it seamlessly. Enjoy the versatility and efficiency of cross-application experiences with UFO!==
==ufo_meeting_note_crossed_app_demo_new.mp4==
## ==📊 Evaluation==
==Please consult the== [==WindowsBench==](https://arxiv.org/pdf/2402.07939.pdf) ==provided in Section A of the Appendix within our technical report. Here are some tips (and requirements) to aid in completing your request:==
- ==Prior to UFO execution of your request, ensure that the targeted application is active (though it may be minimized).==
- ==Occasionally, requests to GPT-V may trigger content safety measures. UFO will attempt to retry regardless, but adjusting the size or scale of the application window may prove helpful. We are actively solving this issue.==
- ==Currently, UFO supports a limited set of applications and UI controls that are compatible with the Windows== ==**UI Automation**== ==API. Our future plans include extending support to the Win32 API to enhance its capabilities.==
- ==Please note that the output of GPT-V may not consistently align with the same request. If unsuccessful with your initial attempt, consider trying again.==
## ==📚 Citation==
==Our technical report paper can be found== [==here==](https://arxiv.org/abs/2402.07939)==. If you use UFO in your research, please cite our paper:==
```plain
@article{ufo,
  title={{UFO: A UI-Focused Agent for Windows OS Interaction}},
  author={Zhang, Chaoyun and Li, Liqun and He, Shilin and  Zhang, Xu and Qiao, Bo and  Qin, Si and Ma, Minghua and Kang, Yu and Lin, Qingwei and Rajmohan, Saravan and Zhang, Dongmei and  Zhang, Qi},
  journal={arXiv preprint arXiv:2402.07939},
  year={2024}
}
```
## ==🎨 Related Project==
==You may also find== [==TaskWeaver==](https://github.com/microsoft/TaskWeaver?tab=readme-ov-file) ==useful, a code-first LLM agent framework for seamlessly planning and executing data analytics tasks.==
## ==⚠️ Disclaimer==
==By choosing to run the provided code, you acknowledge and agree to the following terms and conditions regarding the functionality and data handling practices in== [==DISCLAIMER.md==](https://github.com/microsoft/UFO/blob/main/DISCLAIMER.md)
## ==Trademarks==
==This project may contain trademarks or logos for projects, products, or services. Authorized use of Microsoft trademarks or logos is subject to and must follow== [==Microsoft's Trademark & Brand Guidelines==](https://www.microsoft.com/en-us/legal/intellectualproperty/trademarks/usage/general)==. Use of Microsoft trademarks or logos in modified versions of this project must not cause confusion or imply Microsoft sponsorship. Any use of third-party trademarks or logos are subject to those third-party's policies.==