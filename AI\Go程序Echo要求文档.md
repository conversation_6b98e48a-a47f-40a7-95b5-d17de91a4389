---
Updated: 2023-03-05T23:07
tags:
  - AI->-Programming
Created: 2023-03-05T22:59
---
## 要求
1. 编写一个简单的echo程序，可以将用户输入的内容原样输出。
2. 用户可以使用`shift+enter`进行多行输入，但如果只输入`\\n`，则输入结束。
## 实现
```Plain
package main
import (
    "bufio"
    "fmt"
    "os"
    "strings"
)
func main() {
    fmt.Println("请输入内容，多行输入请使用shift+enter，单独输入回车结束")
    scanner := bufio.NewScanner(os.Stdin)
    var lines []string
    for scanner.Scan() {
        line := scanner.Text()
        if line == "" {
            break
        }
        lines = append(lines, line)
    }
    fmt.Println(strings.Join(lines, "\\n"))
}
```
## 使用说明
1. 打开命令行终端。
2. 进入程序所在的目录。
3. 输入`go run main.go`，并按下回车键。
4. 按照提示输入内容，多行输入请使用`shift+enter`，单独输入回车结束。
5. 程序将会输出您输入的内容。
注意：如果您的电脑上没有安装Go语言环境，请先安装Go语言环境。