---
Updated: 2023-10-31T08:38
tags:
  - AI->-Competition
  - AI->-Dataset
  - AI->-Programming
Created: 2023-10-31T08:38
---
[![](https://miro.medium.com/v2/resize:fit:1200/1*vGf8PYRayK_JvOHH8PIN0Q.jpeg)](https://miro.medium.com/v2/resize:fit:1200/1*vGf8PYRayK_JvOHH8PIN0Q.jpeg)
## ==Learn how visualizations, algorithms, and statistics help you to identify anomalies for your machine learning tasks.==
==[==
[![](https://miro.medium.com/v2/resize:fill:44:44/1*87UizUSgwULHOoaiid9ugw.jpeg)](https://miro.medium.com/v2/resize:fill:44:44/1*87UizUSgwULHOoaiid9ugw.jpeg)
==](https://medium.com/@riccardo.andreoni?source=post_page-----382d1411b8ec--------------------------------)[==
[![](https://miro.medium.com/v2/resize:fill:24:24/1*CJe3891yB1A1mzMdqemkdg.jpeg)](https://miro.medium.com/v2/resize:fill:24:24/1*CJe3891yB1A1mzMdqemkdg.jpeg)
==](https://towardsdatascience.com/?source=post_page-----382d1411b8ec--------------------------------)==
[![](https://miro.medium.com/v2/resize:fit:700/1*vGf8PYRayK_JvOHH8PIN0Q.jpeg)](https://miro.medium.com/v2/resize:fit:700/1*vGf8PYRayK_JvOHH8PIN0Q.jpeg)
==What do balloons have to do with outliers? Find the answer in the introduction. Image source:== [==pixabay.com==](https://pixabay.com/illustrations/balloons-spring-nature-watercolor-1615032/)==.==
==Imagine a room filled with== ==**colorful balloons**====, each symbolizing a data point in a dataset. Due to their different features, the balloons float at different heights. Now, picture some== ==**helium-filled balloons**== ==that unexpectedly soar far above the rest. Just as these exceptional balloons disrupt the uniformity of the room, outliers disrupt the pattern in a dataset.==
==Returning from this colorful analogy to pure statistic,== ==**outliers**== ==are defined as anomalies, or better, data points that deviate significantly from the rest of the dataset.==
==Consider a== ==**Machine Learning algorithm**== ==developed to diagnose diseases based on patient data. In this real-world example, outliers could be extremely high values in laboratory results or physiological parameters. While their origin may consist in various reasons like== ==**data collection errors**====,== ==**measurement inaccuracies**====, or genuine== ==**rare events**====, their presence can lead the algorithm to make incorrect diagnoses.==
==This is the reason why we, Machine Learning or Data Science practitioners, must always== ==**treat outliers with care**====.==
==In this short post, I will discuss several methods to efficiently identify and remove outliers from your data.==
==One of them is== [==**SVM**==](https://en.wikipedia.org/wiki/Support_vector_machine)==, which I explored in this post.==
## ==What Are Outliers?==
==Outliers are== ==**nonrepresentative data points**== ==in a dataset, or better, data points that deviate significantly from the rest. Despite their simple definition, detecting these anomalies is not always straightforward but first, let’s answer the following basic question.==
==Why do we want to detect outliers in a dataset?==
==There exist two answers to this question. The== ==**first reason**== ==for detecting outliers is that these…==