---
Updated: 2023-12-11T16:11
tags:
  - AI->-<PERSON><PERSON>&Gemini
  - AI->-Programming
  - AI->-YouTube
Created: 2023-12-11T14:14
---
Google Cloud → Vertex AI → Language
(使用前，
create new project → APIs&Services → Enable APIs&Services → search vertex ai → Enable Vertex AI
IAM&Admin → Service Account → Create service account if not existing, if it is there, then actions → manage keys
Add Key → create new key → JSON (it will generate one json key and download it， save this json file
)
[https://python.langchain.com/docs/integrations/chat/google_vertex_ai_palm](https://python.langchain.com/docs/integrations/chat/google_vertex_ai_palm)
  
```Python
G:\ResearchDirection\AI\langchain\ChatVertexAITest.ipynb
import os
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = r'G:\ResearchDirection\AI\VertexAI\key\vertexai-407508-5a8228b55411.json'
from langchain.chat_models import ChatVertexAI
from langchain.prompts import ChatPromptTemplate
system = "You are a helpful assistant who translate English to French"
human = "Translate this sentence from English to French. I love programming."
prompt = ChatPromptTemplate.from_messages([("system", system), ("human", human)])
chat = ChatVertexAI()
chain = prompt | chat
chain.invoke({})
```
  
  
[![](https://i.ytimg.com/vi/Pv4UsF3LkWA/maxresdefault.jpg)](https://i.ytimg.com/vi/Pv4UsF3LkWA/maxresdefault.jpg)