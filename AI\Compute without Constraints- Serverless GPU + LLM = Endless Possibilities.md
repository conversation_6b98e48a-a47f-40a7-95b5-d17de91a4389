---
Updated: 2023-09-03T12:39
tags:
  - AI->-Image
  - AI->-Programming
Created: 2023-09-03T12:39
---
[![](https://miro.medium.com/v2/resize:fit:1200/0*dpgGMS-KxHx79_3T.png)](https://miro.medium.com/v2/resize:fit:1200/0*dpgGMS-KxHx79_3T.png)
---
image created by the author and Leonardo.ai
![[0dpgGMS-KxHx79_3T.png]]
For developers working with large language models, the constraints of hardware can often hold back the boundaries of what’s possible. In fact securing access to GPUs requires a lot of upfront investment and technical overhead.
For developers working with large language models, the constraints of hardware can often hold back the boundaries of what’s possible. In fact securing access to GPUs requires a lot of upfront investment and technical overhead.
But what if you could plan and use your AI projects paying only for the resources used, without any infrastructure to procure or maintain? That’s the promise of combining serverless computing with powerful GPU accelerators in the cloud.
# Introduction
I am a fan of open-source, but even if I hardly want to admit it, there are limits to the free resources in terms of computational power. I don’t even have an Nvidia GPU so I am stuck with my 16 GB of RAM and my CPU.
If you want to scale up your projects or even simply use more powerful AI models, you need more resources. In this article, we will explore [beam.cloud](http://beam.cloud/), an easy and powerful swiss-army-knife for running LLMs and code directly on the cloud.
We’ll explore how leveraging serverless GPU computing opens up new possibilities for you and your AI applications.
We’ll discuss how it streamlines workflows for NLP tasks, enables real-time conversations with giant conversational models, and much more. By paying only for actual usage, serverless puts the full potential of GPU-accelerated AI within everyone’s reach.
Ah, [beam.cloud](http://beam.cloud/) free tier gives you 10 hours of GPU for free! And you pay only for the GPU time you use, nothing more, nothing less.
the Twitter Story Creator App — image byt the author
![[0CTyFUBjbT8HQoXVa.gif]]
# The project I have in mind
I wanted to create an app that generates a twitter post with creative and original content, and at the same time take my pictures to another level.
So this will be a **Twitter Story Creator App** and I will use [Orca-mini-3B](https://huggingface.co/psmathur/orca_mini_3b) parameters for the tweet creation, and [Stable Diffusion 1.5](https://huggingface.co/runwayml/stable-diffusion-v1-5) for AI image generation. Both of these models cannot run on a standard consumer hardware, so it is the best playground for a serverless GPU test.
# App structure
[Beam Cloud](https://www.beam.cloud/) (from now on simply Beam) is really easy and straight forward to setup. When you register with your email, you get immediately access to 10 hours of GPU time and up to 3 applications.
We will need 2 apps from Beam: one to generate the tweet post with the hashtags (I called this app TweetGenerator) and one to generate an image from an existing one (image2image generation — I called this app TweetDreams).
The amazing thing is that you can create the apps directly on your computer, deploy them in an instant to Beam, and get access to them with a super useful API.
diaagram and image by the author
![[07TrocbkjcKMJjr44.png]]
As soon as you register, you get a welcome message with the code to install the SDK, called Beam CLI, and your API keys (required to deploy your apps).
beam.cloud Getting Started page
![[0HouClARNIKeDH2BR.png]]
Paste the commands in the terminal and you are ready to go.
You can follow along coding, it is the best way to learn. And you can find all the app details in my GitHub Repo:
## [GitHub - fabiomatricardi/twitterStoryCreator: Repository for the Article on Medium about Serverless…](https://github.com/fabiomatricardi/twitterStoryCreator/tree/main?source=post_page-----c771968e74b5--------------------------------)
### [Repository for the Article on Medium about Serverless GPU - GitHub - fabiomatricardi/twitterStoryCreator: Repository…](https://github.com/fabiomatricardi/twitterStoryCreator/tree/main?source=post_page-----c771968e74b5--------------------------------)
[github.com](https://github.com/fabiomatricardi/twitterStoryCreator/tree/main?source=post_page-----c771968e74b5--------------------------------)
# TweetGenerator App
This app is for text-generation. We send the topic of the post and we will receive back a creative tweet with all the fancy tags.
I took inspiration from the documentation of Beam: since orca-mini-3B is using LlamaForCausalLM and LlamaTokenizer I started with the code related to the Llama 2 inference (docs: [https://docs.beam.cloud/examples/llama](https://docs.beam.cloud/examples/llama))
Open your preferred code editor and create a file called [tweet.py](http://tweet.py/)
Here the content of the file… don’t freak out, we will go through the code 🤣.
```Plain
from beam import App, Runtime, Image, Output, Volume, VolumeType
import os
import torch
from io import BytesIO
import base64
from transformers import LlamaForCausalLM, LlamaTokenizer
import sentencepiece
# The environment your code will run on
app = App(
    name="TweetGenerator",
    runtime=Runtime(
        cpu=8,
        memory="32Gi",
        gpu="A10G",
        image=Image(
            python_version="python3.10",
            python_packages=[
                "accelerate>=0.16.0,<1",
                "transformers[torch]>=4.28.1,<5",
                "torch>=1.13.1,<2",
                "langchain",
                "sentencepiece",
                "xformers",
                "protobuf"
            ],
        ),
    ),
    volumes=[
        Volume(
            name="model_weights",
            path="./model_weights",
            volume_type=VolumeType.Persistent,
        )
    ],
)
# Cached model
cache_path = "./model_weights"
# Huggingface model
model_id = "psmathur/orca_mini_3b"
def load_models():
    tokenizer = LlamaTokenizer.from_pretrained(
        model_id, cache_dir=cache_path, legacy=False, use_fast=False
    )
    model = LlamaForCausalLM.from_pretrained(
        model_id,
        torch_dtype=torch.float16,
        device_map="auto",
        cache_dir=cache_path,
    )
    return tokenizer, model
```
Here we are declaring the dependencies, the profile (hardware, memory, GPU) of our application and a Volume to store the model (so that you don’t need to download the weight every time you run it).
The `load_models()`function is here to [speedup the loading process](https://docs.beam.cloud/deployment/cold-start#caching) of the model weights. In fact, we will pass this function into the main API function so that the model and the tokenizer will be immediately ready as soon as the API call arrives.
Now, we have to tell Beam to create an API endpoint (which is your access door to the serverless LLM).
We simply add a `rest_api()` decorator to our function, which will allow it to be deployed onto Beam:
```Plain
@app.rest_api(loader=load_models)
def generate(**inputs):
    # Retrieve cached model from the loader
    tokenizer, model = inputs["context"]
    myprompt = inputs["myprompt"]
    # Generate output
    you = myprompt
    instruction = f"Write a creative twitter post about '{you}'."
    system = "You are an AI assistant that follows instruction extremely well. Help as much as you can."
    if input:
        prompt = f"### System:\n{system}\n\n### User:\n{instruction}\n\n### Input:\n{input}\n\n### Response:\n"
    else:
        prompt = f"### System:\n{system}\n\n### User:\n{instruction}\n\n### Response:\n"
    tokens = tokenizer.encode(prompt)
    tokens = torch.LongTensor(tokens).unsqueeze(0)
    tokens = tokens.to("cuda")
    instance = {
        "input_ids": tokens,
        "top_p": 1.0,
        "temperature": 0.7,
        "generate_len": 1024,
        "top_k": 50,
    }
    length = len(tokens[0])
    with torch.no_grad():
        rest = model.generate(
            input_ids=tokens,
            max_length=length + instance["generate_len"],
            use_cache=True,
            do_sample=True,
            top_p=instance["top_p"],
            temperature=instance["temperature"],
            top_k=instance["top_k"],
        )
    output = rest[0][length:]
    blog = tokenizer.decode(output, skip_special_tokens=True)
    # print the results
    print("Suggested tweet content:\n")
    print("Generated with psmathur/orca_mini_3b\n")
    print("-------")
    print(blog)
    print("-------")
    # return the json with the results
    return {"blogpost": blog}
```
I have to say that for this part I was helped by the amazing team of Beam: you drop a message on Slack or on the [online Chat section](https://www.beam.cloud/dashboard/apps#:~:text=Slack-,Help,-Onboarding), and they reply to you immediately!
the help page on Beam
So here with the decorator `@`we define the API endpoint and tell Beam to load the tokenizer and the model. In the Inputs there is also our instruction `myprompt = inputs["myprompt"]`that we have to send from the Streamlit app (we will see it later on).
After that I did not make use of any special knowledge. I simply copy and paste from the official HuggingFace model card the code for the text generation.
We finally return the text generated by the AI into the variable blog `blog = tokenizer.decode(output, skip_special_tokens=True)` after creating a json object `return {"blogpost": blog}`. NOTE: that this is really important: we need a structured output so that our POST/GET messages can be recognized in the Streamlit app.
Since we return only text, there is no need for special encoding: you will see that for the images is really another thing!
The app is ready on our pc, now is time to push it into Beam. Save the python file, go in the terminal and run:
```Plain
beam deploy tweet.py -p "default"
```
you should see something like this…
Your default browser will open on a new tab with the app: clicking on the API button will reveal you the code and parameters required for calling the app.
Select the python TAB to get the details for HTTP request.
![[0s-lASOMoSt80BJry.png]]
# TweetDreams
If running a 15 GB model was already amazing, imagine using Stable Diffusion! I am not interested in this Use Case Project to explore all the tweaks of Image generation: here we will see how to generate an image starting from an existing picture with a prompt guidance. Here below is an example:
Generated with Twitter Story Creator App — created by the author
![[01TbYpAUy-27fOOCM.png]]
Open a new python file, called [app.py](http://app.py/), here we are going to prepare our Stable Diffusion inference API. The app will have to accept some inputs: the prompt, the original image, a guidance scale and a strength (both of the last ones are parameters for the image2image inference).

> The Stable Diffusion model can also be applied to image-to-image generation by passing a text prompt and an initial image to condition the generation of new images.
- **strength** (`float`, _optional_, defaults to 0.8) — Indicates extent to transform the reference `image`. Must be between 0 and 1. `image` is used as a starting point and more noise is added the higher the `strength`. The number of denoising steps depends on the amount of noise initially added. When `strength` is 1, added noise is maximum and the denoising process runs for the full number of iterations specified in `num_inference_steps`. A value of 1 essentially ignores `image`.
- **guidance_scale** (`float`, _optional_, defaults to 7.5) — A higher guidance scale value encourages the model to generate images closely linked to the text `prompt` at the expense of lower image quality. Guidance scale is enabled when `guidance_scale > 1`.
You can find more about the Diffusers Pipelines here: [https://huggingface.co/docs/diffusers/v0.20.0/en/api/pipelines/stable_diffusion/img2img#imagetoimage](https://huggingface.co/docs/diffusers/v0.20.0/en/api/pipelines/stable_diffusion/img2img#imagetoimage)
The opening of the Beam app is the same as in the previous section: we import the required libraries, we define the app hardware and software and a persistent volume to store the model weights.
```Plain
from beam import App, Runtime, Image, Output, Volume
import os
import torch
from diffusers import StableDiffusionPipeline, StableDiffusionImg2ImgPipeline
from PIL import Image as PImage
from io import BytesIO
import base64
cache_path = "./models"
model_id = "runwayml/stable-diffusion-v1-5"
\#your Hugging face token… optional
yourHFtoken = "hf_xxxxxxx"
os.environ["HUGGINGFACEHUB_API_TOKEN"] = "hf_xxxxxxx"
# The environment your code will run on
app = App(
    name="TweetDreams",
    runtime=Runtime(
        cpu=8,
        memory="32Gi",
        gpu="A10G",
        image=Image(
            python_version="python3.10",
            python_packages=[
                "diffusers[torch]>=0.10",
                "transformers",
                "torch",
                "pillow",
                "accelerate",
                "safetensors",
                "xformers",
            ],
        ),
    ),
    volumes=[Volume(name="models", path="./models")],)
```
Since we are going to work with images we must import PIL (pillow), nut also BytesIO and base64.
**NOTE 1: this is really important: you cannot send binary objects in json format, so you need to transform them into strings!** I will explain the process in a bit.
**NOTE 2**: I spent 2 hours to troubleshoot this part! it is really Critical to import pillow in this way `from PIL import Image as PImage` because Beam has also a module with the same namespace `from beam import App, Runtime, Image…` so to be able to understand who is who we need to assign a different name to the module _PIL.Image_ (I used **Pimage**, not really imaginative, right?).
a futuristic serverless GPU — created by the author and Leonardo.ai
![[0nyiM6UmnE0TYkhTW.png]]
Now it is time for the API endpoint: note that here we are also declaring some outputs, means that the inference is saving for every API call 2 images (the original one and the generated one).
```Plain
@app.rest_api(
    # File to store image outputs
    outputs=[Output(path="output.png"),
             Output(path="orig_image.png"),
             ],
)
def generate_image(origimage, myprompt, stoi, gs):
    prompt = myprompt
    im_binary = origimage.encode('utf-8')
    png_recovered = base64.b64decode(im_binary)
    init_image = PImage.open(BytesIO(png_recovered)).convert('RGB')
    init_image = init_image.resize((768, 512))
    init_image.save("orig_image.png")
    torch.backends.cuda.matmul.allow_tf32 = True
    pipe = StableDiffusionImg2ImgPipeline.from_pretrained(
        model_id,
        revision="fp16",
        torch_dtype=torch.float16,
        cache_dir=cache_path,).to("cuda")
    with torch.inference_mode():
        with torch.autocast("cuda"):
            image = pipe(prompt=prompt, image=init_image, strength=stoi, guidance_scale=gs).images
    print(f"Saved Image: {image[0]}")
    print(myprompt)
    image[0].save("output.png")
    input_image = "output.png"
    with open(input_image, "rb") as image_file:
        encoded_gen_image = base64.b64encode(image_file.read()).decode("utf-8")
    return {"prompt" : myprompt,
            "guidance_scale": gs,
            "strenght_originalimage": stoi,
            "gen_image" : encoded_gen_image}
```
Let’s go step by steps: we start declaring the function for the inference with the mentioned above 4 parameters (this means that we will have to send them to the API later…)
```Plain
def generate_image(origimage, myprompt, stoi, gs):
    prompt = myprompt
    im_binary = origimage.encode('utf-8')
    png_recovered = base64.b64decode(im_binary)
    init_image = PImage.open(BytesIO(png_recovered)).convert('RGB')
    init_image = init_image.resize((768, 512))
    init_image.save("orig_image.png")
```
As you can see we have to do some job before we can pass the image to the diffuser pipeline:
- encode the origimage to utf-8
- decode it back to base64
- open the image as a BytesIO object with PILLOW converting it into RGB format
- resize it to the preferred dimension of the Stable Diffusion model (768×512)
- save it (so that it will be in the outputs of the app on Beam)
You see here in action our namespace tweak: `init_image = PImage.open(BytesIO(png_recovered)).convert('RGB')`: my app was always crushing 🥲.
The inference calls are also copy pasted from the official Hugging Face Card: I am telling you this because we do not need to reinvent the wheel: we just need a little of experimenting and fun.
```Plain
image[0].save("output.png")
    with open(input_image, "rb") as image_file:
        encoded_gen_image = base64.b64encode(image_file.read()).decode("utf-8")
    return {"prompt" : myprompt,
            "guidance_scale": gs,
            "strenght_originalimage": stoi,
            "gen_image" : encoded_gen_image}
```
As you can see yourself there is a double-tweak here. In fact after saving the generate image you have to send it back into a json format, but now you know that you cannot do it… unless we **convert it into a string-like object**.
And here is the magic:
- we open a stream object `with open(input_image, "rb") as image_file:`
- we encode it to base64
- we decode it into utf-8
Now our image can be sent back, and the structured return object is a json containing the original prompt, the guidance scale, the strength and the encoded generated image.
The app is ready on our pc, now is time to push it into Beam. Save the python file, go in the terminal and run
```Plain
beam deploy app.py -p "default"
```
here the result…
Also for this app, your default browser will open on a new tab with the app: clicking on the API button will reveal you the code and parameters required for calling the app.
Select the python TAB to get the details for http request
# And finally Twitter Story Creator App
The only required package for you to install in your local computer is basically Streamlit (version 1.24.0, that is the latest).
from your terminal run `pip install streamlit==1.24.0`
You will need also the requests library: `pip install requests`
Create a new python file and call it [stapp.py](http://stapp.py/)
We start as usual with importing the libraries required: we need also here PIL (but here I can import the Image namespace without any worries…), ByteIO and base64 for the encoding/decoding job on the images. Finally we need json and requests to communicate with the Beam APIs.
```Plain
import streamlit as st
from PIL import Image
import requests
import json
from io import BytesIO
import base64
import os
st.set_page_config(page_title="Your Twitter Story Creatror App",
                   page_icon='📱')
st.header("Turn your Photos into Amazing Twitter Stories")
```
In streamlit the page_config must be the first instruction, then we set the header.
For clarity I am going to define 2 main functions: one for dealing with the text-generation, the other for the image2image generation.
# The TweetGeneration function
This function has only one argument: the user prompt
```Plain
def tweetgenerated(userprompt):
  import requests
  import json
  import requests
  import json
  prompt = userprompt
  url = "https://apps.beam.cloud/mandd"
  payload = {'myprompt': prompt}
  headers = {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate",
    "Authorization": "Basic xxxxxxxxxxxxxxxxxxxxxx=",
    "Connection": "keep-alive",
    "Content-Type": "application/json"
  }
  response = requests.request("POST", url,
    headers=headers,
    data=json.dumps(payload)
  )
  res = response.content
  import textwrap
  json_object = json.loads(res)
  blogpost  = json_object["blogpost"]
  print("Suggested tweet content:\n")
  print(textwrap.fill(blogpost,60))
  print("-------")
  return blogpost
```
As you can see yourself here I did not have to out so much of an effort: the code is almost completely copied from the Beam API hints. If you are lost in the backend of Beam follow me:
Go to the Dashboard, you can find all your Beam apps listed there
Select one of the apps, for example the TweetGenerator one:
Click on the Call API button, on the top right and copy paste the code for python, in JSON format:
Where you must pay attention the most is on the payloads: the **payloads are basically the data we are sending to the Beam app with our API call**.
This means that we have somehow to send our “prompt” to the Orca-mini-3B model so that our LLM can generate the Tweet for us. The instruction is really easy once we know that it must be a json (like a python dictionary):
```Plain
payload = {'myprompt': prompt}
```
What is happening now is that:
- we send a request with a POST
```Plain
response = requests.request("POST", url,
    headers=headers,
    data=json.dumps(payload)
  )
```
- the request will wait for the response, that contains the generated text `res = response.content`
- since the json object is a binary string we decode it `json_object = json.loads(res)`
- we take then only the text from the json dictionary `blogpost = json_object["blogpost"]` and return it from the function
# The Image Generation function
I called this function callAPI. It has 4 arguments (as expected by our App running on demand on Beam…): the image path (not an image object but the local path of the image — I didn’t know how to do it directly with the PIL object…), the prompt for the generation, the strength and the guidance scale.
```Plain
def callAPI(i,p,stoi,gs):
  """
  i = image path, string
  p = prompt, string
  stoi = strenght of the original image, float
  gs = guidance scale of the prompt, float
  return PILLOW IMAGE
  """
  # SETUP INIT_IMAGE AND PROMPT
  prompt = p
  # encoded_image is the stream for  the API POST
  input_image = i
  with open(input_image, "rb") as image_file:
    encoded_image = base64.b64encode(image_file.read()).decode("utf-8")
  # API CALL
  url = "https://apps.beam.cloud/h7qs7"
  payload = {'origimage': encoded_image,
            'myprompt': prompt,
            'stoi' : stoi,
            'gs' : gs}
  headers = {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate",
    "Authorization": "Basic xxxxxxxxxxxxxxxxxxxxxxxx=",
    "Connection": "keep-alive",
    "Content-Type": "application/json"
  }
  response = requests.request("POST", url,
    headers=headers,
    data=json.dumps(payload))
  # GET THE RESPONSE
  # return the byte stream into a string
  res = response.content.decode('utf-8')
  # convert the string into a json dict
  json_object = json.loads(res)
  rec_image = json_object["gen_image"]
  png_recovered = base64.b64decode(rec_image)
  return png_recovered
```
Same as for the text generation function **we need to prepare the payloads**. For the prompt, guidance scale and strength is really straight forward: for the image now we know the pain…
Similarly to what we did on the Beam app, we have to “transform” the image into a string-like stream.
```Plain
input_image = i
  with open(input_image, "rb") as image_file:
    encoded_image = base64.b64encode(image_file.read()).decode("utf-8")
```
We create the json dictionary with all our keys:
```Plain
payload = {'origimage': encoded_image,
            'myprompt': prompt,
            'stoi' : stoi,
            'gs' : gs}
```
**NOTE: the API setup is similar but you have to take the details from the app you are going to talk to.** In this case is will be the tweetDreams app that has a different url 🤣.
As you can guess now the requests will wait for the response from the Beam app. Also here we will have to do some magic because Stable Diffusion is going to send us a string-like image… 🤬
```Plain
# GET THE RESPONSE
  # return the byte stream into a string
  res = response.content.decode('utf-8')
  # convert the string into a json dict
  json_object = json.loads(res)
  rec_image = json_object["gen_image"]
  png_recovered = base64.b64decode(rec_image)
  return png_recovered
```
- We read the response and decode it into utf-8 format `res = response.content.decode('utf-8')`
- we convert the byte-string into a json object `json_object = json.loads(res)`
- we extract only the image `rec_image = json_object["gen_image"]`
- we decode back the image stream into base64 `png_recovered = base64.b64decode(rec_image)`
snapshot of the Streamlit app running
![[0VmYjsmm7koWQzH_I.png]]