---
Updated: 2023-12-04T13:35
tags:
  - AI->-Theory
URL: https://towardsdatascience.com/decoding-llms-creating-transformer-encoders-and-multi-head-attention-layers-in-python-from-scratch-631429553ce8
Created: 2023-12-04T08:55
---
# Decoding LLMs: Creating Transformer Encoders and Multi-Head Attention Layers in Python from Scratch
## Exploring the intricacies of encoder, multi-head attention, and positional encoding in large language models
![[2K_VENFQPxpVXGGsR05mVfQ.jpeg]]
![[Notion/AI/Decoding LLMs- Creating Transformer Encoders and Multi-Head Attention Layers in Python from Scratch Towards Data Science/attachments/1CJe3891yB1A1mzMdqemkdg.jpeg|1CJe3891yB1A1mzMdqemkdg.jpeg]]
[Luís Roque](https://medium.com/@luisroque?source=post_page-----631429553ce8--------------------------------)
·
[Follow](https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fsubscribe%2Fuser%2F2195f049db86&operation=register&redirect=https%3A%2F%2Ftowardsdatascience.com%2Fdecoding-llms-creating-transformer-encoders-and-multi-head-attention-layers-in-python-from-scratch-631429553ce8&user=Lu%C3%ADs+Roque&userId=2195f049db86&source=post_page-2195f049db86----631429553ce8---------------------post_header-----------)
Published in
[Towards Data Science](https://towardsdatascience.com/?source=post_page-----631429553ce8--------------------------------)
·
12 min read
·
1 day ago
_This post was co-authored with Rafael Nardi._
# Introduction
Today, Computational Natural Language Processing (NLP) is a rapidly evolving endeavour in which the power of computation meets linguistics. The linguistic side of it is mainly attributed to the theory of _Distributive Semantics_ by John Rupert Firth. He once said the following:
_“You shall know a word by the company it keeps”_
So, the semantic representation of a word is determined by the context in which it is being used. It is precisely in attendance to this assumption that the paper “Attention is all you need” by Ashish Vaswani et. al. [[1]](https://github.com/zaai-ai/large-language-models-math/blob/main/attention_is_all_you_need.md\#attention) assumes its groundbreaking relevance. It set the transformer architecture as the core of many of the rapidly growing tools like BERT, GPT4, Llama, etc.
In this article, we examine the key mathematical operations at the heart of the encoder segment in the transformer architecture.
Figure 1: Self-Attention is complex (image by author)
![[1YeXHeZ6NufKf2YbnWDXt5Q.png]]
As always, the code is available on our [GitHub](https://github.com/zaai-ai/large-language-models).
# Tokenization, Embeddings, and Vector Spaces
The first task one has to face while dealing with NLP problems is how to encode the information contained in a sentence so that the machine can handle it. Machines can only work with numbers which means that the words, their meanings, punctuation, etc, must be translated into a numeric representation. This is essentially the problem of embedding.
Before diving into what embeddings are, we need to take an intermediate step and discuss tokenization. Here, the blocks of words or pieces of words are defined as the basic building blocks (so-called tokens) which will lately be represented as numbers. One important note is that we cannot characterize a word or piece of word with a single number and, thus, we use lists of numbers (vectors). It gives us a much bigger representation power.