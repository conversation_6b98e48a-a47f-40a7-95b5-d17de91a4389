---
Updated: 2023-08-12T11:25
tags:
  - AI->-Theory
Created: 2023-08-12T11:25
---
Many new Transformer architecture improvements have been proposed since my last post on [“The Transformer Family”](https://lilianweng.github.io/posts/2020-04-07-the-transformer-family/) about three years ago. Here I did a big refactoring and enrichment of that 2020 post — restructure the hierarchy of sections and improve many sections with more recent papers. Version 2.0 is a superset of the old version, about twice the length.
# Notations
|   |   |
|---|---|
|Symbol|Meaning|
|d|The model size / hidden state dimension / positional encoding size.|
|h|The number of heads in multi-head attention layer.|
|L|The segment length of input sequence.|
|N|The total number of attention layers in the model; not considering MoE.|
|X∈RL×d|The input sequence where each element has been mapped into an embedding vector of shape d, same as the model size.|
|Wk∈Rd×dk|The key weight matrix.|
|Wq∈Rd×dk|The query weight matrix.|
|Wv∈Rd×dv|The value weight matrix. Often we have dk=dv=d.|
|Wik,Wiq∈Rd×dk/h;Wiv∈Rd×dv/h|The weight matrices per head.|
|Wo∈Rdv×d|The output weight matrix.|
|Q=XWq∈RL×dk|The query embedding inputs.|
|K=XWk∈RL×dk|The key embedding inputs.|
|V=XWv∈RL×dv|The value embedding inputs.|
|qi,ki∈Rdk,vi∈Rdv|Row vectors in query, key, value matrices, Q, K and V.|
|Si|A collection of key positions for the i-th query qi to attend to.|
|A∈RL×L|The self-attention matrix between a input sequence of lenght L and itself. A=softmax(QK⊤/dk).|
|aij∈A|The scalar attention score between query qi and key kj.|
|P∈RL×d|position encoding matrix, where the i-th row pi is the positional encoding for input xi.|
# Transformer Basics
The **Transformer** (which will be referred to as “vanilla Transformer” to distinguish it from other enhanced versions; [Vaswani, et al., 2017](https://arxiv.org/abs/1706.03762)) model has an encoder-decoder architecture, as commonly used in many [NMT](https://lilianweng.github.io/posts/2018-06-24-attention/#born-for-translation) models. Later simplified Transformer was shown to achieve great performance in language modeling tasks, like in encoder-only [BERT](https://lilianweng.github.io/posts/2019-01-31-lm/#bert) or decoder-only [GPT](https://lilianweng.github.io/posts/2019-01-31-lm/#openai-gpt).
## Attention and Self-Attention
**Attention** is a mechanism in neural network that a model can learn to make predictions by selectively attending to a given set of data. The amount of attention is quantified by learned weights and thus the output is usually formed as a weighted average.
**Self-attention** is a type of attention mechanism where the model makes prediction for one part of a data sample using other parts of the observation about the same sample. Conceptually, it feels quite similar to [non-local means](https://en.wikipedia.org/wiki/Non-local_means). Also note that self-attention is permutation-invariant; in other words, it is an operation on sets.
There are various forms of attention / self-attention, Transformer ([Vaswani et al., 2017](https://arxiv.org/abs/1706.03762)) relies on the _scaled dot-product attention_: given a query matrix Q, a key matrix K and a value matrix V, the output is a weighted sum of the value vectors, where the weight assigned to each value slot is determined by the dot-product of the query with the corresponding key:
attn(Q,K,V)=softmax(QK⊤dk)V
And for a query and a key vector qi,kj∈Rd (row vectors in query and key matrices), we have a scalar score:
aij=softmax(qikj⊤dk)=exp(qikj⊤)dk∑r∈Siexp(qikr⊤)
where Si is a collection of key positions for the i-th query to attend to.
See my old [post for other types of attention](https://lilianweng.github.io/posts/2018-06-24-attention/#a-family-of-attention-mechanisms) if interested.
## Multi-Head Self-Attention
The **multi-head self-attention** module is a key component in Transformer. Rather than only computing the attention once, the multi-head mechanism splits the inputs into smaller chunks and then computes the scaled dot-product attention over each subspace in parallel. The independent attention outputs are simply concatenated and linearly transformed into expected dimensions.
MultiHeadAttn(Xq,Xk,Xv)=[head1;…;headh]Wowhere headi=Attention(XqWiq,XkWik,XvWiv)
where [.;.] is a concatenation operation. Wiq,Wik∈Rd×dk/h,Wiv∈Rd×dv/h are weight matrices to map input embeddings of size L×d into query, key and value matrices. And Wo∈Rdv×d is the output linear transformation. All the weights should be learned during training.
![[multi-head-attention.png]]
Fig. 1. Illustration of the multi-head scaled dot-product attention mechanism. (Image source: Figure 2 in Vaswani, et al., 2017)
## Encoder-Decoder Architecture
The **encoder** generates an attention-based representation with capability to locate a specific piece of information from a large context. It consists of a stack of 6 identity modules, each containing two submodules, a _multi-head self-attention_ layer and a _point-wise_ fully connected feed-forward network. By point-wise, it means that it applies the same linear transformation (with same weights) to each element in the sequence. This can also be viewed as a convolutional layer with filter size 1. Each submodule has a residual connection and layer normalization. All the submodules output data of the same dimension d.
The function of Transformer **decoder** is to retrieve information from the encoded representation. The architecture is quite similar to the encoder, except that the decoder contains two multi-head attention submodules instead of one in each identical repeating module. The first multi-head attention submodule is _masked_ to prevent positions from attending to the future.
![[transformer.png]]
Fig. 2. The architecture of the vanilla Transformer model. (Image source: Figure 17)
## Positional Encoding
Because self-attention operation is permutation invariant, it is important to use proper **positional encoding** to provide _order information_ to the model. The positional encoding P∈RL×d has the same dimension as the input embedding, so it can be added on the input directly. The vanilla Transformer considered two types of encodings:
### Sinusoidal Positional Encoding
Sinusoidal positional encoding is defined as follows, given the token position i=1,…,L and the dimension δ=1,…,d:
PE(i,δ)={sin(i100002δ′/d)if δ=2δ′cos(i100002δ′/d)if δ=2δ′+1
In this way each dimension of the positional encoding corresponds to a sinusoid of different wavelengths in different dimensions, from 2π to 10000⋅2π.
![[sinoidual-positional-encoding.png]]
Fig. 3. Sinusoidal positional encoding with L=32 and d=128. The value is between -1 (black) and 1 (white) and the value 0 is in gray.
### Learned Positional Encoding
Learned positional encoding assigns each element with a _learned_ column vector which encodes its absolute position ([Gehring, et al. 2017](https://arxiv.org/abs/1705.03122)) and furthermroe this encoding can be learned differently per layer ([Al-Rfou et al. 2018](https://arxiv.org/abs/1808.04444)).
### Relative Position Encoding
[Shaw et al. (2018)](https://arxiv.org/abs/1803.02155)) incorporated relative positional information into Wk and Wv. Maximum relative position is clipped to a maximum absolute value of k and this clipping operation enables the model to generalize to unseen sequence lengths. Therefore, 2k+1 unique edge labels are considered and let us denote Pk,Pv∈R2k+1 as learnable relative position representations.
Aijk=Pclip(j−i,k)kAijv=Pclip(j−i,k)vwhere clip(x,k)=clip(x,−k,k)
[Transformer-XL](https://lilianweng.github.io/posts/2023-01-27-the-transformer-family-v2/#transformer-xl) ([Dai et al., 2019](https://arxiv.org/abs/1901.02860)) proposed a type of relative positional encoding based on reparametrization of dot-product of keys and queries. To keep the positional information flow coherently across segments, Transformer-XL encodes the _relative_ position instead, as it could be sufficient enough to know the position offset for making good predictions, i.e. i−j, between one key vector kτ,j and its query qτ,i.
If omitting the scalar 1/dk and the normalizing term in softmax but including positional encodings, we can write the attention score between query at position i and key at position j as:
aij=qikj⊤=(xi+pi)Wq((xj+pj)Wk)⊤=xiWqWk⊤xj⊤+xiWqWk⊤pj⊤+piWqWk⊤xj⊤+piWqWk⊤pj⊤
Transformer-XL reparameterizes the above four terms as follows:
aijrel=xiWqWEk⊤xj⊤⏟content-based addressing+xiWqWRk⊤ri−j⊤⏟content-dependent positional bias+uWEk⊤xj⊤⏟global content bias+vWRk⊤ri−j⊤⏟global positional bias
- Replace pj with relative positional encoding ri−j∈Rd;
- Replace piWq with two trainable parameters u (for content) and v (for location) in two different terms;
- Split Wk into two matrices, WEk for content information and WRk for location information.
### Rotary Position Embedding
Rotary position embedding (_RoPE_; [Su et al. 2021](https://arxiv.org/abs/2104.09864)) encodes the absolution position with a [rotation matrix](https://en.wikipedia.org/wiki/Rotation_matrix) and multiplies key and value matrices of every attention layer with it to inject relative positional information at every layer.
When encoding relative positional information into the inner product of the i-th key and the j-th query, we would like to formulate the function in a way that the inner product is only about the relative position i−j. Rotary Position Embedding (RoPE) makes use of the rotation operation in Euclidean space and frames the relative position embedding as simply rotating feature matrix by an angle proportional to its position index.
Given a vector z, if we want to rotate it counterclockwise by θ, we can multiply it by a rotation matrix to get Rz where the rotation matrix R is defined as:
R=[cosθ−sinθsinθcosθ]
When generalizing to higher dimensional space, RoPE divide the d-dimensional space into d/2 subspaces and constructs a rotation matrix R of size d×d for token at position i:
RΘ,id=[cosiθ1−siniθ100…00siniθ1cosiθ100…0000cosiθ2−siniθ2…0000siniθ1cosiθ1…00⋮⋮⋮⋮⋱⋮⋮0000…cosiθd/2−siniθd/20000…siniθd/2cosiθd/2]
where in the paper we have Θ=θi=10000−2(i−1)/d,i∈[1,2,…,d/2]. Note that this is essentially equivalent to sinusoidal positional encoding but formulated as a rotation matrix.
Then both key and query matrices incorporates the positional information by multiplying with this rotation matrix:
qi⊤kj=(RΘ,idWqxi)⊤(RΘ,jdWkxj)=xi⊤WqRΘ,j−idWkxj where RΘ,j−id=(RΘ,id)⊤RΘ,jd
![[RoPE.png]]
Fig. 4. Visual illustration of how rotary position embedding is implemented.(Image source: Su et al., 2021)
# Longer Context
The length of an input sequence for transformer models at inference time is upper-bounded by the context length used for training. Naively increasing context length leads to high consumption in both time (O(L2d)) and memory (O(L2)) and may not be supported due to hardware constraints.
This section introduces several improvements in transformer architecture to better support long context at inference; E.g. using additional memory, design for better context extrapolation, or recurrency mechanism.
## Context Memory
The vanilla Transformer has a fixed and limited attention span. The model can only attend to other elements in the same segments during each update step and no information can flow across separated fixed-length segments. This _context segmentation_ causes several issues:
- The model cannot capture very long term dependencies.
- It is hard to predict the first few tokens in each segment given no or thin context.
- The evaluation is expensive. Whenever the segment is shifted to the right by one, the new segment is re-processed from scratch, although there are a lot of overlapped tokens.
**Transformer-XL** ([Dai et al., 2019](https://arxiv.org/abs/1901.02860); “XL” means “extra long”) modifies the architecture to reuse hidden states between segments with an additional memory. The recurrent connection between segments is introduced into the model by continuously using the hidden states from the previous segments.
![[transformer-XL-training.png]]
Fig. 5. A comparison between the training phrase of vanilla Transformer & Transformer-XL with a segment length 4. (Image source: left part of Figure 2 in [Dai et al., 2019](https://arxiv.org/abs/1901.02860)).
Let’s label the hidden state of the n-th layer for the (τ+1)-th segment in the model as hτ+1(n)∈RL×d. In addition to the hidden state of the last layer for the same segment hτ+1(n−1), it also depends on the hidden state of the same layer for the previous segment hτ(n). By incorporating information from the previous hidden states, the model extends the attention span much longer in the past, over multiple segments.
h~τ+1(n−1)=[stop-gradient(hτ(n−1))∘hτ+1(n−1)]Qτ+1(n)=hτ+1(n−1)WqKτ+1(n)=h~τ+1(n−1)WkVτ+1(n)=h~τ+1(n−1)Wvhτ+1(n)=transformer-layer(Qτ+1(n),Kτ+1(n),Vτ+1(n))
Note that both keys and values rely on extended hidden states, while queries only consume hidden states at the current step. The concatenation operation [.∘.] is along the sequence length dimension. And Transformer-XL needs to use [relative positional encoding](https://lilianweng.github.io/posts/2023-01-27-the-transformer-family-v2/#transformer-xl-encoding) because previous and current segments would be assigned with the same encoding if we encode absolute positions, which is undesired.
**Compressive Transformer** ([Rae et al. 2019](https://arxiv.org/abs/1911.05507)) extends Transformer-XL by compressing past memories to support longer sequences. It explicitly adds _memory_ slots of size mm per layer for storing past activations of this layer to preserve long context. When some past activations become old enough, they are compressed and saved in an additional _compressed memory_ of size mcm per layer.
![[compressive-transformer.png]]
Fig. 6. Compressive transformer maintains two types of memory slots, memory and compressed memory, to support long context. (Image source: [Rae et al. 2019](https://arxiv.org/abs/1911.05507)).
Both memory and compressed memory are FIFO queues. Given the model context length L, the compression function of compression rate c is defined as fc:RL×d→R[Lc]×d, mapping L oldest activations to [Lc] compressed memory elements. There are several choices of compression functions:
1. Max/mean pooling of kernel and stride size c;
2. 1D convolution with kernel and stride size c (need to learn additional parameters);
3. Dilated convolution (need to learn additional parameters). In their experiments, convolution compression works out the best on `EnWik8` dataset;
4. Most used memories.
Compressive transformer has two additional training losses:
1. Lac=‖old_mem(i)−g(new_cm(i))‖2 where g:R[Lc]×d→RL×d reverses the compression function f.
    
    **Auto-encoding loss** (lossless compression objective) measures how well we can reconstruct the original memories from compressed memories
    
2. Lar=‖attn(h(i),old_mem(i))−attn(h(i),new_cm(i))‖2
    
    **Attention-reconstruction loss** (lossy objective) reconstructs content-based attention over memory vs compressed memory and minimize the difference:
    
Transformer-XL with a memory of size m has a maximum temporal range of m×N, where N is the number of layers in the model, and attention cost O(L2+Lm). In comparison, compressed transformer has a temporal range of (mm+c⋅mcm)×N and attention cost O(L2+L(mm+mcm)). A larger compression rate c gives better tradeoff between temporal range length and attention cost.
Attention weights, from oldest to newest, are stored in three locations: compressed memory → memory → causally masked sequence. In the experiments, they observed an increase in attention weights from oldest activations stored in the regular memory, to activations stored in the compressed memory, implying that the network is learning to preserve salient information.
![[compressive-transformer-memory.png]]
Fig. 7. Attention weights with one standard deviation as error bars versus memory positions, from oldest (left) to newest (right). (Image source: [Rae et al. 2019](https://arxiv.org/abs/1911.05507)).
## Non-Differentiable External Memory
**kNN-LM** ([Khandelwal et al. 2020](https://arxiv.org/abs/1911.00172)) enhances a pretrained LM with a separate kNN model by linearly interpolating the next token probabilities predicted by both models. The kNN model is built upon an external key-value store which can store any large pre-training dataset or OOD new dataset. This datastore is preprocessed to save a _large_ number of pairs, (LM embedding representation of context, next token) and the nearest neighbor retrieval happens in the LM embedding space. Because the datastore can be gigantic, we need to rely on libraries for fast dense vector search such as [FAISS](https://github.com/facebookresearch/faiss) or [ScaNN](https://github.com/google-research/google-research/tree/master/scann). The indexing process only happens once and parallelism is easy to implement at inference time.
At inference time, the next token probability is a weighted sum of two predictions:
𝟙p(y|x)=λpkNN(y|x)+(1−λ)pLM(y|x)pkNN(y|x)∝∑(ki,wi)∈N1[y=wi]exp(−d(ki,f(x)))
where N contains a set of nearest neighbor data points retrieved by kNN; d(.,.) is a distance function such as L2 distance.
According to the experiments, larger datastore size or larger k is correlated with better perplexity. The weighting scalar λ should be tuned, but in general it is expected to be larger for out-of-domain data compared to in-domain data and larger datastore can afford a larger λ.
**SPALM** (_Adaptive semiparametric language models_; [Yogatama et al. 2021](https://arxiv.org/abs/2102.02557)) incorporates both (1) Transformer-XL style memory for hidden states from external context as short-term memory and (2) kNN-LM style key-value store as long memory.
![[SPALM2.png]]
Fig. 8. Illustration of how SPALM combines context memory of past hidden states (short term memory) with an external key-value datastore (long term memory) to support longer context. (Image source: [Yogatama et al. 2021](https://arxiv.org/abs/2102.02557)).
SPALM runs kNN search to fetch k tokens with most relevant context. For each token we can get the same embedding representation provided by a pretrained LM, denoted as {yi}i=1k. The gating mechanism first aggregates the retrieved token embeddings with a simple attention layer using htR (the hidden state for token xt at layer R) as a query and then learns a gating parameter gt to balance between local information htR and long-term information mt.
mt=∑i=1kexp(yi⊤htR)∑j=1kexp(yj⊤htR)⋅yigt=σ(wg⊤htR)zt=(1−gt)⊙mt+gt⊙htRp(xt+1∣x≤t)=softmax(zt;W)
where wg is a parameter vector to learn; σ(.) is sigmoid; W is the word embedding matrix shared between both input and output tokens. Different from kNN-LM, they didn’t find the nearest neighbor distance to be helpful in the aggregation of retrieved tokens.
During training, the key representations in the long-term memory stay constant, produced by a pretrained LM, but the value encoder, aka the word embedding matrix, gets updated.
**Memorizing Transformer** ([Wu et al. 2022](https://arxiv.org/abs/2203.08913)) adds a kNN-augmented attention layer near the top stack of a decoder-only Transformer. This special layer maintains a Transformer-XL style FIFO cache of past key-value pairs.
The same QKV values are used for both local attention and kNN mechanisms. The kNN lookup returns top-k (key, value) pairs for each query in the input sequence and then they are processed through the self-attention stack to compute a weighted average of retrieved values. Two types of attention are combined with a learnable per-head gating parameter. To prevent large distributional shifts in value magnitude, both keys and values in the cache are normalized.
What they found during experiments with Memorizing Transformer:
- It is observed in some experiments that training models with a small memory and then finetuned with a larger memory works better than training with a large memory from scratch.
- The smaller Memorizing Transformer with just 8k tokens in memory can match the perplexity of a larger vanilla Transformer with 5X more trainable parameters.
- Increasing the size of external memory provided consistent gains up to a size of 262K.
- A non-memory transformer can be finetuned to use memory.
![[memorizing-transformer.png]]
Fig. 9. Fine-tuning a vanilla Transformer with a key-value memory can achieve similar performance as training a memorizing transformer from scratch. (Image source: [Wu et al. 2022](https://arxiv.org/abs/2203.08913)).
## Distance-Enhanced Attention Scores
**Distance Aware Transformer**(**DA-Transformer**; [Wu, et al. 2021](https://aclanthology.org/2021.naacl-main.166)) and **Attention with Linear Biases** (**ALiBi**; [Press et al. 2022](https://arxiv.org/abs/2108.12409)) are motivated by similar ideas — in order to encourage the model to extrapolate over longer context than what the model is trained on, we can explicitly attach the positional information to every pair of attention score based on the distance between key and query tokens.
Note that the default positional encoding in vanilla Transformer only adds positional information to the input sequence, while later improved encoding mechanisms alter attention scores of every layer, such as [rotary position embedding](https://lilianweng.github.io/posts/2023-01-27-the-transformer-family-v2/#rotary-position-embedding), and they take on form very similar to distance enhanced attention scores.
_DA-Transformer_ ([Wu, et al. 2021](https://aclanthology.org/2021.naacl-main.166)) multiplies attention scores at each layer by a learnable bias that is formulated as a function of the distance between key and query. Different attention heads use different parameters to distinguish diverse preferences to short-term vs long-term context. Given two positions, i,j, DA-Transformer uses the following weighting function to alter the self-attention score:
R(i)=αiRwhere Rij=|i−j|f(R(i);βi)=1+exp(βi)1+exp(βi−R(i))attn(Q(i),K(i),V(i))=row-softmax(ReLU(Q(i)K(i)⊤)f(R(i))d)V(i)
where αi is a learnable parameters to weight relative distance differently per head where the head is indexed by superscript (i); βi is a learnable parameter to control the upper bound and ascending slope wrt the distance for the i-th attention head. The weighting function f(.) is designed in a way that: (1) f(0)=1; (2) f(R(i))=0 when R(i)→−∞; (3) f(R(i)) is bounded when R(i)→+∞; (4) the scale is tunable; (5) and the function is monotonic. The extra time complexity brought by f(R(i)) is O(L2) and it is small relative to the self attention time complexity O(L2d). The extra memory consumption is minimal, ~O(2h).
Instead of multipliers, _ALiBi_ ([Press et al. 2022](https://arxiv.org/abs/2108.12409)) adds a constant bias term on query-key attention scores, proportional to pairwise distances. The bias introduces a strong recency preference and penalizes keys that are too far away. The penalties are increased at different rates within different heads. softmax(qiK⊤+αi⋅[0,−1,−2,…,−(i−1)]) where αi is a head-specific weighting scalar. Different from DA-transformer, αi is not learned but fixed as a geometric sequence; for example, for 8 heads, αi=12,122,…,128. The overall idea is very much similar to what relative positional encoding aims to solve.
![[ALiBi-bias.png]]
Fig. 10. Illustration of how ALiBi enhances attention scores with a positional bias term. (Image source: Press et al. 2021).
With ALiBi, [Press et al. (2022)](https://arxiv.org/abs/2108.12409) trained a 1.3B model on context length 1024 during training and extrapolated to 2046 at inference time.
![[ALiBi-exp.png]]
Fig. 11. Extrapolation experiments for running inference with Transformers of different configs, including sinusoidal positional encoding, rotary positional encoding, simplified relative positional encoding in T5 and ALiBi. All models were trained with small context length but inference ran for much longer context. (Image source: [Press et al. 2021](https://arxiv.org/abs/2108.12409)).
## Make it Recurrent
**Universal Transformer** ([Dehghani, et al. 2019](https://arxiv.org/abs/1807.03819)) combines self-attention in Transformer with the recurrent mechanism in RNN, aiming to benefit from both a long-term global receptive field of Transformer and learned inductive biases of RNN. Rather than going through a fixed number of layers, Universal Transformer dynamically adjusts the number of steps using [adaptive computation time](https://lilianweng.github.io/posts/2020-04-07-the-transformer-family/#adaptive-computation-time-act). If we fix the number of steps, an Universal Transformer is equivalent to a multi-layer Transformer with shared parameters across layers.
On a high level, the universal transformer can be viewed as a recurrent function for learning the hidden state representation per token. The recurrent function evolves in parallel across token positions and the information between positions is shared through self-attention.
![[universal-transformer-loop.png]]
Fig. 12. How the Universal Transformer refines a set of hidden state representations repeatedly for every position in parallel. (Image source: Figure 1 in [Dehghani, et al. 2019](https://arxiv.org/abs/1807.03819)).
Given an input sequence of length L, Universal Transformer iteratively updates the representation ht∈RL×d at step t for an adjustable number of steps. At step 0, h0 is initialized to be same as the input embedding matrix. All the positions are processed in parallel in the multi-head self-attention mechanism and then go through a recurrent transition function.
At=LayerNorm(ht−1+MultiHeadAttention(ht−1+Pt)ht=LayerNorm(At−1+Transition(At))
where Transition(.) is either a [separable convolution](https://arxiv.org/abs/1610.02357) or a fully-connected neural network that consists of two position-wise (i.e. applied to each row of At individually) affine transformation + one ReLU.
The positional encoding Pt uses [sinusoidal position signal](https://lilianweng.github.io/posts/2023-01-27-the-transformer-family-v2/#sinusoidal-positional-encoding) but with an additional time dimension:
PE(i,t,δ)={sin(i100002δ′/d)⊕sin(t100002δ′/d)if δ=2δ′cos(i100002δ′/d)⊕cos(t100002δ′/d)if δ=2δ′+1
![[universal-transformer.png]]
Fig. 13. A simplified illustration of Universal Transformer. The encoder and decoder share the same basic recurrent structure. But the decoder also attends to final encoder representation hT. (Image source: Figure 2 in [Dehghani, et al. 2019](https://arxiv.org/abs/1807.03819))
In the adaptive version of Universal Transformer, the number of recurrent steps T is dynamically determined by [ACT](https://lilianweng.github.io/posts/2020-04-07-the-transformer-family/#adaptive-computation-time-act). Each position is equipped with a dynamic ACT halting mechanism. Once a per-token recurrent block halts, it stops taking more recurrent updates but simply copies the current value to the next step until all the blocks halt or until the model reaches a maximum step limit.
# Adaptive Modeling
Adaptive modeling refers to a mechanism that can adjust the amount of computation according to different inputs. For example, some tokens may only need local information and thus demand a shorter attention span; Or some tokens are relatively easier to predict and do not need to be processed through the entire attention stack.
## Adaptive Attention Span
One key advantage of Transformer is the capability of capturing long-term dependencies. Depending on the context, the model may prefer to attend further sometime than others; or one attention head may had different attention pattern from the other. If the attention span could adapt its length flexibly and only attend further back when needed, it would help reduce both computation and memory cost to support longer maximum context size in the model.
This is the motivation for **Adaptive Attention Span**. [Sukhbaatar et al (2019)](https://arxiv.org/abs/1905.07799) proposed a self-attention mechanism that seeks an optimal attention span. They hypothesized that different attention heads might assign scores differently within the same context window (See Fig. 14) and thus the optimal span would be trained separately per head.
![[attention-per-head.png]]
Fig. 14. Two attention heads in the same model, A & B, assign attention differently within the same context window. Head A attends more to the recent tokens, while head B look further back into the past uniformly. (Image source: [Sukhbaatar, et al. 2019](https://arxiv.org/abs/1905.07799))
Given the i-th token, we need to compute the attention weights between this token and other keys within its attention span of size s:
eij=qikj⊤aij=softmax(eij)=exp(eij)∑r=i−si−1exp(eir)yi=∑r=i−si−1airvr=∑r=i−si−1airxrWv
A _soft mask function_ mz is added to control for an effective adjustable attention span, which maps the distance between query and key into a [0, 1] value. mz is parameterized by z∈[0,s] and z is to be learned:
mz(x)=clip(1R(R+z−x),0,1)
where R is a hyper-parameter which defines the softness of mz.
![[soft-masking-function.png]]
Fig. 15. The soft masking function used in the adaptive attention span. (Image source: Sukhbaatar, et al. 2019.)
The soft mask function is applied to the softmax elements in the attention weights:
aij=mz(i−j)exp(sij)∑r=i−si−1mz(i−r)exp(sir)
In the above equation, z is differentiable so it is trained jointly with other parts of the model. Parameters z(i),i=1,…,h are learned _separately per head_. Moreover, the loss function has an extra L1 penalty on ∑i=1hz(i).
Using [Adaptive Computation Time](https://lilianweng.github.io/posts/2020-04-07-the-transformer-family/#adaptive-computation-time-act), the approach can be further enhanced to have flexible attention span length, adaptive to the current input dynamically. The span parameter zt of an attention head at time t is a sigmoidal function, zt=Sσ(v⋅xt+b), where the vector v and the bias scalar b are learned jointly with other parameters.
In the experiments of Transformer with adaptive attention span, [Sukhbaatar, et al. (2019)](https://arxiv.org/abs/1905.07799) found a general tendency that lower layers do not require very long attention spans, while a few attention heads in higher layers may use exceptionally long spans. Adaptive attention span also helps greatly reduce the number of FLOPS, especially in a big model with many attention layers and a large context length.
## Depth-Adaptive Transformer
At inference time, it is natural to assume that some tokens are easier to predict and thus do not require as much computation as others. Therefore we may only process its prediction through a limited number of layers to achieve a good balance between speed and performance.
Both **Depth-Adaptive Transformer** ([Elabyad et al. 2020](https://arxiv.org/abs/1910.10073)) and **Confident Adaptive Language Model** (**CALM**; [Schuster et al. 2022](https://arxiv.org/abs/2207.07061)) are motivated by this idea and learn to predict optimal numbers of layers needed for different input tokens.
_Depth-adaptive transformer_ ([Elabyad et al. 2020](https://arxiv.org/abs/1910.10073)) attaches an output classifier to every layer to produce exit predictions based on activations of that layer. The classifier weight matrices can be different per layer or shared across layers. During training, the model sample different sequences of exits such that the model is optimized with hidden states of different layers. The learning objective incorporates likelihood probabilities predicted at different layers, n=1,…,N:
LLtn=logp(yt|ht−1n)LLn=∑t=1|y|LLtn
Adaptive depth classifiers outputs a parametric distribution qt. It is trained with cross entropy loss against an oracle distribution qt∗. The paper explored three confiurations for how to learn such a classifier qt.
![[depth-adaptive-classifier.png]]
Fig. 16. Illustration of three types of adaptive depth classifiers. (Image source: Elabyad et al. 2020).
1. q(n|x)=softmax(Wnx¯+bn)∈RNqlik∗(x,y)=δ(argmaxnLLn−λn)or qcorr∗(x,y)=δ(argmaxnCn−λn) where Cn=|{t|yt=argmaxyp(y|ht−1n)}|
    
    _Sequence-specific depth classifier_: All tokens of the same sequence share the same exit block. It depends on the average of the encoder representation of the sequence. Given an input sequence x of length L, the classifier takes x¯=1L∑t=1Lxt as input and outputs a multinomial distribution of N dimensions, corresponding to N layers.
    
    where δ is [dirac delta](https://en.wikipedia.org/wiki/Dirac_delta_function) (unit impulse) function and −λn is a regularization term to encourage lower layer exits. The ground truth q∗ can be prepared in two way, based on maximum likelihood qlik∗ or correctness qcorr∗.
    
2. qt(n|x,y<t)=softmax(Wnht1+bn)
    
    _Token-specific depth classifier (multinomial)_: Each token is decoded with different exit block, predicted conditioned on the first decoder hidden state ht1:
    
3. 𝟙Xtn=sigmoid(wn⊤htn+bn)∀n∈[1,…,N−1]qt(n|x,y<t)={Xtn∏n′<n(1−Xtn′)if n<N∏n′<N(1−Xtn′)otherwiseqlik∗(x,y)=δ(argmaxnLL~tn−λn) where LL~tn=∑t′=1|y|κ(t,t′)LLt′nor qcor∗(x,y)=δ(argmaxnC~tn−λn) where Ctn=1[yt=argmaxyp(y|ht−1n)],C~tn=∑t′=1|y|κ(t,t′)Ct′n
    
    _Token-specific depth classifier (geometric-like)_: A binary exit prediction distribution is made per layer per token, Xtn. The RBF kernel κ(t,t′)=exp(|t−t′|2σ) is used to smooth the predictions to incorporate the impact of current decision on future time steps.
    
At inference time, the confidence threshold for making an exit decision needs to be calibrated. Depth-adaptive transformer finds such a threshold on a validation set via grid search. _CALM_ ([Schuster et al. 2022](https://arxiv.org/abs/2207.07061)) applied the Learn then Test (LTT) framework ([Angelopoulos et al. 2021](https://arxiv.org/abs/2110.01052)) to identify a subset of valid thresholds and chose the minimum value as the threshold for inference. Except for training per-layer exit classifier, CALM also explored other methods for adaptive depth prediction, including the softmax responses (i.e. difference between top two softmax outputs) and hidden state saturation (i.e. cos(htn,htn+1)) as confidence scores for exit decisions. They found softmax responses result in best inference speedup.
# Efficient Attention
The computation and memory cost of the vanilla Transformer grows quadratically with sequence length and hence it is hard to be applied on very long sequences. Many efficiency improvements for Transformer architecture have something to do with the self-attention module - making it cheaper, smaller or faster to run. See the survey paper on _Efficient Transformers_ ([Tay et al. 2020](https://arxiv.org/abs/2009.06732)).
## Sparse Attention Patterns
### Fixed Local Context
A simple alternation to make self-attention less expensive is to restrict the attention span of each token to **local** context only, so that self-attention grows linearly with the sequence length.
The idea was introduced by **Image Transformer** ([Parmer, et al 2018](https://arxiv.org/abs/1802.05751)), which formulates image generation as sequence modeling using an encoder-decoder transformer architecture:
- The encoder generates a contextualized, per-pixel-channel representation of the source image;
- Then the decoder autoregressively generates an output image, one channel per pixel at each time step.
Let’s label the representation of the current pixel to be generated as the query q. Other positions whose representations will be used for computing q are key vector k1,k2,… and they together form a memory matrix M. The scope of M defines the context window for pixel query q.
Image Transformer introduced two types of localized M, as illustrated below.
![[image-transformer-attention.png]]
Fig. 17. Illustration of 1D and 2D attention span for visual inputs in Image Transformer. The black line marks a query block and the cyan outlines the actual attention span for pixel q. (Image source: Figure 2 in
[Parmer et al, 2018](https://arxiv.org/abs/1802.05751))
1. _1D Local Attention_: The input image is flattened in the [raster scanning](https://en.wikipedia.org/wiki/Raster_scan#Scanning_pattern) order, that is, from left to right and top to bottom. The linearized image is then partitioned into non-overlapping query blocks. The context window consists of pixels in the same query block as q and a fixed number of additional pixels generated before this query block.
    
2. _2D Local Attention_: The image is partitioned into multiple non-overlapping rectangular query blocks. The query pixel can attend to all others in the same memory blocks. To make sure the pixel at the top-left corner can also have a valid context window, the memory block is extended to the top, left and right by a fixed amount, respectively.
    
### Strided Context
**Sparse Transformer** ([Child et al., 2019](https://arxiv.org/abs/1904.10509)) introduced _factorized self-attention_, through sparse matrix factorization, making it possible to train dense attention networks with hundreds of layers on sequence length up to 16,384, which would be infeasible on modern hardware otherwise.
Given a set of attention connectivity pattern S={S1,…,Sn}, where each Si records a set of key positions that the i-th query vector attends to.
Attend(X,S)=(a(xi,Si))i∈{1,…,L} where a(xi,Si)=softmax((xiWq)(xjWk)j∈Si⊤dk)(xjWv)j∈Si
Note that although the size of Si is not fixed, a(xi,Si) is always of size dv and thus Attend(X,S)∈RL×dv.
In anto-regressive models, one attention span is defined as Si={j:j≤i} as it allows each token to attend to all the positions in the past.
In factorized self-attention, the set Si is decomposed into a _tree_ of dependencies, such that for every pair of (i,j) where j≤i, there is a path connecting i back to j and i can attend to j either directly or indirectly.
Precisely, the set Si is divided into p _non-overlapping_ subsets, where the m-th subset is denoted as Ai(m)⊂Si,m=1,…,p. Therefore the path between the output position i and any j has a maximum length p+1. For example, if (j,a,b,c,…,i) is a path of indices between i and j, we would have j∈Aa(1),a∈Ab(2),b∈Ac(3),…, so on and so forth.
**Sparse Factorized Attention**
Sparse Transformer proposed two types of fractorized attention. It is easier to understand the concepts as illustrated in Fig. 10 with 2D image inputs as examples.
![[sparse-attention.png]]
Fig. 18. The top row illustrates the attention connectivity patterns in (a) Transformer, (b) Sparse Transformer with strided attention, and (c) Sparse Transformer with fixed attention. The bottom row contains corresponding self-attention connectivity matrices. Note that the top and bottom rows are not in the same scale. (Image source: [Child et al., 2019](https://arxiv.org/abs/1904.10509) + a few of extra annotations.)
1. Ai(1)={t,t+1,…,i}, where t=max(0,i−ℓ)Ai(2)={j:(i−j)modℓ=0}
    
    _Strided_ attention with stride ℓ∼n. This works well with image data as the structure is aligned with strides. In the image case, each pixel would attend to all the previous ℓ pixels in the raster scanning order (naturally cover the entire width of the image) and then those pixels attend to others in the same column (defined by another attention connectivity subset).
    
2. Ai(1)={j:⌊jℓ⌋=⌊iℓ⌋}Ai(2)={j:jmodℓ∈{ℓ−c,…,ℓ−1}}
    
    _Fixed_ attention. A small set of tokens summarize previous locations and propagate that information to all future locations.
    
    where c is a hyperparameter. If c=1, it restricts the representation whereas many depend on a few positions. The paper chose c∈{8,16,32} for ℓ∈{128,256}.
    
**Use Factorized Self-Attention in Transformer**
There are three ways to use sparse factorized attention patterns in Transformer architecture:
1. One attention type per residual block and then interleave them, attn(X)=Attend(X,A(nmodp))Wo, where n is the index of the current residual block.
2. Set up a single head which attends to locations that all the factorized heads attend to, attn(X)=Attend(X,∪m=1pA(m))Wo.
3. Use a multi-head attention mechanism, but different from vanilla Transformer, each head might adopt a pattern presented above, 1 or 2. → This option often performs the best.
Sparse Transformer also proposed a set of changes so as to train the Transformer up to hundreds of layers, including gradient checkpointing, recomputing attention & FF layers during the backward pass, mixed precision training, efficient block-sparse implementation, etc. Please check the [paper](https://arxiv.org/abs/1904.10509) for more details or my previous post on [techniques for scaling up model training](https://lilianweng.github.io/posts/2021-09-25-train-large/).
**Blockwise Attention** ([Qiu et al. 2019](https://arxiv.org/abs/1911.02972)) introduces a _sparse block matrix_ to only allow each token to attend to a small set of other tokens. Each attention matrix of size L×L is partitioned into n×n smaller blocks of size Ln×Ln and a sparse block matrix M∈{0,1}L×L is defined by a permutation π of 1,…,n, which records the column index per row in the block matrix.
attn(Q,K,V,M)=softmax(QK⊤d⊙M)V(A⊙M)ij={Aijif Mij=1−∞if Mij=0where Mij={1if π(⌊(i−1)nL+1⌋)=⌊(j−1)nL+1⌋0otherwise
The actual implementation of Blockwise Attention only stores QKV as block matrices, each of size n×n:
Blockwise-attn(Q,K,V,M)=[softmax(q^1k^π(1)⊤d)v^π(1)⋮softmax(q^nk^π(n)⊤d⊙)v^π(n)]
where q^i, k^i and v^i are the i-the row in the QKV block matrix respectively. Each qikπ(i)⊤,∀i=1,…,n is of size Nn×Nn and therefore Blockwise Attention is able to reduce the memory complexity of attention matrix from O(L2) to O(Ln×Ln×n)=O(L2/n).
### Combination of Local and Global Context
**ETC** (_Extended Transformer Construction_; [Ainslie et al. 2019](https://aclanthology.org/2020.emnlp-main.19/)), **Longformer** ([Beltagy et al. 2020](https://arxiv.org/abs/2004/05150)) and **Big Bird** ([Zaheer et al. 2020](https://arxiv.org/abs/2007.14062)) models combine both local and global context when building an attention matrix. All these models can be initialized from existing pretrained models.
**Global-Local Attention** of _ETC_ ([Ainslie et al. 2019](https://aclanthology.org/2020.emnlp-main.19/)) takes two inputs, (1) the long input xl of size nl which is the regular input sequence and (2) the global input xg of size ng which contains a smaller number of auxiliary tokens, ng≪nl. Attention is thus split into four components based on directional attention across these two inputs: g2g, g2l, l2g and l2l. Because the l2l attention piece can be very large, it is restricted to a fixed size attention span of radius w (i.e. local attention span) and the l2l matrix can be reshaped to nl×(2w+1).
ETC utilizes four binary matrices to handle structured inputs, Mg2g, Mg2l, Ml2g and Ml2l. For example, each element zig∈Rd in the attention output zg=(z1g,…,zngg) for g2g attention piece is formatted as:
aijg2g=1dxigWQ(xjgWK+PijK)⊤−(1−Mijg2g)CAijg2g=exp(aijg2g)∑k=1ngexp(aikg2g)zig=∑j=1ngAijg2gxjgWV
where PijK is a learnable vector for relative position encoding and C is a very large constant (C=10000 in the paper) to offset any attention weights when mask is off.
![[combined-attention.png]]
Fig. 19. Attention patterns of ETC, Longformer and Big Bird.
One more update in ETC is to incorporate a CPC (contrastive predictive coding) task using [NCE loss](https://lilianweng.github.io/posts/2021-05-31-contrastive/#nce) into the pretraining stage, besides the [MLM](https://lilianweng.github.io/posts/2019-01-31-lm/#MLM) task: The representation of one sentence should be similar to the representation of context around it when this sentence is masked.
The global input xg for ETC is constructed as follows: Assuming there are some segments within the long inputs (e.g. by sentence), each segment is attached with one auxiliary token to learn global inputs. [Relative position encoding](https://lilianweng.github.io/posts/2023-01-27-the-transformer-family-v2/#relative-position-encoding) is used to mark the global segment tokens with the token position. Hard masking in one direction (i.e., tokens before vs after are labeled differently) is found to bring performance gains in some datasets.
Attention pattern in Longformer contains three components:
1. _Local attention_: Similar to ETC, local attention is controlled by a sliding window of fixed size w;
2. _Global attention of preselected tokens_: Longformer has a few pre-selected tokens (e.g. `[CLS]` token) assigned with global attention span, that is, attending to all other tokens in the input sequence.
3. _Dilated attention_: Dilated sliding window of fixed size r and gaps of dilation size d, similar to Sparse Transformer;
_Big Bird_ is quite similar to Longformer, equipped with both local attention and a few preselected tokens with global attention span, but Big Bird replaces dilated attention with a new mechanism where all tokens attend to a set of random tokens. The design is motivated by the fact that attention pattern can be viewed as a [directed graph](https://en.wikipedia.org/wiki/Directed_graph) and a [random graph](https://en.wikipedia.org/wiki/Random_graph) has the property that information is able to rapidly flow between any pair of nodes.
_Longformer_ uses smaller window size at lower layers and larger window sizes at higher layers. Ablation studies showed that this setup works better than reversed or fixed size config. Lower layers do not have dilated sliding windows to better learn to use immediate local context. Longformer also has a staged training procedure where initially the model is trained with small window size to learn from local context and then subsequent stages of training have window sizes increased and learning rate decreased.
## Content-based Attention
The improvements proposed by **Reformer** ([Kitaev, et al. 2020](https://arxiv.org/abs/2001.04451)) aim to solve the following pain points in vanilla Transformer:
- Quadratic time and memory complexity within self-attention module.
- Memory in a model with N layers is N-times larger than in a single-layer model because we need to store activations for back-propagation.
- The intermediate FF layers are often quite large.
Reformer proposed two main changes:
1. Replace the dot-product attention with _locality-sensitive hashing (LSH) attention_, reducing the complexity from O(L2) to O(LlogL).
2. Replace the standard residual blocks with _reversible residual layers_, which allows storing activations only once during training instead of N times (i.e. proportional to the number of layers).
**Locality-Sensitive Hashing Attention**
In QK⊤ part of the [attention formula](https://lilianweng.github.io/posts/2023-01-27-the-transformer-family-v2/#attention-and-self-attention), we are only interested in the largest elements as only large elements contribute a lot after softmax. For each query qi∈Q, we are looking for row vectors in K closest to qi. In order to find nearest neighbors quickly in high-dimensional space, Reformer incorporates [Locality-Sensitive Hashing (LSH)](https://en.wikipedia.org/wiki/Locality-sensitive_hashing) into its attention mechanism.
A hashing scheme x↦h(x) is _locality-sensitive_ if it preserves the distancing information between data points, such that close vectors obtain similar hashes while distant vectors have very different ones. The Reformer adopts a hashing scheme as such, given a fixed random matrix R∈Rd×b/2 (where b is a hyperparam), the hash function is h(x)=argmax([xR;−xR]).
![[LSH-attention-matrix.png]]
Fig. 20. Illustration of Locality-Sensitive Hashing (LSH) attention. (Image source: right part of Figure 1 in Kitaev, et al. 2020).
In LSH attention, a query can only attend to positions in the same hashing bucket, Si={j:h(qi)=h(kj)}. It is carried out in the following process, as illustrated in Fig. 20:
- (a) The attention matrix for full attention is often sparse.
- (b) Using LSH, we can sort the keys and queries to be aligned according to their hash buckets.
- (c) Set Q=K (precisely kj=qj/|qj|), so that there are equal numbers of keys and queries in one bucket, easier for batching. Interestingly, this “shared-QK” config does not affect the performance of the Transformer.
- (d) Apply batching where chunks of m consecutive queries are grouped together.
![[LSH-attention.png]]
Fig. 21. The LSH attention consists of 4 steps: bucketing, sorting, chunking, and attention computation. (Image source: left part of Figure 1 in [Kitaev, et al. 2020](https://arxiv.org/abs/2001.04451)).
**Reversible Residual Network**
Another improvement by Reformer is to use _reversible residual layers_ ([Gomez et al. 2017](https://arxiv.org/abs/1707.04585)). The motivation for reversible residual network is to design the architecture in a way that activations at any given layer can be recovered from the activations at the following layer, using only the model parameters. Hence, we can save memory by recomputing the activation during backprop rather than storing all the activations.
Given a layer x↦y, the normal residual layer does y=x+F(x), but the reversible layer splits both input and output into pairs (x1,x2)↦(y1,y2) and then executes the following:
y1=x1+F(x2),y2=x2+G(y1)
and reversing is easy:
x2=y2−G(y1),x1=y1−F(x2)
Reformer applies the same idea to Transformer by combination attention (F) and feed-forward layers (G) within a reversible net block:
Y1=X1+Attention(X2),Y2=X2+FeedForward(Y1)
The memory can be further reduced by chunking the feed-forward computation:
Y2=[Y2(1);…;Y2(c)]=[X2(1)+FeedForward(Y1(1));…;X2(c)+FeedForward(Y1(c))]
The resulting reversible Transformer does not need to store activation in every layer.
**Routing Transformer** ([Roy et al. 2021](https://arxiv.org/abs/2003.05997)) is also built on content-based clustering of keys and queries. Instead of using a static hashing function like LSH, it utilizes online k-means clustering and combines it with local, temporal sparse attention to reduce the attention complexity from O(L2) to O(L1.5).
Within routing attention, both keys and queries are clustered with k-means clustering method and the same set of centroids μ=(μ1,…,μk)∈Rk×d. Queries are routed to keys that get assigned to the same centroid. The total complexity is O(Lkd+L2d/k), where O(Lkd) is for running clustering assignments and O(L2d/k) is for attention computation. The cluster centroids are updated by EMA (exponential moving average) using all associated keys and queries.
In the experiments for Routing Transformer, some best config only has routing attention enabled in the last two layers of the model and half of the attention heads, while the other half utilizing local attention. They also observed that local attention is a pretty strong baseline and larger attention window always leads to better results.
## Low-Rank Attention
**Linformer** ([Wang et al. 2020](https://arxiv.org/abs/2006.04768)) approximates the full attention matrix with a _low rank_ matrix, reducing the time & space complexity to be _linear_. Instead of using expensive SVD to identify low rank decomposition, Linformer adds two linear projections Ei,Fi∈RL×k for key and value matrices, respectively, reducing their dimensions from L×d to k×d. As long as k≪L, the attention memory can be greatly reduced.
head―i=attn(XqWiq,EiXkWik,FiXvWiv)=softmax(XqWiq(EiXkWik)⊤d)⏟low rank attention matrix A¯∈Rk×dFiXvWiv
Additional techniques can be applied to further improve efficiency of Linformer:
- Parameter sharing between projection layers, such as head-wise, key-value and layer-wise (across all layers) sharing.
- Use different k at different layers, as heads in higher layers tend to have a more skewed distribution (lower rank) and thus we can use smaller k at higher layers.
- Use different types of projections; e.g. mean/max pooling, convolution layer with kernel and stride L/k.
![[linformer.png]]
Fig. 22. (Left) Informer has two projection layers added for keys and values. (Right) Plot of inference time as a function of sequence length. (Image source: [Wang et al. 2020](https://arxiv.org/abs/2006.04768)).
**Random Feature Attention** (**RFA**; [Peng et al. 2021](https://arxiv.org/abs/2103.02143)) relies on _random feature methods_ ([Rahimi & Recht, 2007](https://people.eecs.berkeley.edu/~brecht/papers/07.rah.rec.nips.pdf)) to approximate softmax operation in self-attention with low rank feature maps in order to achieve linear time and space complexity. **Performers** ([Choromanski et al. 2021](https://arxiv.org/abs/2009.14794)) also adopts random feature attention with improvements on the kernel construction to further reduce the kernel approximation error.
The main theorem behind RFA is from [Rahimi & Recht, 2007](https://people.eecs.berkeley.edu/~brecht/papers/07.rah.rec.nips.pdf):

> Let ϕ:Rd→R2D be a nonlinear transformation:
An unbiased estimation of exp(x⋅y) is:
exp(x⋅y/σ2)=exp(12σ2(‖x‖2+‖y‖2−‖x−y‖2)=exp(‖x‖22σ2)exp(‖y‖22σ2)(−‖x−y‖22σ2)≈exp(‖x‖22σ2)exp(‖y‖22σ2)ϕ(x)⋅ϕ(y)=exp(1σ2)ϕ(x)⋅ϕ(y); unit vectors
Then we can write the attention function as follows, where ⊗ is outer product operation and σ2 is the temperature:
attn(qt,{ki},{vi})=∑iexp(qt⋅ki/σ2)∑jexp(qt⋅kj/σ2)vi⊤≈∑iϕ(qt)ϕ(ki)vi⊤∑jϕ(qt)ϕ(kj)=ϕ(qt)⊤∑iϕ(ki)⊗viϕ(qt)⊤∑jϕ(kj)=RFA(qt,{ki},{vi})
![[RFA.png]]
Fig. 23. (Left) The order of computation for default softmax operation. (Right) The order of computation when using random feature attention, a lot cheaper than default softmax. (Image source: [Peng et al. 2021](https://arxiv.org/abs/2103.02143)).
**Causal Attention RFA** has token at time step t only attend to earlier keys and values {ki}i≤t,{vi}i≤t. Let us use a tuple of variables, (St∈R2D×d,z∈R2D), to track the hidden state history at time step t, similar to RNNs:
causal-RFA(qt,{ki}i≤t,{vi}i≤t)=ϕ(qt)⊤Stϕ(qt)⋅ztwhere St=St−1+ϕ(kt)⊗vt,zt=zt−1+ϕ(kt)
where 2D is the size of ϕ(.) and D should be no less than the model size d for reasonable approximation.
RFA leads to significant speedup in autoregressive decoding and the memory complexity mainly depends on the choice of D when constructing the kernel ϕ(.).
Performer modifies the random feature attention with positive random feature maps to reduce the estimation error. It also keeps the randomly sampled w1,…,wD to be orthogonal to further reduce the variance of the estimator.
![[performer.png]]
Fig. 24. Comparison of approximation error when using (Left) i.i.d vs orthogonal features and (Right) sin/cos vs positive random features. (Image source: [Choromanski et al. 2021](https://arxiv.org/abs/2009.14794)).
# Transformers for Reinforcement Learning
The self-attention mechanism avoids compressing the whole past into a fixed-size hidden state and does not suffer from vanishing or exploding gradients as much as RNNs. Reinforcement Learning tasks can for sure benefit from these traits. _However_, it is quite difficult to train Transformer even in supervised learning, let alone in the RL context. It could be quite challenging to stabilize and train a LSTM agent by itself, after all.
The **Gated Transformer-XL** (**GTrXL**; [Parisotto, et al. 2019](https://arxiv.org/abs/1910.06764)) is one attempt to use Transformer for RL. GTrXL succeeded in stabilizing training with two changes on top of [Transformer-XL](https://lilianweng.github.io/posts/2023-01-27-the-transformer-family-v2/#longer-attention-span-transformer-xl):
1. The layer normalization is only applied on the input stream in a residual module, but NOT on the shortcut stream. A key benefit to this reordering is to allow the original input to flow from the first to last layer.
2. The residual connection is replaced with a GRU-style (Gated Recurrent Unit; [Chung et al., 2014](https://arxiv.org/abs/1412.3555)) _gating_ mechanism.
r=σ(Wr(l)y+Ur(l)x)z=σ(Wz(l)y+Uz(l)x−bg(l))h^=tanh(Wg(l)y+Ug(l)(r⊙x))g(l)(x,y)=(1−z)⊙x+z⊙h^
The gating function parameters are explicitly initialized to be close to an identity map - this is why there is a bg term. A bg>0 greatly helps with the learning speedup.
![[gated-transformer-XL.png]]
Fig. 25. Comparison of the model architecture of Transformer-XL, Transformer-XL with the layer norm reordered, and Gated Transformer-XL. (Image source: Figure 1 in
[Parisotto, et al. 2019](https://arxiv.org/abs/1910.06764))
**Decision Transformer** (**DT**; [Chen et al 2021](https://arxiv.org/abs/2106.01345)) formulates Reinforcement Learning problems as a process of _conditional sequence modeling_, outputting the optimal actions conditioned on the desired return, past states and actions. It therefore becomes straightforward to use Transformer architecture. Decision Transformer is for [off-policy RL](https://lilianweng.github.io/posts/2018-02-19-rl-overview/#key-concepts), where the model only has access to a fixed collection of trajectories collected by other policies.
To encourage the model to learn how to act in order to achieve a desired return, it feeds the model with desired future return R^=∑t′=tTrt′ instead of the current reward. The trajectory consists of a list of triplets, (return-to-go R^t,states_t,actiona_t$), and it is used as an input sequence for Transformer:
τ=(R^1,s1,a1,R^2,s2,a2,…,R^T,sT,aT)
Three linear layers are added and trained for return-to-go, state and action respectively to extract token embeddings. The prediction head learns to predict at corresponding to the input token st. The training uses cross-entropy loss for discrete actions or MSE for continuous actions. Predicting the states or return-to-go was not found to help improve the performance in their experiments.
The experiments compared DT with several model-free RL algorithm baselines and showed that:
- DT is more efficient than behavior cloning in low data regime;
- DT can model the distribution of returns very well;
- Having a long context is crucial for obtaining good results;
- DT can work with sparse rewards.
# Citation
Cited as:

> Weng, Lilian. (Jan 2023). The transformer family version 2.0. Lil’Log. https://lilianweng.github.io/posts/2023-01-27-the-transformer-family-v2/.
Or
```Plain
@article{weng2023transformer,
  title   = "The Transformer Family Version 2.0",
  author  = "Weng, Lilian",
  journal = "lilianweng.github.io",
  year    = "2023",
  month   = "Jan",
  url     = "https://lilianweng.github.io/posts/2023-01-27-the-transformer-family-v2/"
}
```
# References
[1] Ashish Vaswani, et al. [“Attention is all you need."](http://papers.nips.cc/paper/7181-attention-is-all-you-need.pdf) NIPS 2017.
[2] Rami Al-Rfou, et al. [“Character-level language modeling with deeper self-attention."](https://arxiv.org/abs/1808.04444) AAAI 2019.
[3] Olah & Carter, [“Attention and Augmented Recurrent Neural Networks”](http://doi.org/10.23915/disti), Distill, 2016.
[4] Sainbayar Sukhbaatar, et al. [“Adaptive Attention Span in Transformers”](https://arxiv.org/abs/1905.07799). ACL 2019.
[5] Rewon Child, et al. [“Generating Long Sequences with Sparse Transformers”](https://arxiv.org/abs/1904.10509) arXiv:1904.10509 (2019).
[6] Nikita Kitaev, et al. [“Reformer: The Efficient Transformer”](https://arxiv.org/abs/2001.04451) ICLR 2020.
[7] Alex Graves. (“Adaptive Computation Time for Recurrent Neural Networks”)[https://arxiv.org/abs/1603.08983]
[8] Niki Parmar, et al. [“Image Transformer”](https://arxiv.org/abs/1802.05751) ICML 2018.
[9] Zihang Dai, et al. [“Transformer-XL: Attentive Language Models Beyond a Fixed-Length Context."](https://arxiv.org/abs/1901.02860) ACL 2019.
[10] Aidan N. Gomez, et al. [“The Reversible Residual Network: Backpropagation Without Storing Activations”](https://arxiv.org/abs/1707.04585) NIPS 2017.
[11] Mostafa Dehghani, et al. [“Universal Transformers”](https://arxiv.org/abs/1807.03819) ICLR 2019.
[12] Emilio Parisotto, et al. [“Stabilizing Transformers for Reinforcement Learning”](https://arxiv.org/abs/1910.06764) arXiv:1910.06764 (2019).
[13] Rae et al. [“Compressive Transformers for Long-Range Sequence Modelling.”](https://arxiv.org/abs/1911.05507) 2019.
[14] Press et al. [“Train Short, Test Long: Attention With Linear Biases Enables Input Length Extrapolation.”](https://arxiv.org/abs/2108.12409) ICLR 2022.
[15] Wu, et al. [“DA-Transformer: Distance Aware Transformer”](https://aclanthology.org/2021.naacl-main.166) 2021.
[16] Elabyad et al. [“Depth-Adaptive Transformer.”](https://arxiv.org/abs/1910.10073) ICLR 2020.
[17] Schuster et al. [“Confident Adaptive Language Modeling”](https://arxiv.org/abs/2207.07061) 2022.
[18] Qiu et al. [“Blockwise self-attention for long document understanding”](https://arxiv.org/abs/1911.02972) 2019
[19] Roy et al. [“Efficient Content-Based Sparse Attention with Routing Transformers.”](https://arxiv.org/abs/2003.05997) 2021.
[20] Ainslie et al. [“ETC: Encoding Long and Structured Inputs in Transformers.”](https://aclanthology.org/2020.emnlp-main.19/) EMNLP 2019.
[21] Beltagy et al. [“Longformer: The long-document transformer.”](https://arxiv.org/abs/2004/05150) 2020.
[22] Zaheer et al. [“Big Bird: Transformers for Longer Sequences.”](https://arxiv.org/abs/2007.14062) 2020.
[23] Wang et al. [“Linformer: Self-Attention with Linear Complexity.”](https://arxiv.org/abs/2006.04768) arXiv preprint arXiv:2006.04768 (2020).
[24] Tay et al. 2020 [“Sparse Sinkhorn Attention.”](https://arxiv.org/abs/2002.11296) ICML 2020.
[25] Peng et al. [“Random Feature Attention.”](https://arxiv.org/abs/2103.02143) ICLR 2021.
[26] Choromanski et al. [“Rethinking Attention with Performers.”](https://arxiv.org/abs/2009.14794) ICLR 2021.
[27] Khandelwal et al. [“Generalization through memorization: Nearest neighbor language models.”](https://arxiv.org/abs/1911.00172) ICLR 2020.
[28] Yogatama et al. [“Adaptive semiparametric language models.”](https://arxiv.org/abs/2102.02557) ACL 2021.
[29] Wu et al. [“Memorizing Transformers.”](https://arxiv.org/abs/2203.08913) ICLR 2022.
[30] Su et al. [“Roformer: Enhanced transformer with rotary position embedding.”](https://arxiv.org/abs/2104.09864) arXiv preprint arXiv:2104.09864 (2021).
[31] Shaw et al. [“Self-attention with relative position representations.”](https://arxiv.org/abs/1803.02155) arXiv preprint arXiv:1803.02155 (2018).
[32] Tay et al. [“Efficient Transformers: A Survey."](https://arxiv.org/abs/2009.06732) ACM Computing Surveys 55.6 (2022): 1-28.
[33] Chen et al., [“Decision Transformer: Reinforcement Learning via Sequence Modeling”](https://arxiv.org/abs/2106.01345) arXiv preprint arXiv:2106.01345 (2021).